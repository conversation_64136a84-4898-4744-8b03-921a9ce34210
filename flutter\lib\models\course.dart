class Course {
  final String id;
  final String title;
  final String subtitle;
  final String content;
  final int number;
  final bool isCompleted;

  Course({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.content,
    required this.number,
    this.isCompleted = false,
  });

  // Mock data factory
  static List<Course> getMockCourses() {
    return [
      Course(
        id: '1',
        title: 'What Are Cryptocurrencies',
        subtitle: 'From Bitcoin to Altcoins',
        number: 1,
        content: '''
# What Are Cryptocurrencies: From Bitcoin to Altcoins

## Introduction to Cryptocurrencies

Cryptocurrencies are digital or virtual currencies that use cryptography for security and operate on decentralized networks based on blockchain technology. Unlike traditional currencies issued by governments (fiat currencies), cryptocurrencies are typically not subject to central authority control, making them theoretically immune to government interference or manipulation.

## Bitcoin: The First Cryptocurrency

Bitcoin, created in 2009 by an unknown person or group using the pseudonym <PERSON><PERSON>, was the first cryptocurrency. It introduced several key concepts:

- **Decentralization**: No central authority controls Bitcoin
- **Limited Supply**: Only 21 million Bitcoins will ever exist
- **Blockchain Technology**: A public ledger that records all transactions
- **Mining**: The process by which new Bitcoins are created and transactions are verified

Bitcoin was designed as a peer-to-peer electronic cash system that would eliminate the need for trusted third parties, such as banks, in financial transactions.

## Beyond Bitcoin: The Rise of Altcoins

Following Bitcoin's success, thousands of alternative cryptocurrencies, known as "altcoins," have been created. Some notable examples include:

### Ethereum (ETH)
Created in 2015, Ethereum expanded on Bitcoin's blockchain concept by adding smart contract functionality, allowing developers to build decentralized applications (dApps) on its platform.

### Ripple (XRP)
Designed for cross-border payments and remittances, Ripple aims to improve the efficiency of international money transfers.

### Litecoin (LTC)
Created in 2011 as a "lighter" version of Bitcoin, Litecoin offers faster transaction confirmation times and a different hashing algorithm.

### Cardano (ADA)
Developed using academic research and peer-reviewed methods, Cardano aims to provide a more secure and sustainable blockchain platform for smart contracts.

## Types of Cryptocurrencies

Cryptocurrencies can be categorized based on their purpose and technology:

1. **Payment Cryptocurrencies**: Designed primarily as digital money (Bitcoin, Litecoin)
2. **Platform Cryptocurrencies**: Support the development of applications (Ethereum, Solana)
3. **Utility Tokens**: Provide access to a specific product or service (Basic Attention Token)
4. **Security Tokens**: Represent ownership in an asset (real estate, company shares)
5. **Stablecoins**: Pegged to stable assets like the US dollar (USDC, Tether)
6. **Privacy Coins**: Focus on enhancing transaction privacy (Monero, Zcash)

## How Cryptocurrencies Work

Despite their differences, most cryptocurrencies share some common elements:

- **Blockchain Technology**: A distributed ledger that records all transactions
- **Cryptographic Security**: Uses advanced mathematics to secure transactions
- **Consensus Mechanisms**: Methods for validating transactions (Proof of Work, Proof of Stake)
- **Wallets**: Software that stores private keys needed to access and transfer cryptocurrencies

## The Future of Cryptocurrencies

As the cryptocurrency ecosystem continues to evolve, several trends are emerging:

- **Central Bank Digital Currencies (CBDCs)**: Government-issued digital currencies
- **DeFi (Decentralized Finance)**: Financial services without traditional intermediaries
- **NFTs (Non-Fungible Tokens)**: Unique digital assets representing ownership of items
- **Web3**: A vision of the internet built on decentralized protocols

Understanding the basics of cryptocurrencies is the first step toward navigating this complex and rapidly changing landscape.
''',
      ),
      Course(
        id: '2',
        title: 'How Blockchain Works',
        subtitle: 'Basics of Decentralization',
        number: 2,
        content: '''
# How Blockchain Works: Basics of Decentralization

## What is Blockchain Technology?

Blockchain is a distributed database or ledger shared among computer network nodes. It stores information electronically in digital format, maintaining a secure and decentralized record of transactions. The innovation of blockchain is that it guarantees the fidelity and security of data records without needing a trusted third party.

## Key Components of Blockchain

### 1. Blocks

The "block" in blockchain refers to a collection of data. Each block contains:

- **Transactions**: The actual data being stored (e.g., cryptocurrency transfers)
- **Timestamp**: When the block was created
- **Hash**: A unique identifier created by running the block's contents through a cryptographic algorithm
- **Previous Block's Hash**: Creates the "chain" by linking to the previous block

### 2. Decentralization

Unlike traditional databases managed by a central authority, blockchain operates on a peer-to-peer network where:

- Multiple copies of the blockchain exist across the network
- No single entity has control over the entire blockchain
- Changes require consensus from network participants

### 3. Consensus Mechanisms

For a new block to be added to the blockchain, network participants must agree on its validity. Common consensus mechanisms include:

#### Proof of Work (PoW)
- Used by Bitcoin and some other cryptocurrencies
- Requires solving complex mathematical puzzles
- Energy-intensive but highly secure

#### Proof of Stake (PoS)
- Validators are selected based on the number of coins they hold and "stake"
- More energy-efficient than PoW
- Used by Ethereum 2.0, Cardano, and others

#### Other Mechanisms
- Delegated Proof of Stake (DPoS)
- Proof of Authority (PoA)
- Practical Byzantine Fault Tolerance (PBFT)

## How Transactions Work on a Blockchain

1. **Transaction Initiation**: A user initiates a transaction (e.g., sending cryptocurrency)
2. **Transaction Broadcast**: The transaction is broadcast to the peer-to-peer network
3. **Validation**: Network nodes validate the transaction using known algorithms
4. **Verification**: Verified transactions are combined with others to create a new block
5. **Block Addition**: The new block is added to the existing blockchain
6. **Transaction Completion**: The transaction is now permanent and transparent

## Types of Blockchains

### Public Blockchains
- Open to anyone
- Fully decentralized
- Examples: Bitcoin, Ethereum

### Private Blockchains
- Restricted access
- Controlled by an organization
- Examples: Hyperledger Fabric, R3 Corda

### Consortium Blockchains
- Partially decentralized
- Controlled by a group of organizations
- Examples: Energy Web Foundation, Quorum

## Beyond Cryptocurrency: Blockchain Applications

While blockchain technology gained popularity through cryptocurrencies, its applications extend far beyond:

- **Supply Chain Management**: Tracking products from manufacturer to consumer
- **Healthcare**: Secure sharing of patient records
- **Voting Systems**: Transparent and tamper-proof elections
- **Identity Verification**: Self-sovereign identity management
- **Smart Contracts**: Self-executing contracts with terms written in code

## The Power of Decentralization

The fundamental innovation of blockchain is decentralization, which offers several advantages:

- **Transparency**: All transactions are visible to network participants
- **Immutability**: Once recorded, data cannot be altered
- **Security**: Distributed nature makes it difficult to hack
- **Removal of Intermediaries**: Direct peer-to-peer transactions
- **Censorship Resistance**: No single point of failure or control

Understanding blockchain technology is essential for grasping how cryptocurrencies function and appreciating the broader potential of decentralized systems.
''',
      ),
      Course(
        id: '3',
        title: 'Wallets and Security',
        subtitle: 'How to Store Cryptocurrency',
        number: 3,
        content: 'Detailed content about cryptocurrency wallets and security best practices...',
      ),
      Course(
        id: '4',
        title: 'Basics of Crypto Exchanges',
        subtitle: 'Buying, Selling, Trading',
        number: 4,
        content: 'Comprehensive guide to cryptocurrency exchanges, trading pairs, order types, and more...',
      ),
    ];
  }
}
