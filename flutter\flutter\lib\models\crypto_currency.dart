class CryptoCurrency {
  final String id;
  final String name;
  final String symbol;
  final double price;
  final double priceChangePercentage24h;
  final double marketCap;
  final double volume24h;
  final String imageUrl;
  final bool isFavorite;
  final List<PricePoint> priceHistory;

  CryptoCurrency({
    required this.id,
    required this.name,
    required this.symbol,
    required this.price,
    required this.priceChangePercentage24h,
    required this.marketCap,
    required this.volume24h,
    required this.imageUrl,
    this.isFavorite = false,
    required this.priceHistory,
  });

  // Mock data factory
  static List<CryptoCurrency> getMockItems() {
    return [
      CryptoCurrency(
        id: 'bitcoin',
        name: 'Bitcoin',
        symbol: 'BTC',
        price: 60123.45,
        priceChangePercentage24h: 2.34,
        marketCap: 1150000000000,
        volume24h: 32000000000,
        imageUrl: 'https://via.placeholder.com/50?text=BTC',
        isFavorite: true,
        priceHistory: _generateMockPriceHistory(60000, 3),
      ),
      CryptoCurrency(
        id: 'ethereum',
        name: 'Ethereum',
        symbol: 'ETH',
        price: 3245.67,
        priceChangePercentage24h: -1.23,
        marketCap: 390000000000,
        volume24h: 18000000000,
        imageUrl: 'https://via.placeholder.com/50?text=ETH',
        priceHistory: _generateMockPriceHistory(3200, 5),
      ),
      CryptoCurrency(
        id: 'binancecoin',
        name: 'Binance Coin',
        symbol: 'BNB',
        price: 456.78,
        priceChangePercentage24h: 0.45,
        marketCap: 75000000000,
        volume24h: 2000000000,
        imageUrl: 'https://via.placeholder.com/50?text=BNB',
        priceHistory: _generateMockPriceHistory(450, 2),
      ),
      CryptoCurrency(
        id: 'solana',
        name: 'Solana',
        symbol: 'SOL',
        price: 123.45,
        priceChangePercentage24h: 5.67,
        marketCap: 45000000000,
        volume24h: 3000000000,
        imageUrl: 'https://via.placeholder.com/50?text=SOL',
        isFavorite: true,
        priceHistory: _generateMockPriceHistory(120, 8),
      ),
      CryptoCurrency(
        id: 'cardano',
        name: 'Cardano',
        symbol: 'ADA',
        price: 1.23,
        priceChangePercentage24h: -0.45,
        marketCap: 40000000000,
        volume24h: 1500000000,
        imageUrl: 'https://via.placeholder.com/50?text=ADA',
        priceHistory: _generateMockPriceHistory(1.2, 1),
      ),
    ];
  }

  static List<PricePoint> _generateMockPriceHistory(double basePrice, double volatility) {
    final random = DateTime.now().millisecondsSinceEpoch;
    final List<PricePoint> pricePoints = [];
    
    // Generate price points for different time intervals
    // Last hour (12 points, 5 minutes each)
    DateTime time = DateTime.now().subtract(const Duration(hours: 1));
    for (int i = 0; i < 12; i++) {
      final randomFactor = (((random + i) % 100) / 100 - 0.5) * volatility / 10;
      final price = basePrice * (1 + randomFactor);
      pricePoints.add(PricePoint(time, price));
      time = time.add(const Duration(minutes: 5));
    }
    
    // Last day (24 points, 1 hour each)
    time = DateTime.now().subtract(const Duration(days: 1));
    for (int i = 0; i < 24; i++) {
      final randomFactor = (((random + i * 3) % 100) / 100 - 0.5) * volatility / 5;
      final price = basePrice * (1 + randomFactor);
      pricePoints.add(PricePoint(time, price));
      time = time.add(const Duration(hours: 1));
    }
    
    // Last week (7 points, 1 day each)
    time = DateTime.now().subtract(const Duration(days: 7));
    for (int i = 0; i < 7; i++) {
      final randomFactor = (((random + i * 7) % 100) / 100 - 0.5) * volatility / 2;
      final price = basePrice * (1 + randomFactor);
      pricePoints.add(PricePoint(time, price));
      time = time.add(const Duration(days: 1));
    }
    
    // Last month (30 points, 1 day each)
    time = DateTime.now().subtract(const Duration(days: 30));
    for (int i = 0; i < 30; i++) {
      final randomFactor = (((random + i * 11) % 100) / 100 - 0.5) * volatility;
      final price = basePrice * (1 + randomFactor);
      pricePoints.add(PricePoint(time, price));
      time = time.add(const Duration(days: 1));
    }
    
    return pricePoints;
  }
}

class PricePoint {
  final DateTime time;
  final double price;

  PricePoint(this.time, this.price);
}
