import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/historical_entry.dart';

// Модель для хранения рыночных данных
class MarketSentimentData {
  final DateTime timestamp;
  final double overallScore;
  final Map<String, dynamic> detailedMetrics;

  MarketSentimentData({
    required this.timestamp,
    required this.overallScore,
    required this.detailedMetrics,
  });

  // Преобразование в JSON для сохранения
  Map<String, dynamic> toJson() => {
    'timestamp': timestamp.toIso8601String(),
    'overallScore': overallScore,
    'detailedMetrics': detailedMetrics,
  };

  // Создание из JSON
  factory MarketSentimentData.fromJson(Map<String, dynamic> json) {
    return MarketSentimentData(
      timestamp: DateTime.parse(json['timestamp']),
      overallScore: json['overallScore'],
      detailedMetrics: Map<String, dynamic>.from(json['detailedMetrics']),
    );
  }
}

// Сервис для получения рыночных данных
class MarketSentimentDataService {
  // Веса для различных метрик
  static const Map<String, double> _metricWeights = {
    'Fear & Greed Index': 0.3,
    'News Sentiment': 0.15,
    'Holders Score': 0.15,
    'Volume Score': 0.2,
    'Social Engagement': 0.1,
    'Price Volatility': 0.1,
    'Bitcoin Dominance': 0.0, // Не используется в общем расчете
  };

  // Получение данных с различных источников
  Future<MarketSentimentData> fetchMarketSentiment() async {
    final random = Random();

    try {
      // Симуляция получения данных с разных источников
      final metrics = {
        'Fear & Greed Index': await _fetchFearGreedIndex(),
        'News Sentiment': await _fetchNewsSentiment(),
        'Holders Score': await _fetchHoldersScore(),
        'Volume Score': await _fetchVolumeScore(),
        'Social Engagement': await _fetchSocialEngagement(),
        'Price Volatility': await _fetchPriceVolatility(),
        'Bitcoin Dominance': await _fetchBitcoinDominance(),
      };

      // Расчет общего индекса
      double overallScore = _calculateOverallScore(metrics);

      // Создание объекта с данными
      final sentimentData = MarketSentimentData(
        timestamp: DateTime.now(),
        overallScore: overallScore,
        detailedMetrics: metrics,
      );

      // Сохранение данных
      _cacheMarketData(sentimentData);

      // Сохраняем в историю
      _saveToHistory(overallScore, metrics);

      return sentimentData;
    } catch (e) {
      debugPrint('Ошибка при получении данных: $e');

      // Возврат кэшированных данных или генерация временных
      return await _retrieveCachedOrGenerateData();
    }
  }

  // Расчет общего индекса рыночного сентимента
  Future<double> calculateMarketSentiment() async {
    try {
      final data = await fetchMarketSentiment();
      return data.overallScore;
    } catch (e) {
      debugPrint('Ошибка при расчете рыночного сентимента: $e');
      return 50.0; // Нейтральное значение по умолчанию
    }
  }

  // Получение исторических данных
  Future<List<HistoricalEntry>> getHistoricalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('sentimentHistory');

      if (historyJson != null) {
        final List<dynamic> historyList = jsonDecode(historyJson);
        return historyList.map((entry) => HistoricalEntry.fromJson(entry)).toList();
      }
    } catch (e) {
      debugPrint('Ошибка при получении исторических данных: $e');
    }

    return [];
  }

  // Сохранение данных в историю
  void _saveToHistory(double value, Map<String, dynamic> metrics) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('sentimentHistory');

      List<HistoricalEntry> history = [];

      if (historyJson != null) {
        final List<dynamic> historyList = jsonDecode(historyJson);
        history = historyList.map((entry) => HistoricalEntry.fromJson(entry)).toList();
      }

      // Добавляем новую запись
      history.add(HistoricalEntry(
        DateTime.now(),
        value,
        metrics,
      ));

      // Ограничиваем историю последними 30 записями
      if (history.length > 30) {
        history = history.sublist(history.length - 30);
      }

      // Сохраняем обновленную историю
      await prefs.setString('sentimentHistory', jsonEncode(history.map((e) => e.toJson()).toList()));
    } catch (e) {
      debugPrint('Ошибка при сохранении в историю: $e');
    }
  }

  // Кэширование данных
  void _cacheMarketData(MarketSentimentData data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('marketSentimentData', jsonEncode(data.toJson()));
  }

  // Извлечение кэшированных данных или генерация временных
  Future<MarketSentimentData> _retrieveCachedOrGenerateData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedDataString = prefs.getString('marketSentimentData');

      if (cachedDataString != null) {
        return MarketSentimentData.fromJson(jsonDecode(cachedDataString));
      }
    } catch (e) {
      debugPrint('Ошибка при извлечении кэшированных данных: $e');
    }

    // Генерация временных данных
    return _generateTemporaryData();
  }

  // Генерация временных данных
  MarketSentimentData _generateTemporaryData() {
    final random = Random();
    final metrics = {
      'Fear & Greed Index': 50.0 + random.nextDouble() * 20 - 10,
      'News Sentiment': 50.0 + random.nextDouble() * 20 - 10,
      'Holders Score': 50.0 + random.nextDouble() * 20 - 10,
      'Volume Score': 50.0 + random.nextDouble() * 20 - 10,
      'Social Engagement': 50.0 + random.nextDouble() * 20 - 10,
      'Price Volatility': 50.0 + random.nextDouble() * 20 - 10,
      'Bitcoin Dominance': 50.0 + random.nextDouble() * 20 - 10,
    };

    return MarketSentimentData(
      timestamp: DateTime.now(),
      overallScore: _calculateOverallScore(metrics),
      detailedMetrics: metrics,
    );
  }

  // Расчет общего индекса
  double _calculateOverallScore(Map<String, dynamic> metrics) {
    double score = 0;
    double weightSum = 0;

    _metricWeights.forEach((key, weight) {
      if (metrics.containsKey(key) && weight > 0) {
        score += (metrics[key] ?? 50.0) * weight;
        weightSum += weight;
      }
    });

    // Нормализация по сумме весов
    if (weightSum > 0) {
      score = score / weightSum;
    }

    return score.clamp(0.0, 100.0);
  }

  // Получение Fear & Greed Index
  Future<double> _fetchFearGreedIndex() async {
    try {
      final response = await http.get(
        Uri.parse('https://api.alternative.me/fng/?limit=1'),
        headers: {'User-Agent': 'MarketSentimentApp/1.0'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['data'] != null && data['data'].isNotEmpty) {
          return double.parse(data['data'][0]['value']);
        }
      }
      return 50.0;
    } catch (e) {
      debugPrint('Ошибка получения Fear & Greed Index: $e');
      return 50.0;
    }
  }

  // Получение новостного сентимента
  Future<double> _fetchNewsSentiment() async {
    try {
      final response = await http.get(
        Uri.parse('https://cryptopanic.com/api/v1/posts/?auth_token=38f4c3b9e89ed5c0fda9211409cd20a05a19b079&currencies=BTC,ETH'),
        headers: {'User-Agent': 'MarketSentimentApp/1.0'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        int positive = 0;
        int negative = 0;

        if (data['results'] != null) {
          for (var post in data['results']) {
            if (post['votes'] != null) {
              positive += (post['votes']['positive'] as num?)?.toInt() ?? 0;
              negative += (post['votes']['negative'] as num?)?.toInt() ?? 0;
            }
          }

          if (positive + negative > 0) {
            return (positive / (positive + negative) * 100).clamp(0.0, 100.0);
          }
        }
      }
      return 50.0;
    } catch (e) {
      debugPrint('Ошибка получения новостного сентимента: $e');
      return 50.0;
    }
  }

  // Получение оценки холдеров
  Future<double> _fetchHoldersScore() async {
    try {
      final response = await http.get(
        Uri.parse('https://api.blockchain.info/charts/n-unique-addresses?timespan=7days&format=json'),
        headers: {'User-Agent': 'MarketSentimentApp/1.0'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['values'] != null && data['values'].isNotEmpty) {
          final values = List<double>.from(data['values'].map((e) => e['y'] as double));
          final avgValue = values.reduce((a, b) => a + b) / values.length;
          final lastValue = values.last;

          // Нормализуем значение относительно среднего
          return ((lastValue / avgValue) * 50 + 50).clamp(0.0, 100.0);
        }
      }
      return 50.0;
    } catch (e) {
      debugPrint('Ошибка получения оценки холдеров: $e');
      return 50.0;
    }
  }

  // Получение оценки объема торгов
  Future<double> _fetchVolumeScore() async {
    try {
      final response = await http.get(
        Uri.parse('https://api.coingecko.com/api/v3/coins/bitcoin/market_chart?vs_currency=usd&days=30'),
        headers: {'User-Agent': 'MarketSentimentApp/1.0'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['total_volumes'] != null) {
          final volumes = List<double>.from(data['total_volumes'].map((e) => e[1] as double));
          final avgVolume = volumes.reduce((a, b) => a + b) / volumes.length;
          final lastVolume = volumes.last;

          return ((lastVolume / avgVolume) * 50 + 50).clamp(0.0, 100.0);
        }
      }
      return 50.0;
    } catch (e) {
      debugPrint('Ошибка получения оценки объема торгов: $e');
      return 50.0;
    }
  }

  // Получение социальной активности
  Future<double> _fetchSocialEngagement() async {
    try {
      final response = await http.get(
        Uri.parse('https://api.coingecko.com/api/v3/coins/bitcoin'),
        headers: {'User-Agent': 'MarketSentimentApp/1.0'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['community_data'] != null) {
          final twitterFollowers = data['community_data']['twitter_followers'] as int? ?? 0;
          final redditSubscribers = data['community_data']['reddit_subscribers'] as int? ?? 0;

          // Нормализуем значение (максимум примерно 5 миллионов)
          return ((twitterFollowers + redditSubscribers) / 5000000 * 100).clamp(0.0, 100.0);
        }
      }
      return 50.0;
    } catch (e) {
      debugPrint('Ошибка получения социальной активности: $e');
      return 50.0;
    }
  }

  // Получение волатильности цены
  Future<double> _fetchPriceVolatility() async {
    try {
      final response = await http.get(
        Uri.parse('https://api.coingecko.com/api/v3/coins/bitcoin/market_chart?vs_currency=usd&days=30'),
        headers: {'User-Agent': 'MarketSentimentApp/1.0'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['prices'] != null) {
          final prices = List<double>.from(data['prices'].map((e) => e[1] as double));
          final mean = prices.reduce((a, b) => a + b) / prices.length;
          final variance = prices.map((x) => pow(x - mean, 2)).reduce((a, b) => a + b) / prices.length;
          final volatility = sqrt(variance) / mean;

          // Нормализуем значение (максимальная волатильность примерно 0.2 или 20%)
          return (volatility / 0.2 * 100).clamp(0.0, 100.0);
        }
      }
      return 50.0;
    } catch (e) {
      debugPrint('Ошибка получения волатильности цены: $e');
      return 50.0;
    }
  }

  // Получение доминирования Bitcoin
  Future<double> _fetchBitcoinDominance() async {
    try {
      final response = await http.get(
        Uri.parse('https://api.coingecko.com/api/v3/global'),
        headers: {'User-Agent': 'MarketSentimentApp/1.0'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['data'] != null && data['data']['market_cap_percentage'] != null) {
          return data['data']['market_cap_percentage']['btc'] as double;
        }
      }
      return 50.0;
    } catch (e) {
      debugPrint('Ошибка получения доминирования Bitcoin: $e');
      return 50.0;
    }
  }
}
