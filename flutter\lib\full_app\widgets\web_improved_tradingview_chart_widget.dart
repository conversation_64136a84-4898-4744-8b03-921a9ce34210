import 'dart:convert';
import 'dart:html' as html;
import 'dart:ui_web' as ui_web;
import 'package:flutter/material.dart';
import '../models/candle.dart';

/// Улучшенный виджет для отображения графика TradingView на веб-платформе
class WebImprovedTradingViewChartWidget extends StatefulWidget {
  final List<Candle> allCandles; // Все свечи
  final Function(double price, int time)? onEntryPointSet;
  final Function(bool isUp, double percentChange, double finalPrice)? onTradeResult;

  const WebImprovedTradingViewChartWidget({
    super.key,
    required this.allCandles,
    this.onEntryPointSet,
    this.onTradeResult,
  });

  @override
  State<WebImprovedTradingViewChartWidget> createState() => WebImprovedTradingViewChartWidgetState();
}

class WebImprovedTradingViewChartWidgetState extends State<WebImprovedTradingViewChartWidget> {
  final String viewType = 'tradingview-chart-${DateTime.now().millisecondsSinceEpoch}';
  late html.IFrameElement _iframeElement;
  bool _isLoaded = false;
  String _htmlContent = '';
  bool _areAllCandlesShown = false;
  List<Candle> _currentCandles = [];

  @override
  void initState() {
    super.initState();
    _loadHtmlContent();
  }

  @override
  void didUpdateWidget(WebImprovedTradingViewChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Если изменились свечи, загружаем их заново
    if (widget.allCandles != oldWidget.allCandles) {
      _loadAllCandles();
    }
  }

  // Загрузка HTML-контента
  Future<void> _loadHtmlContent() async {
    try {
      _htmlContent = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improved TradingView Chart</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #131722;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <script src="https://unpkg.com/lightweight-charts@4.1.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Базовая реализация графика
        let chart;
        let candleSeries;
        let allCandles = [];
        
        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });
        
        // Инициализация графика
        function initChart() {
            const container = document.getElementById('chart-container');
            
            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: container.clientHeight,
                layout: {
                    background: { color: '#131722' },
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    borderColor: '#2a2e39',
                },
                rightPriceScale: {
                    borderColor: '#2a2e39',
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                handleScroll: true,
                handleScale: true,
            });
            
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });
            
            // Обработка сообщений от Flutter
            window.addEventListener('message', function(event) {
                const message = JSON.parse(event.data);
                
                switch (message.action) {
                    case 'loadCandles':
                        loadCandles(message.data);
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                }
            });
            
            // Уведомляем Flutter, что график инициализирован
            window.parent.postMessage(JSON.stringify({
                type: 'chartInitialized',
                data: {}
            }), '*');
        }
        
        // Загрузка свечей
        function loadCandles(candles) {
            allCandles = candles;
            candleSeries.setData(candles);
            chart.timeScale().fitContent();
        }
        
        // Показать все свечи
        function showAllCandles() {
            // Базовая реализация
            window.parent.postMessage(JSON.stringify({
                type: 'allCandlesShown',
                data: {}
            }), '*');
        }
        
        // Установка точки входа
        function setEntryPoint() {
            // Базовая реализация
            const lastCandle = allCandles[allCandles.length - 1];
            
            window.parent.postMessage(JSON.stringify({
                type: 'entryPointSet',
                data: {
                    price: lastCandle.close,
                    time: lastCandle.time
                }
            }), '*');
        }
    </script>
</body>
</html>
''';

      // Регистрируем обработчик сообщений
      html.window.onMessage.listen(_handleMessage);

      // Создаем iframe
      _iframeElement = html.IFrameElement()
        ..style.border = 'none'
        ..style.height = '100%'
        ..style.width = '100%'
        ..style.overflow = 'hidden'
        ..style.backgroundColor = '#131722'
        ..srcdoc = _htmlContent;

      // Регистрируем view factory
      ui_web.platformViewRegistry.registerViewFactory(
        viewType,
        (int viewId) => _iframeElement,
      );

      setState(() {
        _isLoaded = true;
      });
    } catch (e) {
      debugPrint('Error loading HTML content: $e');
    }
  }

  // Обработка сообщений от JavaScript
  void _handleMessage(html.MessageEvent event) {
    try {
      final Map<String, dynamic> data = jsonDecode(event.data);
      final String type = data['type'];
      final Map<String, dynamic> payload = data['data'] ?? {};

      switch (type) {
        case 'chartInitialized':
          _loadAllCandles();
          break;
        case 'entryPointSet':
          if (widget.onEntryPointSet != null) {
            widget.onEntryPointSet!(
              payload['price'],
              payload['time'],
            );
          }
          break;
        case 'allCandlesShown':
          setState(() {
            _areAllCandlesShown = true;
          });
          break;
        case 'tradeResult':
          if (widget.onTradeResult != null) {
            widget.onTradeResult!(
              payload['isUp'],
              payload['percentChange'],
              payload['finalPrice'],
            );
          }
          break;
      }
    } catch (e) {
      debugPrint('Error handling message: $e');
    }
  }

  // Загрузка всех свечей
  void _loadAllCandles() {
    if (!_isLoaded || widget.allCandles.isEmpty) return;

    try {
      final List<Map<String, dynamic>> candlesData = widget.allCandles.map((candle) => {
        'time': candle.time,
        'open': candle.open,
        'high': candle.high,
        'low': candle.low,
        'close': candle.close,
        'volume': candle.volume,
      }).toList();

      final message = jsonEncode({
        'action': 'loadCandles',
        'data': candlesData,
      });

      _iframeElement.contentWindow?.postMessage(message, '*');
    } catch (e) {
      debugPrint('Error loading candles: $e');
    }
  }

  // Показать все свечи
  void showAllCandles() {
    if (!_isLoaded) return;

    try {
      final message = jsonEncode({
        'action': 'showAllCandles',
        'data': {},
      });

      _iframeElement.contentWindow?.postMessage(message, '*');
    } catch (e) {
      debugPrint('Error showing all candles: $e');
    }
  }

  // Показать только начальные свечи
  void showInitialCandles() {
    if (!_isLoaded) return;

    try {
      final message = jsonEncode({
        'action': 'showInitialCandles',
        'data': {},
      });

      _iframeElement.contentWindow?.postMessage(message, '*');
    } catch (e) {
      debugPrint('Error showing initial candles: $e');
    }
  }

  // Установить точку входа
  void setEntryPoint() {
    if (!_isLoaded) return;

    try {
      final message = jsonEncode({
        'action': 'setEntryPoint',
        'data': {},
      });

      _iframeElement.contentWindow?.postMessage(message, '*');
    } catch (e) {
      debugPrint('Error setting entry point: $e');
    }
  }

  // Сохранить состояние графика
  void saveChartState() {
    // Базовая реализация
  }

  // Восстановить состояние графика
  void restoreChartState() {
    // Базовая реализация
  }

  // Очистить элементы графика
  void clearChartElements() {
    // Базовая реализация
  }

  @override
  Widget build(BuildContext context) {
    if (!_isLoaded) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    return HtmlElementView(viewType: viewType);
  }
}
