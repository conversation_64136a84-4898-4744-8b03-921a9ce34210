import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class CryptoLogoService {
  // Базовый URL для логотипов криптовалют (CoinGecko)
  static const String _coinGeckoBaseUrl = 'https://assets.coingecko.com/coins/images/';

  // Альтернативные базовые URL для логотипов
  static const String _kucoinBaseUrl = 'https://assets.kucoin.com/www/coin/pc/';
  static const String _binanceBaseUrl = 'https://bin.bnbstatic.com/image/admin_mgs_image_upload/20201110/';

  // Маппинг ID криптовалют для CoinGecko
  static const Map<String, String> _coinGeckoIds = {
    'BTC': '1/large/bitcoin.png',
    'ETH': '279/large/ethereum.png',
    'BNB': '825/large/bnb-icon2_2x.png',
    'SOL': '4128/large/solana.png',
    'ADA': '2010/large/cardano.png',
    'XRP': '44/large/xrp-symbol-white-128.png',
    'DOT': '12171/large/polkadot.png',
    'DOGE': '5/large/dogecoin.png',
    'AVAX': '12559/large/avalanche-avax-logo.png',
    'SHIB': '11939/large/shiba.png',
    'MATIC': '4713/large/matic-token.png',
    'LTC': '2/large/litecoin.png',
    'LINK': '877/large/chainlink.png',
    'UNI': '12504/large/uniswap.png',
    'ATOM': '1481/large/cosmos_hub.png',
    'XLM': '100/large/stellar.png',
    'ALGO': '4030/large/algorand.png',
    'FIL': '12817/large/filecoin.png',
    'NEAR': '10365/large/near.png',
    'ICP': '14495/large/icp.png',
    'VET': '1167/large/VeChain-Logo-768x725.png',
    'MANA': '878/large/decentraland.png',
    'SAND': '12129/large/sandbox_logo.jpg',
    'AXS': '13029/large/axie_infinity_logo.png',
    'GALA': '12493/large/GALA-COINGECKO.png',
    'ENJ': '1102/large/enjin-coin-logo.png',
    'CHZ': '4066/large/chiliz.png',
    'ONE': '3945/large/harmony.png',
    'HOT': '2682/large/holo.png',
    'ZIL': '2687/large/zilliqa-logo.png',
    'NEO': '480/large/neo.jpg',
    'BCC': '1831/large/bitcoin-cash-circle.png',
    'QTUM': '1714/large/qtum.png',
    'EOS': '738/large/eos-eos-logo.png',
    'TUSD': '3449/large/tusd.png',
    'IOTA': '1720/large/iota.png',
    'ONT': '2566/large/ont.jpg',
    'TRX': '1094/large/tron.png',
  };

  // Кэш для хранения URL логотипов
  final Map<String, String> _logoUrlCache = {};

  // Ключ для SharedPreferences
  static const String _cacheKey = 'crypto_logos_cache';

  // Конструктор, который загружает кэш из SharedPreferences
  CryptoLogoService() {
    _loadCache();
  }

  // Загрузка кэша из SharedPreferences
  Future<void> _loadCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_cacheKey);

      if (cachedData != null) {
        final Map<String, dynamic> decoded = jsonDecode(cachedData);
        decoded.forEach((key, value) {
          _logoUrlCache[key] = value.toString();
        });
        print('Loaded ${_logoUrlCache.length} logo URLs from cache');
      }
    } catch (e) {
      print('Error loading logo cache: $e');
    }
  }

  // Сохранение кэша в SharedPreferences
  Future<void> _saveCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_cacheKey, jsonEncode(_logoUrlCache));
      print('Saved ${_logoUrlCache.length} logo URLs to cache');
    } catch (e) {
      print('Error saving logo cache: $e');
    }
  }

  // Получение URL логотипа для одной монеты
  String getLogoUrl(String symbol) {
    // Проверяем кэш
    if (_logoUrlCache.containsKey(symbol)) {
      return _logoUrlCache[symbol]!;
    }

    // Если в кэше нет, генерируем URL
    final logoUrl = _generateLogoUrl(symbol);

    // Сохраняем в кэш
    _logoUrlCache[symbol] = logoUrl;

    // Обновляем кэш в SharedPreferences
    _saveCache();

    return logoUrl;
  }

  // Получение URL логотипов для нескольких монет
  Map<String, String> getLogoUrls(List<String> symbols) {
    final Map<String, String> result = {};

    for (var symbol in symbols) {
      result[symbol] = getLogoUrl(symbol);
    }

    return result;
  }

  // Генерация URL логотипа на основе символа
  String _generateLogoUrl(String symbol) {
    // Приводим символ к верхнему регистру для соответствия ключам в маппинге
    final upperSymbol = symbol.trim().toUpperCase();

    // Проверяем, есть ли символ в маппинге CoinGecko
    if (_coinGeckoIds.containsKey(upperSymbol)) {
      // Используем URL из CoinGecko
      return '$_coinGeckoBaseUrl${_coinGeckoIds[upperSymbol]}';
    }

    // Пробуем использовать Binance в качестве запасного варианта
    final normalizedSymbol = symbol.trim().toLowerCase();

    // Используем несколько источников для повышения вероятности найти логотип
    final urls = [
      '$_kucoinBaseUrl$normalizedSymbol.png',
      'https://cryptologos.cc/logos/$normalizedSymbol-$upperSymbol-logo.png',
      'https://assets.coingecko.com/coins/images/1/large/$normalizedSymbol.png',
      'https://s2.coinmarketcap.com/static/img/coins/64x64/$normalizedSymbol.png',
      'https://raw.githubusercontent.com/spothq/cryptocurrency-icons/master/128/color/$normalizedSymbol.png',
    ];

    // Возвращаем первый URL из списка (в будущем можно реализовать проверку доступности)
    return urls[0];
  }
}
