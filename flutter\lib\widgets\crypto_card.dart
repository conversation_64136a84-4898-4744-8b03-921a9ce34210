import 'package:flutter/material.dart';
import '../models/crypto_coin.dart';
import '../utils/device_type.dart';
import 'sparkline_chart.dart';

class CryptoCard extends StatelessWidget {
  final CryptoCoin coin;
  final VoidCallback onTap;
  final bool isCompact;

  const CryptoCard({
    super.key,
    required this.coin,
    required this.onTap,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = DeviceUtils.isDesktop(context);

    if (isCompact) {
      return _buildCompactCard(context);
    }

    return isDesktop ? _buildDesktopCard(context) : _buildMobileCard(context);
  }

  Widget _buildCompactCard(BuildContext context) {
    return Card(
      color: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Symbol and name
              Row(
                children: [
                  _buildCoinLogo(size: 32),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        coin.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        coin.symbol,
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Price and change
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    coin.formattedPrice,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  Row(
                    children: [
                      Text(
                        coin.formattedPriceChange,
                        style: TextStyle(
                          color: coin.isPriceUp ? Colors.green : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      SparklineChart(
                        data: coin.sparklineData,
                        color: coin.isPriceUp ? Colors.green : Colors.red,
                        width: 80,
                        height: 30,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileCard(BuildContext context) {
    return Card(
      color: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Logo and symbol
              _buildCoinLogo(size: 40),
              const SizedBox(width: 16),

              // Name and symbol
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      coin.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      coin.symbol,
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),

              // Sparkline chart
              SizedBox(
                width: 80,
                height: 40,
                child: SparklineChart(
                  data: coin.sparklineData,
                  color: coin.isPriceUp ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(width: 16),

              // Price and change
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    coin.formattedPrice,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    coin.formattedPriceChange,
                    style: TextStyle(
                      color: coin.isPriceUp ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopCard(BuildContext context) {
    return Card(
      color: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Row(
            children: [
              // Logo and symbol
              _buildCoinLogo(size: 48),
              const SizedBox(width: 20),

              // Name and symbol
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      coin.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      coin.symbol,
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

              // Market cap and volume
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Market Cap: ${coin.formattedMarketCap}',
                      style: TextStyle(
                        color: Colors.grey[300],
                      ),
                    ),
                    Text(
                      'Volume: ${coin.formattedVolume}',
                      style: TextStyle(
                        color: Colors.grey[300],
                      ),
                    ),
                  ],
                ),
              ),

              // Sparkline chart
              Expanded(
                flex: 2,
                child: SizedBox(
                  height: 50,
                  child: SparklineChart(
                    data: coin.sparklineData,
                    color: coin.isPriceUp ? Colors.green : Colors.red,
                  ),
                ),
              ),

              // Price and change
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      coin.formattedPrice,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      coin.formattedPriceChange,
                      style: TextStyle(
                        color: coin.isPriceUp ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCoinLogo({required double size}) {
    // Проверяем, есть ли URL логотипа
    if (coin.imageUrl.isNotEmpty && !coin.imageUrl.contains('placeholder')) {
      return ClipOval(
        child: SizedBox(
          width: size,
          height: size,
          child: Image.network(
            coin.imageUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // В случае ошибки загрузки изображения показываем первую букву символа
              return _buildFallbackLogo(size);
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) {
                return child;
              }
              // Показываем индикатор загрузки
              return Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: SizedBox(
                    width: size * 0.5,
                    height: size * 0.5,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );
    } else {
      // Если URL логотипа отсутствует, показываем первую букву символа
      return _buildFallbackLogo(size);
    }
  }

  // Вспомогательный метод для отображения заглушки логотипа
  Widget _buildFallbackLogo(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey[800],
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          coin.symbol.substring(0, 1),
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: size * 0.5,
          ),
        ),
      ),
    );
  }
}
