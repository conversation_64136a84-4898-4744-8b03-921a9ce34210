<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improved TradingView Chart</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #131722;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
        }
        #result-popup {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(19, 23, 34, 0.9);
            border: 1px solid #2a2e39;
            border-radius: 4px;
            padding: 20px;
            color: white;
            display: none;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
            text-align: center;
            min-width: 200px;
        }
        .result-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .result-profit {
            font-size: 24px;
            font-weight: bold;
            margin-top: 10px;
        }
        .result-profit.positive {
            color: #26a69a;
        }
        .result-profit.negative {
            color: #ef5350;
        }
        #loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 16px;
            text-align: center;
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="loading-indicator">
        <div class="spinner"></div>
        <div>Loading chart...</div>
    </div>
    <div id="result-popup">
        <div class="result-title">Trade Result</div>
        <div id="result-status"></div>
        <div id="result-profit" class="result-profit"></div>
    </div>

    <script src="https://unpkg.com/lightweight-charts@4.1.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Глобальные переменные
        let chart;
        let candleSeries;
        let allCandles = [];
        let visibleCandlesCount = 400;
        let futureCandlesCount = 5;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;
        let verticalLine = null;
        let chartState = null;
        let animationInterval = null;
        let currentAnimationIndex = 0;

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing chart...');
            initChart();
        });

        // Обработчик сообщений от Flutter
        window.addEventListener('message', function(event) {
            try {
                const message = JSON.parse(event.data);
                console.log('Received message:', message.action);
                
                switch (message.action) {
                    case 'loadCandles':
                        loadCandles(message.data);
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'saveChartState':
                        saveChartState();
                        break;
                    case 'restoreChartState':
                        restoreChartState();
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });

        // Отправка сообщения в Flutter
        function sendMessageToFlutter(type, data) {
            if (window.FlutterChannel) {
                const message = JSON.stringify({
                    type: type,
                    data: data
                });
                window.FlutterChannel.postMessage(message);
            } else {
                console.error('FlutterChannel not available');
            }
        }

        // Инициализация графика
        function initChart() {
            console.log('Initializing chart...');
            const container = document.getElementById('chart-container');
            const loadingIndicator = document.getElementById('loading-indicator');

            if (!container) {
                console.error('Chart container not found!');
                return;
            }

            // Создаем график с улучшенными настройками
            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: container.clientHeight,
                layout: {
                    background: { color: '#131722' },
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    borderColor: '#2a2e39',
                    barSpacing: 6,
                    rightOffset: 5,
                    fixRightEdge: false,
                    fixLeftEdge: false,
                    rightBarStaysOnScroll: false,
                },
                rightPriceScale: {
                    borderColor: '#2a2e39',
                    scaleMargins: {
                        top: 0.1,
                        bottom: 0.1,
                    },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                handleScroll: true,
                handleScale: true,
            });

            // Создаем серию свечей
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });

            // Обработка изменения размера окна
            window.addEventListener('resize', function() {
                if (chart) {
                    chart.resize(
                        container.clientWidth,
                        container.clientHeight
                    );
                }
            });

            // Скрываем индикатор загрузки
            loadingIndicator.style.display = 'none';

            // Уведомляем Flutter, что график инициализирован
            sendMessageToFlutter('chartInitialized', {});
        }

        // Загрузка свечей
        function loadCandles(candles) {
            console.log('Loading candles, count:', candles.length);
            
            // Сохраняем все свечи
            allCandles = candles;
            
            // Показываем только начальные свечи
            showInitialCandles();
        }

        // Показать только начальные свечи
        function showInitialCandles() {
            if (!chart || !candleSeries || allCandles.length === 0) {
                console.error('Cannot show initial candles: chart or data not ready');
                return;
            }

            console.log('Showing initial candles');
            
            // Очищаем предыдущие элементы
            clearChartElements();
            
            // Показываем только видимые свечи
            const visibleCandles = allCandles.slice(0, visibleCandlesCount);
            candleSeries.setData(visibleCandles);
            
            // Центрируем последнюю свечу
            setTimeout(() => {
                chart.timeScale().fitContent();
                // Делаем несколько попыток прокрутки с увеличивающейся задержкой
                setTimeout(() => chart.timeScale().scrollToPosition(50, false), 0);
                setTimeout(() => chart.timeScale().scrollToPosition(50, false), 300);
                setTimeout(() => chart.timeScale().scrollToPosition(50, false), 600);
            }, 100);
            
            // Уведомляем Flutter
            sendMessageToFlutter('initialCandlesShown', {});
        }

        // Установка точки входа
        function setEntryPoint() {
            if (!chart || !candleSeries || allCandles.length === 0) {
                console.error('Cannot set entry point: chart or data not ready');
                return;
            }

            console.log('Setting entry point');
            
            // Получаем последнюю видимую свечу
            const lastVisibleCandle = allCandles[visibleCandlesCount - 1];
            entryPointPrice = lastVisibleCandle.close;
            entryPointTime = lastVisibleCandle.time;
            
            // Создаем горизонтальную линию на уровне цены входа
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
            }
            horizontalLine = candleSeries.createPriceLine({
                price: entryPointPrice,
                color: '#2196F3',
                lineWidth: 2,
                lineStyle: LightweightCharts.LineStyle.Dashed,
                axisLabelVisible: true,
                title: 'Entry',
            });
            
            // Создаем вертикальную линию в точке входа
            if (verticalLine) {
                // В версии 4.x нет прямого метода для удаления вертикальной линии
                // Поэтому мы просто создаем новую
            }
            
            // В версии 4.x нет прямого метода для создания вертикальной линии
            // Вместо этого мы можем использовать маркеры
            candleSeries.setMarkers([
                {
                    time: entryPointTime,
                    position: 'inBar',
                    color: 'rgba(255, 0, 0, 0.5)',
                    shape: 'circle',
                    size: 1,
                }
            ]);
            
            // Уведомляем Flutter о установке точки входа
            sendMessageToFlutter('entryPointSet', {
                price: entryPointPrice,
                time: entryPointTime
            });
        }

        // Показать все свечи, включая будущие
        function showAllCandles() {
            if (!chart || !candleSeries || allCandles.length === 0) {
                console.error('Cannot show all candles: chart or data not ready');
                return;
            }

            console.log('Showing all candles');
            
            // Получаем будущие свечи
            const futureCandles = allCandles.slice(visibleCandlesCount, visibleCandlesCount + futureCandlesCount);
            
            // Если нет будущих свечей, выходим
            if (futureCandles.length === 0) {
                console.warn('No future candles available');
                return;
            }
            
            // Останавливаем предыдущую анимацию, если она запущена
            if (animationInterval) {
                clearInterval(animationInterval);
                animationInterval = null;
            }
            
            // Сбрасываем индекс анимации
            currentAnimationIndex = 0;
            
            // Запускаем анимацию добавления свечей
            animationInterval = setInterval(() => {
                if (currentAnimationIndex >= futureCandles.length) {
                    clearInterval(animationInterval);
                    animationInterval = null;
                    
                    // Определяем результат после показа всех свечей
                    determineResult();
                    
                    // Уведомляем Flutter, что все свечи показаны
                    sendMessageToFlutter('allCandlesShown', {});
                    return;
                }
                
                // Добавляем следующую свечу
                const nextCandle = futureCandles[currentAnimationIndex];
                candleSeries.update(nextCandle);
                currentAnimationIndex++;
                
                // Прокручиваем график, чтобы показать новую свечу
                chart.timeScale().scrollToPosition(50, false);
            }, 300); // Интервал между добавлением свечей (300 мс)
        }

        // Определение результата сделки
        function determineResult() {
            if (!entryPointPrice || !entryPointTime) return;
            
            // Получаем последнюю свечу
            const lastCandle = allCandles[visibleCandlesCount + futureCandlesCount - 1];
            if (!lastCandle) return;
            
            // Рассчитываем процентное изменение
            const percentChange = ((lastCandle.close - entryPointPrice) / entryPointPrice) * 100;
            const isUp = lastCandle.close > entryPointPrice;
            
            // Показываем всплывающее окно с результатом
            const resultPopup = document.getElementById('result-popup');
            const resultStatus = document.getElementById('result-status');
            const resultProfit = document.getElementById('result-profit');
            
            resultStatus.textContent = isUp ? 'Price went UP' : 'Price went DOWN';
            resultProfit.textContent = `${percentChange.toFixed(2)}%`;
            resultProfit.className = 'result-profit ' + (isUp ? 'positive' : 'negative');
            
            resultPopup.style.display = 'block';
            
            // Скрываем всплывающее окно через 3 секунды
            setTimeout(() => {
                resultPopup.style.display = 'none';
            }, 3000);
            
            // Уведомляем Flutter о результате
            sendMessageToFlutter('tradeResult', {
                isUp: isUp,
                percentChange: percentChange,
                finalPrice: lastCandle.close
            });
        }

        // Сохранение состояния графика
        function saveChartState() {
            chartState = {
                entryPointPrice: entryPointPrice,
                entryPointTime: entryPointTime
            };
            console.log('Chart state saved');
        }

        // Восстановление состояния графика
        function restoreChartState() {
            if (!chartState) {
                console.warn('No chart state to restore');
                return;
            }
            
            entryPointPrice = chartState.entryPointPrice;
            entryPointTime = chartState.entryPointTime;
            
            // Восстанавливаем горизонтальную линию
            if (entryPointPrice) {
                horizontalLine = candleSeries.createPriceLine({
                    price: entryPointPrice,
                    color: '#2196F3',
                    lineWidth: 2,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                    axisLabelVisible: true,
                    title: 'Entry',
                });
            }
            
            console.log('Chart state restored');
        }

        // Очистка элементов графика
        function clearChartElements() {
            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }
            
            // Удаляем маркеры (вертикальную линию)
            candleSeries.setMarkers([]);
            
            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;
            
            // Скрываем всплывающее окно с результатом
            document.getElementById('result-popup').style.display = 'none';
            
            console.log('Chart elements cleared');
        }
    </script>
</body>
</html>
