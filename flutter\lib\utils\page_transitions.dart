import 'package:flutter/material.dart';

/// Красивые анимации переходов между страницами в стиле Apple
class PageTransitions {
  
  /// Элегантный переход с плавным слайдом и затуханием (основной)
  static PageRouteBuilder<T> elegantSlide<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 400),
      reverseTransitionDuration: const Duration(milliseconds: 350),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Основная анимация слайда
        var slideAnimation = Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        ));

        // Анимация затухания для новой страницы
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
        ));

        // Анимация масштабирования предыдущей страницы
        var scaleAnimation = Tween<double>(
          begin: 1.0,
          end: 0.95,
        ).animate(CurvedAnimation(
          parent: secondaryAnimation,
          curve: Curves.easeOutCubic,
        ));

        // Анимация затухания предыдущей страницы
        var backgroundFadeAnimation = Tween<double>(
          begin: 1.0,
          end: 0.8,
        ).animate(CurvedAnimation(
          parent: secondaryAnimation,
          curve: Curves.easeOut,
        ));

        return Stack(
          children: [
            // Предыдущая страница с эффектами
            FadeTransition(
              opacity: backgroundFadeAnimation,
              child: Transform.scale(
                scale: scaleAnimation.value,
                child: Container(),
              ),
            ),
            // Новая страница
            FadeTransition(
              opacity: fadeAnimation,
              child: SlideTransition(
                position: slideAnimation,
                child: child,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Плавное масштабирование с мягким появлением
  static PageRouteBuilder<T> smoothScale<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 450),
      reverseTransitionDuration: const Duration(milliseconds: 400),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Анимация масштабирования
        var scaleAnimation = Tween<double>(
          begin: 0.85,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutQuart,
        ));

        // Анимация затухания
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
        ));

        // Легкое движение вверх
        var slideAnimation = Tween<Offset>(
          begin: const Offset(0.0, 0.1),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        ));

        // Эффект для предыдущей страницы
        var backgroundScaleAnimation = Tween<double>(
          begin: 1.0,
          end: 0.92,
        ).animate(CurvedAnimation(
          parent: secondaryAnimation,
          curve: Curves.easeOut,
        ));

        var backgroundFadeAnimation = Tween<double>(
          begin: 1.0,
          end: 0.7,
        ).animate(CurvedAnimation(
          parent: secondaryAnimation,
          curve: Curves.easeOut,
        ));

        return Stack(
          children: [
            // Предыдущая страница
            FadeTransition(
              opacity: backgroundFadeAnimation,
              child: Transform.scale(
                scale: backgroundScaleAnimation.value,
                child: Container(),
              ),
            ),
            // Новая страница
            FadeTransition(
              opacity: fadeAnimation,
              child: SlideTransition(
                position: slideAnimation,
                child: ScaleTransition(
                  scale: scaleAnimation,
                  child: child,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Элегантное затухание с легким движением
  static PageRouteBuilder<T> softFade<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Основная анимация затухания
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        ));

        // Легкое движение снизу
        var slideAnimation = Tween<Offset>(
          begin: const Offset(0.0, 0.05),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        ));

        // Легкое масштабирование
        var scaleAnimation = Tween<double>(
          begin: 0.98,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOut,
        ));

        return FadeTransition(
          opacity: fadeAnimation,
          child: SlideTransition(
            position: slideAnimation,
            child: ScaleTransition(
              scale: scaleAnimation,
              child: child,
            ),
          ),
        );
      },
    );
  }

  /// Переход снизу с плавным скольжением (для модальных окон)
  static PageRouteBuilder<T> smoothSlideUp<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 400),
      reverseTransitionDuration: const Duration(milliseconds: 350),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Анимация слайда снизу
        var slideAnimation = Tween<Offset>(
          begin: const Offset(0.0, 1.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        ));

        // Анимация затухания
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
        ));

        // Легкое масштабирование
        var scaleAnimation = Tween<double>(
          begin: 0.95,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutBack,
        ));

        return FadeTransition(
          opacity: fadeAnimation,
          child: SlideTransition(
            position: slideAnimation,
            child: ScaleTransition(
              scale: scaleAnimation,
              child: child,
            ),
          ),
        );
      },
    );
  }

  /// Красивый переход с вращением и масштабированием (для специальных случаев)
  static PageRouteBuilder<T> elegantRotation<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 500),
      reverseTransitionDuration: const Duration(milliseconds: 450),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Анимация поворота
        var rotationAnimation = Tween<double>(
          begin: 0.05,
          end: 0.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutBack,
        ));

        // Анимация масштабирования
        var scaleAnimation = Tween<double>(
          begin: 0.9,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutBack,
        ));

        // Анимация затухания
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
        ));

        // Легкое движение
        var slideAnimation = Tween<Offset>(
          begin: const Offset(0.1, 0.1),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        ));

        return FadeTransition(
          opacity: fadeAnimation,
          child: SlideTransition(
            position: slideAnimation,
            child: Transform.rotate(
              angle: rotationAnimation.value,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            ),
          ),
        );
      },
    );
  }

  /// Переход с эффектом "флип" (переворот)
  static PageRouteBuilder<T> flipTransition<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 600),
      reverseTransitionDuration: const Duration(milliseconds: 550),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Анимация переворота по Y
        var flipAnimation = Tween<double>(
          begin: 1.5708, // 90 градусов в радианах
          end: 0.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutBack,
        ));

        // Анимация затухания
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
        ));

        // Анимация масштабирования
        var scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutBack,
        ));

        return FadeTransition(
          opacity: fadeAnimation,
          child: Transform(
            alignment: Alignment.center,
            transform: Matrix4.identity()
              ..setEntry(3, 2, 0.001)
              ..rotateY(flipAnimation.value),
            child: ScaleTransition(
              scale: scaleAnimation,
              child: child,
            ),
          ),
        );
      },
    );
  }
}

/// Расширение для Navigator для удобного использования
extension NavigatorExtensions on NavigatorState {
  
  /// Навигация с элегантным слайдом
  Future<T?> pushWithElegantSlide<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.elegantSlide<T>(page));
  }

  /// Навигация с плавным масштабированием
  Future<T?> pushWithSmoothScale<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.smoothScale<T>(page));
  }

  /// Навигация с мягким затуханием
  Future<T?> pushWithSoftFade<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.softFade<T>(page));
  }

  /// Навигация с плавным слайдом снизу
  Future<T?> pushWithSmoothSlideUp<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.smoothSlideUp<T>(page));
  }

  /// Навигация с элегантным поворотом
  Future<T?> pushWithElegantRotation<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.elegantRotation<T>(page));
  }

  /// Навигация с эффектом переворота
  Future<T?> pushWithFlip<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.flipTransition<T>(page));
  }

  /// Замена текущей страницы с элегантным слайдом
  Future<T?> pushReplacementWithElegantSlide<T extends Object?, TO extends Object?>(
    Widget page, {
    TO? result,
  }) {
    return pushReplacement<T, TO>(
      PageTransitions.elegantSlide<T>(page),
      result: result,
    );
  }

  /// Замена текущей страницы с плавным масштабированием
  Future<T?> pushReplacementWithSmoothScale<T extends Object?, TO extends Object?>(
    Widget page, {
    TO? result,
  }) {
    return pushReplacement<T, TO>(
      PageTransitions.smoothScale<T>(page),
      result: result,
    );
  }
} 