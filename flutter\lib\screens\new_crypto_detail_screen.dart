import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/crypto_currency.dart';
import '../providers/crypto_provider.dart';
import '../widgets/simple_crypto_chart.dart';

class NewCryptoDetailScreen extends StatefulWidget {
  final CryptoCurrency crypto;

  const NewCryptoDetailScreen({super.key, required this.crypto});

  @override
  State<NewCryptoDetailScreen> createState() => _NewCryptoDetailScreenState();
}

class _NewCryptoDetailScreenState extends State<NewCryptoDetailScreen> {
  String _selectedTimeframe = '1h';
  CryptoCurrency? _latestCryptoData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    
    // Initialize with the provided crypto data
    _latestCryptoData = widget.crypto;
    
    // Check if we have updated data in the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLatestData();
    });
  }

  void _loadLatestData() {
    setState(() => _isLoading = true);
    
    try {
      final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
      final updatedCrypto = cryptoProvider.getCryptoBySymbol(widget.crypto.symbol);
      
      if (updatedCrypto != null) {
        setState(() {
          _latestCryptoData = updatedCrypto;
        });
      }
    } catch (e) {
      // Ignore errors when getting data from provider
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use the latest data if available, otherwise use the widget data
    final crypto = _latestCryptoData ?? widget.crypto;
    
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeSign = isPositive ? '+' : '';
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: Text(
          '${crypto.name} Chart',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadLatestData,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with price and change
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        crypto.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                        ),
                      ),
                      Text(
                        '${crypto.symbol} · USD',
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '\$${_formatPrice(crypto.price)}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                        ),
                      ),
                      Text(
                        '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                        style: TextStyle(
                          color: changeColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Timeframe selector
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildTimeframeButton('1h'),
                    _buildTimeframeButton('24h'),
                    _buildTimeframeButton('7d'),
                    _buildTimeframeButton('30d'),
                    _buildTimeframeButton('all'),
                  ],
                ),
              ),
            ),
            
            // Chart
            Container(
              height: 350,
              margin: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(51),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SimpleCryptoChart.fromCrypto(
                        crypto: crypto,
                        timeframe: _selectedTimeframe,
                        showGrid: true,
                        showLabels: true,
                      ),
                    ),
            ),
            
            // Market info
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Market Info',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow('Market Cap', '\$${_formatLargeNumber(crypto.marketCap)}'),
                  const SizedBox(height: 8),
                  _buildInfoRow('24h Volume', '\$${_formatLargeNumber(crypto.volume24h)}'),
                  const SizedBox(height: 8),
                  _buildInfoRow('24h Change', '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(2)}%'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeframeButton(String timeframe) {
    final isSelected = _selectedTimeframe == timeframe;
    
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedTimeframe = timeframe;
          });
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? Colors.blue : Colors.grey[800],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: Text(timeframe.toUpperCase()),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 14,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  String _formatLargeNumber(double number) {
    if (number >= 1000000000) {
      return '${(number / 1000000000).toStringAsFixed(2)}B';
    } else if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(2)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(2)}K';
    } else {
      return number.toString();
    }
  }
}
