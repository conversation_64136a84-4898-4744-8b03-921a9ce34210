{"version": "0.2.0", "configurations": [{"name": "🚀 TMM Full App (Debug)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": [], "console": "debugConsole", "flutterMode": "debug"}, {"name": "📱 TMM Lite App (Debug)", "request": "launch", "type": "dart", "program": "lib/main_lite.dart", "args": [], "console": "debugConsole", "flutterMode": "debug"}, {"name": "🌐 TMM Full (Web - Chrome)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["-d", "chrome", "--web-renderer", "html"], "console": "debugConsole", "flutterMode": "debug"}, {"name": "📲 TMM Lite (Android Emulator)", "request": "launch", "type": "dart", "program": "lib/main_lite.dart", "args": ["-d", "android"], "console": "debugConsole", "flutterMode": "debug"}, {"name": "🍎 TMM Lite (iOS Simulator)", "request": "launch", "type": "dart", "program": "lib/main_lite.dart", "args": ["-d", "ios"], "console": "debugConsole", "flutterMode": "debug"}, {"name": "⚡ TMM Full (Profile Mode)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": [], "console": "debugConsole", "flutterMode": "profile"}, {"name": "⚡ TMM Lite (Profile Mode)", "request": "launch", "type": "dart", "program": "lib/main_lite.dart", "args": [], "console": "debugConsole", "flutterMode": "profile"}, {"name": "🔧 TMM Unified (Full)", "request": "launch", "type": "dart", "program": "lib/main_unified.dart", "args": ["--dart-define=APP_MODE=full"], "console": "debugConsole", "flutterMode": "debug"}, {"name": "🔧 TMM Unified (Lite)", "request": "launch", "type": "dart", "program": "lib/main_unified.dart", "args": ["--dart-define=APP_MODE=lite"], "console": "debugConsole", "flutterMode": "debug"}]}