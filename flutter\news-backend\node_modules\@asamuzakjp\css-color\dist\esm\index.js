import { cssCalc as cssCalc$1 } from "./js/css-calc.js";
import { isGradient } from "./js/css-gradient.js";
import { cssVar } from "./js/css-var.js";
import { splitValue, isColor as isColor$1, extractDashedIdent } from "./js/util.js";
import { convert } from "./js/convert.js";
import { resolve } from "./js/resolve.js";
/*!
 * CSS color - Resolve, parse, convert CSS color.
 * @license MIT
 * @copyright asamuzaK (Kazz)
 * @see {@link https://github.com/asamuzaK/cssColor/blob/main/LICENSE}
 */
const utils = {
  cssCalc: cssCalc$1,
  cssVar,
  extractDashedIdent,
  isColor: isColor$1,
  isGradient,
  splitValue
};
const isColor = utils.isColor;
const cssCalc = utils.cssCalc;
export {
  convert,
  cssCalc,
  isColor,
  resolve,
  utils
};
//# sourceMappingURL=index.js.map
