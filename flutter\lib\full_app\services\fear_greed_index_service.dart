import 'dart:convert';
import 'package:http/http.dart' as http;

class FearGreedIndexService {
  static Future<int?> fetchFearGreedIndex() async {
    final url = Uri.parse('https://api.alternative.me/fng/');
    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return int.tryParse(data['data'][0]['value']);
      }
    } catch (e) {
      // Можно добавить логирование ошибки
    }
    return null;
  }
} 