import 'package:flutter/material.dart';
import '../utils/device_type.dart';

class PillNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final bool showLabels;
  final double height;
  final Color backgroundColor;
  final Color activeColor;
  final Color inactiveColor;
  final Color pillColor;

  const PillNavigationBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    this.showLabels = true,
    this.height = 60.0,
    this.backgroundColor = Colors.black,
    this.activeColor = Colors.white,
    this.inactiveColor = Colors.grey,
    this.pillColor = Colors.black,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(context, 0, Icons.home, 'Home'),
          _buildNavItem(context, 1, Icons.search, 'Search'),
          _buildNavItem(context, 2, Icons.percent, 'Offers'),
          _buildNavItem(context, 3, Icons.shopping_cart, 'Cart'),
          _buildNavItem(context, 4, Icons.person, 'Profile'),
        ],
      ),
    );
  }

  Widget _buildNavItem(BuildContext context, int index, IconData icon, String label) {
    final bool isActive = currentIndex == index;
    
    // For active item, show a pill with icon and text
    if (isActive) {
      return GestureDetector(
        onTap: () => onTap(index),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: Colors.black,
                size: 20,
              ),
              if (showLabels) ...[
                const SizedBox(width: 8),
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    }
    
    // For inactive items, just show the icon
    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Icon(
          icon,
          color: inactiveColor,
          size: 20,
        ),
      ),
    );
  }
}

// A version that can be placed at the top of the screen
class TopPillNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  
  const TopPillNavigationBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final isDesktop = DeviceUtils.isDesktop(context);
    final isMobile = DeviceUtils.isMobile(context);
    
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: isDesktop ? 600 : isMobile ? 300 : 400,
          ),
          child: PillNavigationBar(
            currentIndex: currentIndex,
            onTap: onTap,
            showLabels: !isMobile,
          ),
        ),
      ),
    );
  }
}
