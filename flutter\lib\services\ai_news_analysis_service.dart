import 'dart:convert';
import 'package:http/http.dart' as http;

import '../models/news_item.dart';
import '../models/news_analysis_result.dart';
import '../config/env_config.dart';

/// Сервис для получения анализа новостей от бэкенда
class AINewsAnalysisService {
  static const String _apiUrl = 'http://localhost:4000/news/analyze';
  
  /// Получение анализа новости от бэкенда
  Future<NewsAnalysisResult?> analyzeNews(NewsItem newsItem) async {
    try {
      // Подготовка запроса к API
      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'newsId': newsItem.id,
          'title': newsItem.title,
          'description': newsItem.description,
          'content': newsItem.content,
          'source': newsItem.source,
          'publishedAt': newsItem.publishedAt?.toIso8601String(),
        }),
      );

      // Проверка ответа
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        return NewsAnalysisResult.fromJson(jsonResponse);
      } else {
        print('Error calling backend API: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error in analyzeNews: $e');
      return null;
    }
  }

  /// Получение финансового анализа новости от бэкенда
  Future<FinancialAnalysisResult?> analyzeNewsFinancially(NewsItem newsItem) async {
    try {
      print('[AIAnalysis] Отправляем запрос на финансовый анализ: ${newsItem.title}');

      // Подготовка запроса к API
      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'newsId': newsItem.id,
          'title': newsItem.title,
          'description': newsItem.description,
          'content': newsItem.content,
          'source': newsItem.source,
          'publishedAt': newsItem.publishedAt?.toIso8601String(),
        }),
      );

      // Проверка ответа
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        print('[AIAnalysis] Получен ответ: ${jsonResponse['status']}');

        if (jsonResponse['status'] == 'success' && jsonResponse['analysis'] != null) {
          return FinancialAnalysisResult.fromJson(jsonResponse['analysis']);
        } else {
          print('[AIAnalysis] Ошибка в ответе: ${jsonResponse['message']}');
          return null;
        }
      } else {
        print('[AIAnalysis] HTTP ошибка: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('[AIAnalysis] Исключение: $e');
      return null;
    }
  }
}

/// Модель результата финансового анализа
class FinancialAnalysisResult {
  final String summary;
  final MarketImpact marketImpact;
  final Forecast forecast;
  final String interestingFact;
  final String? analyzedAt;
  final String? newsId;
  final String? error;

  FinancialAnalysisResult({
    required this.summary,
    required this.marketImpact,
    required this.forecast,
    required this.interestingFact,
    this.analyzedAt,
    this.newsId,
    this.error,
  });

  factory FinancialAnalysisResult.fromJson(Map<String, dynamic> json) {
    return FinancialAnalysisResult(
      summary: json['summary'] ?? '',
      marketImpact: MarketImpact.fromJson(json['marketImpact'] ?? {}),
      forecast: Forecast.fromJson(json['forecast'] ?? {}),
      interestingFact: json['interestingFact'] ?? '',
      analyzedAt: json['analyzedAt'],
      newsId: json['newsId'],
      error: json['error'],
    );
  }
}

/// Модель влияния на рынок
class MarketImpact {
  final String strength;
  final String direction;
  final String rationale;
  final List<AffectedAsset> affectedAssets;
  final String timeHorizon;

  MarketImpact({
    required this.strength,
    required this.direction,
    required this.rationale,
    required this.affectedAssets,
    required this.timeHorizon,
  });

  factory MarketImpact.fromJson(Map<String, dynamic> json) {
    return MarketImpact(
      strength: json['strength'] ?? 'низкое',
      direction: json['direction'] ?? 'нейтральное',
      rationale: json['rationale'] ?? '',
      affectedAssets: (json['affectedAssets'] as List<dynamic>?)
          ?.map((asset) => AffectedAsset.fromJson(asset))
          .toList() ?? [],
      timeHorizon: json['timeHorizon'] ?? 'краткосрочное',
    );
  }
}

/// Модель затронутого актива
class AffectedAsset {
  final String ticker;
  final String type;
  final String impact;
  final String impactMagnitude;

  AffectedAsset({
    required this.ticker,
    required this.type,
    required this.impact,
    required this.impactMagnitude,
  });

  factory AffectedAsset.fromJson(Map<String, dynamic> json) {
    return AffectedAsset(
      ticker: json['ticker'] ?? '',
      type: json['type'] ?? '',
      impact: json['impact'] ?? '',
      impactMagnitude: json['impactMagnitude']?.toString() ?? '0',
    );
  }
}

/// Модель прогноза
class Forecast {
  final String baseScenario;
  final String alternativeScenario;
  final TechnicalLevels technicalLevels;

  Forecast({
    required this.baseScenario,
    required this.alternativeScenario,
    required this.technicalLevels,
  });

  factory Forecast.fromJson(Map<String, dynamic> json) {
    return Forecast(
      baseScenario: json['baseScenario'] ?? '',
      alternativeScenario: json['alternativeScenario'] ?? '',
      technicalLevels: TechnicalLevels.fromJson(json['technicalLevels'] ?? {}),
    );
  }
}

/// Модель технических уровней
class TechnicalLevels {
  final List<String> support;
  final List<String> resistance;

  TechnicalLevels({
    required this.support,
    required this.resistance,
  });

  factory TechnicalLevels.fromJson(Map<String, dynamic> json) {
    return TechnicalLevels(
      support: (json['support'] as List<dynamic>?)
          ?.map((level) => level.toString())
          .toList() ?? [],
      resistance: (json['resistance'] as List<dynamic>?)
          ?.map((level) => level.toString())
          .toList() ?? [],
    );
  }
}
