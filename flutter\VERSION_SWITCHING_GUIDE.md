# Руководство по переключению между версиями TMM

## Обзор

У нас есть две версии приложения:
- **Full App** (`main.dart`) - Полная версия с расширенным функционалом
- **Lite App** (`main_lite.dart`) - Упрощенная мобильная версия

## Способы запуска

### 1. Прямой запуск через Flutter CLI

**Полная версия:**
```bash
flutter run lib/main.dart
```

**Лайт версия:**
```bash
flutter run lib/main_lite.dart
```

### 2. Использование скриптов

**Linux/macOS:**
```bash
# Полная версия
./run_full.sh

# Лайт версия
./run_lite.sh

# Универсальный скрипт
./run_unified.sh full    # или desktop
./run_unified.sh lite    # или mobile
```

**Windows:**
```cmd
# Полная версия
run_full.bat

# Лайт версия
run_lite.bat

# Универсальный скрипт
run_unified.bat full    # или desktop
run_unified.bat lite    # или mobile
```

### 3. Использование Makefile

```bash
# Запуск
make full              # Полная версия
make lite              # Лайт версия
make full-web          # Полная версия в браузере
make lite-mobile       # Лайт версия на мобильном

# Сборка
make build-full        # Сборка полной версии
make build-lite        # Сборка лайт версии
make build-web         # Сборка для веб
make build-apk         # APK лайт версии

# Разработка
make clean             # Очистка
make test              # Тесты
make format            # Форматирование
make analyze           # Анализ кода
```

### 4. VS Code конфигурации

В VS Code нажмите `F5` или `Ctrl+Shift+D` и выберите:
- **TMM Full App** - Полная версия
- **TMM Lite App** - Лайт версия
- **TMM Unified (Full)** - Полная через unified
- **TMM Unified (Lite)** - Лайт через unified
- **TMM Full (Web)** - Полная в браузере
- **TMM Lite (Mobile)** - Лайт на мобильном

## Запуск на конкретных устройствах

### Список устройств
```bash
flutter devices
```

### Запуск на конкретном устройстве
```bash
# Полная версия в Chrome
flutter run -d chrome lib/main.dart

# Лайт версия на эмуляторе
flutter run -d emulator-5554 lib/main_lite.dart

# Через универсальный скрипт
./run_unified.sh full -d chrome
./run_unified.sh lite -d emulator-5554
```

## Сборка для продакшена

### Android APK
```bash
# Полная версия
flutter build apk lib/main.dart --release

# Лайт версия
flutter build apk lib/main_lite.dart --release

# Через Makefile
make build-full
make build-lite
```

### Web
```bash
# Полная версия (рекомендуется)
flutter build web lib/main.dart

# Через Makefile
make build-web
```

### iOS (только на macOS)
```bash
# Полная версия
flutter build ios lib/main.dart --release

# Лайт версия
flutter build ios lib/main_lite.dart --release
```

## Переменные окружения

Для unified версии можно использовать переменные:

```bash
# Через dart-define
flutter run --dart-define=APP_MODE=full lib/main_unified.dart
flutter run --dart-define=APP_MODE=lite lib/main_unified.dart

# Через переменные окружения (если поддерживается)
APP_MODE=full flutter run lib/main_unified.dart
APP_MODE=lite flutter run lib/main_unified.dart
```

## Отладка и разработка

### Hot Reload
Работает одинаково для обеих версий:
- `r` - Hot reload
- `R` - Hot restart
- `q` - Выход

### Логи
```bash
# Просмотр логов
flutter logs

# Очистка логов
flutter logs --clear
```

### Профилирование
```bash
# Запуск в режиме профилирования
flutter run --profile lib/main.dart
flutter run --profile lib/main_lite.dart
```

## Рекомендации

### Для разработки
- Используйте **полную версию** для desktop/web разработки
- Используйте **лайт версию** для мобильной разработки
- Тестируйте на реальных устройствах

### Для тестирования
- Тестируйте обе версии на целевых платформах
- Используйте разные размеры экранов
- Проверяйте производительность на слабых устройствах

### Для продакшена
- **Web**: Используйте полную версию
- **Mobile**: Используйте лайт версию
- **Desktop**: Используйте полную версию

## Устранение проблем

### Проблемы с зависимостями
```bash
flutter clean
flutter pub get
```

### Проблемы с кэшем
```bash
flutter clean
rm -rf build/
flutter pub get
```

### Проблемы с устройствами
```bash
flutter doctor
flutter devices
```

### Проблемы с производительностью
- Используйте `--release` для финальной сборки
- Проверьте размер bundle с помощью `flutter build apk --analyze-size`
- Используйте профилировщик Flutter

## Автоматизация

### CI/CD
Можно настроить автоматическую сборку обеих версий:

```yaml
# Пример для GitHub Actions
- name: Build Full App
  run: flutter build apk lib/main.dart --release

- name: Build Lite App  
  run: flutter build apk lib/main_lite.dart --release
```

### Скрипты развертывания
Создайте скрипты для автоматического развертывания разных версий на разные платформы.

## Заключение

Выбирайте подходящий способ запуска в зависимости от ваших потребностей:
- **Быстрое тестирование**: Flutter CLI
- **Ежедневная разработка**: VS Code конфигурации
- **Автоматизация**: Makefile или скрипты
- **CI/CD**: Прямые команды Flutter
