import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import '../widgets/app_bottom_navigation.dart';
import '../services/market_sentiment_service.dart';
import '../services/stable_prediction_service.dart';
import '../models/sentiment_history_model.dart';

class SinusoidScreen extends StatefulWidget {
  const SinusoidScreen({super.key});

  @override
  State<SinusoidScreen> createState() => _SinusoidScreenState();
}

class _SinusoidScreenState extends State<SinusoidScreen> {
  final MarketSentimentService _sentimentService = MarketSentimentService();
  final StablePredictionService _predictionService = StablePredictionService();

  double _indicatorValue = 50.0; // Default neutral value
  bool _isLoading = true;
  bool _isLoadingHistory = true;
  bool _isLoadingPredictions = true;

  Map<String, double> _metricValues = {
    'Fear & Greed Index': 50.0,
    'News Sentiment': 50.0,
    'Holders Score': 50.0,
    'Volume Score': 50.0,
    'Social Engagement': 50.0,
    'Price Volatility': 50.0,
    'Bitcoin Dominance': 50.0,
  };

  // Historical data
  SentimentHistoryEntry? _yesterdayEntry;
  SentimentHistoryEntry? _lastWeekEntry;

  // Prediction data
  List<SentimentHistoryEntry> _predictions = [];

  @override
  void initState() {
    super.initState();
    _fetchMarketSentiment();
    _fetchHistoricalData();
    _fetchPredictions();
  }

  // Fetch historical sentiment data
  Future<void> _fetchHistoricalData() async {
    debugPrint('Fetching historical sentiment data...');

    setState(() {
      _isLoadingHistory = true;
    });

    try {
      // Get historical data
      final history = await _predictionService.getHistoricalData();
      debugPrint('Retrieved ${history.entries.length} historical entries');

      // Get yesterday's and last week's entries
      final yesterdayEntry = history.getYesterdayEntry();
      final lastWeekEntry = history.getLastWeekEntry();

      debugPrint('Yesterday entry: ${yesterdayEntry?.date} - ${yesterdayEntry?.value}');
      debugPrint('Last week entry: ${lastWeekEntry?.date} - ${lastWeekEntry?.value}');

      if (mounted) {
        setState(() {
          _yesterdayEntry = yesterdayEntry;
          _lastWeekEntry = lastWeekEntry;
          _isLoadingHistory = false;
        });
      }
    } catch (e) {
      debugPrint('Error fetching historical data: $e');
      if (mounted) {
        setState(() {
          _isLoadingHistory = false;
        });
      }
    }
  }

  // Fetch sentiment predictions
  Future<void> _fetchPredictions() async {
    debugPrint('Fetching sentiment predictions...');

    setState(() {
      _isLoadingPredictions = true;
    });

    try {
      // Get predictions for the next 2 days using ML-based prediction
      final predictions = await _predictionService.predictFutureSentimentML(2);

      debugPrint('Retrieved ${predictions.length} ML-based predictions');
      for (var i = 0; i < predictions.length; i++) {
        debugPrint('ML Prediction $i: ${predictions[i].date} - ${predictions[i].value}');
      }

      if (mounted) {
        setState(() {
          _predictions = predictions;
          _isLoadingPredictions = false;
        });
      }
    } catch (e) {
      debugPrint('Error fetching ML predictions, falling back to standard method: $e');

      try {
        // Fallback to standard prediction method
        final predictions = await _predictionService.predictFutureSentiment(2);

        debugPrint('Retrieved ${predictions.length} standard predictions');

        if (mounted) {
          setState(() {
            _predictions = predictions;
            _isLoadingPredictions = false;
          });
        }
      } catch (fallbackError) {
        debugPrint('Error fetching predictions with fallback method: $fallbackError');
        if (mounted) {
          setState(() {
            _isLoadingPredictions = false;
          });
        }
      }
    }
  }

  Future<void> _fetchMarketSentiment() async {
    debugPrint('Fetching market sentiment data...');

    setState(() {
      _isLoading = true;
    });

    try {
      // Calculate final indicator (this will fetch all metrics and save to history)
      final indicator = await _sentimentService.calculateMarketSentiment();
      debugPrint('Received indicator value: $indicator');

      // Get metrics from cache to avoid duplicate API calls
      final fearGreedIndex = await _sentimentService.fetchFearGreedIndex();
      final newsSentiment = await _sentimentService.fetchNewsSentiment();
      final holdersScore = await _sentimentService.fetchHoldersScore();
      final volumeScore = await _sentimentService.fetchVolumeScore();
      final socialEngagement = await _sentimentService.fetchSocialEngagement();
      final priceVolatility = await _sentimentService.fetchPriceVolatility();
      final bitcoinDominance = await _sentimentService.fetchBitcoinDominance();

      // Normalize values for display
      final normalizedNewsSentiment = (newsSentiment + 1) * 50;
      final normalizedVolumeScore = volumeScore.clamp(0.0, 1.0) * 100;

      // Create metrics map
      final metrics = {
        'Fear & Greed Index': fearGreedIndex,
        'News Sentiment': normalizedNewsSentiment,
        'Holders Score': holdersScore,
        'Volume Score': normalizedVolumeScore,
        'Social Engagement': socialEngagement,
        'Price Volatility': priceVolatility,
        'Bitcoin Dominance': bitcoinDominance,
      };

      // Manually save to history to ensure it's saved
      await _sentimentService.saveToHistory(indicator, metrics);
      debugPrint('Saved current data to history');

      // Update state
      if (mounted) {
        setState(() {
          _indicatorValue = indicator;
          _metricValues = metrics;
          _isLoading = false;
        });
      }

      // Refresh historical data and predictions
      debugPrint('Refreshing historical data and predictions...');
      await _fetchHistoricalData();
      await _fetchPredictions();
    } catch (e) {
      if (mounted) {
        setState(() {
          // Use default values if everything fails
          _indicatorValue = 50.0;
          _metricValues = {
            'Fear & Greed Index': 50.0,
            'News Sentiment': 50.0,
            'Holders Score': 50.0,
            'Volume Score': 50.0,
            'Social Engagement': 50.0,
            'Price Volatility': 50.0,
            'Bitcoin Dominance': 50.0,
          };
          _isLoading = false;
        });
      }
      debugPrint('Error fetching market sentiment: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: false,
      extendBody: false, // Убираем расширение body за навигационную панель
      appBar: AppBar(
        title: const Text('Market Sentiment'),
        backgroundColor: Colors.black,
        elevation: 0,
        actions: [
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchMarketSentiment,
            tooltip: 'Refresh data',
          ),
          // Reset button (for testing)
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: _resetData,
            tooltip: 'Reset data (for testing)',
          ),
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black, // Убираем любые белые области
        child: Center(
          child: Text(
            'Sinusoid Screen - Value: ${_indicatorValue.toStringAsFixed(1)}',
            style: const TextStyle(color: Colors.white, fontSize: 18),
          ),
        ),
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 2, // Sinusoid
        onTap: (index) {
          if (index != 2) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 1:
                Navigator.pushReplacementNamed(context, '/charts');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }

  // Reset all data (for testing)
  Future<void> _resetData() async {
    debugPrint('Resetting all data...');

    // Show loading indicator
    setState(() {
      _isLoading = true;
      _isLoadingHistory = true;
      _isLoadingPredictions = true;
    });

    try {
      // Clear cache
      await _sentimentService.clearCache();

      // Clear history
      await _predictionService.clearHistory();

      // Reset state
      setState(() {
        _indicatorValue = 50.0;
        _metricValues = {
          'Fear & Greed Index': 50.0,
          'News Sentiment': 50.0,
          'Holders Score': 50.0,
          'Volume Score': 50.0,
          'Social Engagement': 50.0,
          'Price Volatility': 50.0,
          'Bitcoin Dominance': 50.0,
        };
        _yesterdayEntry = null;
        _lastWeekEntry = null;
        _predictions = [];
      });

      // Fetch fresh data
      await _fetchMarketSentiment();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All data has been reset'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error resetting data: $e');
    } finally {
      // Ensure loading indicators are hidden
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isLoadingHistory = false;
          _isLoadingPredictions = false;
        });
      }
    }
  }
}
