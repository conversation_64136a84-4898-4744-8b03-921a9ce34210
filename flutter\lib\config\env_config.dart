import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvConfig {
  // Статические ключи по умолчанию для разработки
  static const Map<String, String> _defaultKeys = {
    'NEWS_API_KEY': '911a4477d78f4bbfba24c0952cda7be1',
    'CRYPTOCOMPARE_API_KEY': '****************************************************************',
    'DEEPSEEK_API_KEY': '***********************************',
  };
  
  // Загрузка переменных окружения
  static Future<void> load() async {
    if (kIsWeb) {
      print('EnvConfig: Web detected, пропускаем загрузку .env');
      // Для Web используем только дефолтные значения
      return;
    }
    try {
      print('EnvConfig: Попытка загрузки .env файла');
      await dotenv.load(fileName: '.env').catchError((error) {
        print('EnvConfig: Ошибка загрузки .env: $error');
      });
    } catch (e) {
      print('EnvConfig: Критическая ошибка при загрузке .env: $e');
      // Продолжаем выполнение без .env файла
    }
    
    // Независимо от результата загрузки, проверяем и устанавливаем все ключи
    
    // Ключ DeepSeek
    if (dotenv.env['DEEPSEEK_API_KEY'] == null || dotenv.env['DEEPSEEK_API_KEY']!.isEmpty) {
      print('EnvConfig: Устанавливаем ключ DeepSeek из значений по умолчанию');
      dotenv.env['DEEPSEEK_API_KEY'] = _defaultKeys['DEEPSEEK_API_KEY']!;
    } else {
      print('EnvConfig: Ключ DeepSeek загружен из .env: ${dotenv.env['DEEPSEEK_API_KEY']!.substring(0, 5)}...');
    }
    
    // Ключ News API
    if (dotenv.env['NEWS_API_KEY'] == null || dotenv.env['NEWS_API_KEY']!.isEmpty) {
      dotenv.env['NEWS_API_KEY'] = _defaultKeys['NEWS_API_KEY']!;
    }
    
    // Ключ CryptoCompare API
    if (dotenv.env['CRYPTOCOMPARE_API_KEY'] == null || dotenv.env['CRYPTOCOMPARE_API_KEY']!.isEmpty) {
      dotenv.env['CRYPTOCOMPARE_API_KEY'] = _defaultKeys['CRYPTOCOMPARE_API_KEY']!;
    }
    
    // Контрольная проверка ключа DeepSeek после всех инициализаций
    print('EnvConfig: Финальная проверка - ключ DeepSeek установлен: ${dotenv.env['DEEPSEEK_API_KEY'] != null && dotenv.env['DEEPSEEK_API_KEY']!.isNotEmpty}');
  }

  // Получение API ключа для News API
  static String get newsApiKey {
    return dotenv.env['NEWS_API_KEY'] ?? _defaultKeys['NEWS_API_KEY']!;
  }
  
  // Получение API ключа для CryptoCompare
  static String get cryptoCompareApiKey {
    return dotenv.env['CRYPTOCOMPARE_API_KEY'] ?? _defaultKeys['CRYPTOCOMPARE_API_KEY']!;
  }
  
  // Получение API ключа для DeepSeek AI
  static String get deepSeekApiKey {
    final key = dotenv.env['DEEPSEEK_API_KEY'];
    if (key == null || key.isEmpty) {
      return _defaultKeys['DEEPSEEK_API_KEY']!;
    }
    return key;
  }
  
  // Дополнительные API ключи
  static const String cryptoPanicApiKey = 'cd34f89cbfd95a1ba291277f2a99a2b388ab944f';
  static const String polygonApiKey = 'qXdWN4NwpUjYY164_sXuIiZdxqoce5PC';
}
