import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/candle.dart';

// Компонент для отображения графика свечей
class CandlestickChart extends StatelessWidget {
  final List<Candle> candles;
  final int? entryPointIndex;

  const CandlestickChart({
    Key? key,
    required this.candles,
    this.entryPointIndex,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: candles.isEmpty
          ? const Center(child: Text('No data available', style: TextStyle(color: Colors.white)))
          : CandleStickChart(
              data: candles.map((candle) => CandleData(
                date: DateTime.fromMillisecondsSinceEpoch(candle.time * 1000),
                high: candle.high,
                low: candle.low,
                open: candle.open,
                close: candle.close,
                volume: candle.volume,
              )).toList(),
              entryPointIndex: entryPointIndex,
            ),
    );
  }
}

// Виджет для отображения свечного графика с использованием fl_chart
class CandleStickChart extends StatelessWidget {
  final List<CandleData> data;
  final int? entryPointIndex;

  const CandleStickChart({
    Key? key,
    required this.data,
    this.entryPointIndex,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(8),
      child: CandlestickChartWidget(
        data: data,
        entryPointIndex: entryPointIndex,
      ),
    );
  }
}

// Виджет для отображения свечного графика с использованием fl_chart
class CandlestickChartWidget extends StatelessWidget {
  final List<CandleData> data;
  final int? entryPointIndex;

  const CandlestickChartWidget({
    Key? key,
    required this.data,
    this.entryPointIndex,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        barTouchData: BarTouchData(
          enabled: false,
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() % 20 != 0) return const SizedBox();
                final index = value.toInt();
                if (index < 0 || index >= data.length) return const SizedBox();
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    '${data[index].date.day}/${data[index].date.month}',
                    style: const TextStyle(
                      color: Colors.white60,
                      fontSize: 10,
                    ),
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Text(
                    value.toStringAsFixed(1),
                    style: const TextStyle(
                      color: Colors.white60,
                      fontSize: 10,
                    ),
                  ),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        gridData: FlGridData(
          show: true,
          horizontalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.white10,
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.white10,
              strokeWidth: 1,
            );
          },
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: data.asMap().entries.map((entry) {
          final index = entry.key;
          final candle = entry.value;

          final isUp = candle.close >= candle.open;
          final color = isUp ? Colors.green : Colors.red;

          // Отмечаем точку входа
          final isEntryPoint = entryPointIndex != null && index == entryPointIndex;

          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: candle.high,
                fromY: candle.low,
                color: color,
                width: 2,
              ),
              BarChartRodData(
                toY: isUp ? candle.close : candle.open,
                fromY: isUp ? candle.open : candle.close,
                color: color,
                width: 8,
              ),
            ],
            // Добавляем маркер для точки входа
            showingTooltipIndicators: isEntryPoint ? [0] : [],
          );
        }).toList(),
      ),
    );
  }
}
