# Mobile Lite Architecture

## Обзор

Мы реструктурировали приложение для поддержки двух версий:
- **Full App** - полная версия с расширенным функционалом
- **Lite App** - упрощенная мобильная версия

## Структура проекта

```
lib/
├── shared/                    # Общие компоненты для обеих версий
│   ├── models/               # Модели данных
│   ├── styles/               # Стили и цвета
│   ├── widgets/              # Базовые UI компоненты
│   ├── services/             # Базовые сервисы
│   ├── providers/            # State management
│   ├── config/               # Конфигурация
│   └── utils/                # Утилиты
├── full_app/                 # Полная версия приложения
│   ├── screens/              # Экраны полной версии
│   ├── widgets/              # Специфичные виджеты
│   └── services/             # Расширенные сервисы
├── lite_app/                 # Лайт версия приложения
│   ├── screens/              # Упрощенные экраны
│   ├── widgets/              # Мобильные виджеты
│   └── services/             # Базовые сервисы
├── main.dart                 # Точка входа для полной версии
└── main_lite.dart           # Точка входа для лайт версии
```

## Shared компоненты

### Стили (`shared/styles/`)
- `app_colors.dart` - Цветовая палитра приложения
- `text_styles.dart` - Стили текста
- `card_gradients.dart` - Градиенты для карточек

### Виджеты (`shared/widgets/`)
- `primary_button.dart` - Основная кнопка с анимациями
- `form_field.dart` - Поле формы с валидацией
- `base_card.dart` - Базовая карточка с вариантами
- Переиспользуемые компоненты (кнопки, карточки, чарты)

### Модели (`shared/models/`)
- Все модели данных (криптовалюты, новости, пользователи)
- Модели для торгового симулятора
- Модели для обучающих материалов

### Сервисы (`shared/services/`)
- `binance_service.dart` - API Binance
- `coingecko_service.dart` - API CoinGecko
- `news_service.dart` - Сервис новостей
- `crypto_logo_service.dart` - Логотипы криптовалют

## Full App

Содержит все расширенные функции:
- Продвинутые чарты и анализ
- Торговый симулятор
- Обучающие материалы
- Сложные анимации и эффекты
- Desktop-ориентированная навигация

## Lite App

Упрощенная версия для мобильных устройств:
- Базовый просмотр рынка
- Простые новости
- Портфолио
- Минималистичный дизайн
- Мобильная навигация

## Запуск приложений

### Полная версия
```bash
flutter run lib/main.dart
```

### Лайт версия
```bash
flutter run lib/main_lite.dart
```

## Преимущества архитектуры

1. **Переиспользование кода** - общие компоненты в shared/
2. **Разделение ответственности** - четкое разделение функций
3. **Масштабируемость** - легко добавлять новые функции
4. **Поддержка** - упрощенная поддержка двух версий
5. **Производительность** - лайт версия загружает только необходимое

## Следующие шаги

1. Адаптация экранов для мобильных устройств
2. Оптимизация производительности лайт версии
3. Создание адаптивных компонентов
4. Тестирование на различных устройствах
5. Настройка CI/CD для двух версий

## Компоненты для адаптации

### Приоритет 1 (Критично)
- [ ] Навигация для мобильных устройств
- [ ] Адаптивные карточки криптовалют
- [ ] Мобильные чарты
- [ ] Упрощенные формы

### Приоритет 2 (Важно)
- [ ] Мобильные новости
- [ ] Портфолио для телефонов
- [ ] Поиск и фильтры
- [ ] Настройки пользователя

### Приоритет 3 (Желательно)
- [ ] Базовый торговый симулятор
- [ ] Уведомления
- [ ] Офлайн режим
- [ ] Темная/светлая тема

## Технические детали

### Зависимости
- `provider` - State management
- `flutter_animate` - Анимации
- `webview_flutter` - Веб-виджеты (только full app)

### Цветовая схема
- Основной: `#6366F1` (Indigo)
- Вторичный: `#10B981` (Emerald)
- Фон: `#0F172A` (Slate 900)
- Поверхность: `#334155` (Slate 700)

### Типографика
- Заголовки: Inter, 600-700 weight
- Основной текст: Inter, 400-500 weight
- Размеры: 12px - 32px

## Рекомендации по разработке

1. **Используйте shared компоненты** везде, где возможно
2. **Создавайте адаптивные виджеты** для разных размеров экрана
3. **Тестируйте на реальных устройствах** для проверки производительности
4. **Следуйте принципам Material Design** для мобильной версии
5. **Оптимизируйте изображения и анимации** для мобильных устройств
