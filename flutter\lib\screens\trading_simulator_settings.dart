import 'package:flutter/material.dart';
import 'trading_simulator_game.dart';

class TradingSimulatorSettings extends StatefulWidget {
  const TradingSimulatorSettings({Key? key}) : super(key: key);

  @override
  _TradingSimulatorSettingsState createState() => _TradingSimulatorSettingsState();
}

class _TradingSimulatorSettingsState extends State<TradingSimulatorSettings> {
  final _symbolController = TextEditingController(text: 'BTCUSDT');
  int _intervalSeconds = 60;

  final List<Map<String, dynamic>> _intervalOptions = [
    {'label': '1 min', 'value': 60},
    {'label': '5 min', 'value': 300},
    {'label': '15 min', 'value': 900},
    {'label': '1 hour', 'value': 3600},
    {'label': '4 hours', 'value': 14400},
    {'label': '1 day', 'value': 86400},
  ];

  final List<Map<String, dynamic>> _popularSymbols = [
    {'symbol': 'CUSTOM', 'name': '<PERSON> Scenarios'},
    {'symbol': 'BTCUSDT', 'name': 'Bitcoin'},
    {'symbol': 'ETHUSDT', 'name': 'Ethereum'},
    {'symbol': 'BNBUSDT', 'name': 'Binance Coin'},
    {'symbol': 'SOLUSDT', 'name': 'Solana'},
    {'symbol': 'ADAUSDT', 'name': 'Cardano'},
    {'symbol': 'XRPUSDT', 'name': 'Ripple'},
    {'symbol': 'DOGEUSDT', 'name': 'Dogecoin'},
    {'symbol': 'DOTUSDT', 'name': 'Polkadot'},
  ];

  void _startGame() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TradingSimulatorGame(
          symbol: _symbolController.text,
          intervalSeconds: _intervalSeconds,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trading Simulator Settings'),
        backgroundColor: Colors.black,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black,
              Colors.blueGrey.shade900,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Symbol input
              const Text(
                'Symbol (e.g. BTCUSDT)',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _symbolController,
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.white10,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  hintText: 'Enter trading pair',
                  hintStyle: TextStyle(color: Colors.white30),
                  suffixIcon: Icon(Icons.search, color: Colors.white54),
                ),
                style: TextStyle(color: Colors.white),
              ),

              const SizedBox(height: 16),

              // Popular symbols
              const Text(
                'Trading Pairs',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _popularSymbols.map((symbol) {
                  return InkWell(
                    onTap: () {
                      setState(() {
                        _symbolController.text = symbol['symbol'];
                      });
                    },
                    child: Chip(
                      backgroundColor: _symbolController.text == symbol['symbol']
                          ? Colors.blue
                          : Colors.white10,
                      label: Text(
                        symbol['symbol'],
                        style: TextStyle(
                          color: _symbolController.text == symbol['symbol']
                              ? Colors.white
                              : Colors.white70,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),

              // Description for Custom Mode
              if (_symbolController.text == 'CUSTOM') ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withAlpha(40),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withAlpha(60)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Text(
                        'Random Scenarios Mode',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Each level will present a unique trading scenario with different patterns. Perfect for training your pattern recognition skills across various market conditions.',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // Interval selection
              const Text(
                'Time Interval',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Interval: ${_getIntervalLabel(_intervalSeconds)}',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Slider(
                min: 0,
                max: _intervalOptions.length - 1.0,
                divisions: _intervalOptions.length - 1,
                value: _getIntervalIndex(_intervalSeconds).toDouble(),
                onChanged: (v) => setState(() => _intervalSeconds = _intervalOptions[v.toInt()]['value']),
                activeColor: Colors.blue,
                inactiveColor: Colors.white24,
              ),

              // Interval options
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _intervalOptions.first['label'],
                    style: TextStyle(color: Colors.white54, fontSize: 12),
                  ),
                  Text(
                    _intervalOptions.last['label'],
                    style: TextStyle(color: Colors.white54, fontSize: 12),
                  ),
                ],
              ),

              const Spacer(),

              // Start button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _startGame,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Start Game',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getIntervalLabel(int seconds) {
    final option = _intervalOptions.firstWhere(
      (option) => option['value'] == seconds,
      orElse: () => _intervalOptions[0],
    );
    return option['label'];
  }

  int _getIntervalIndex(int seconds) {
    final index = _intervalOptions.indexWhere((option) => option['value'] == seconds);
    return index >= 0 ? index : 0;
  }

  @override
  void dispose() {
    _symbolController.dispose();
    super.dispose();
  }
}
