import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart' as ui;

class CandlestickChart extends StatelessWidget {
  final List<List<dynamic>> klines;
  final Color upColor;
  final Color downColor;
  final bool showGrid;
  final bool showLabels;
  final double? height;
  final double? width;
  final String timeframe; // Добавляем параметр для таймфрейма
  final double? currentPrice; // Добавляем параметр для текущей цены
  final double rightPadding; // Новый параметр

  const CandlestickChart({
    super.key,
    required this.klines,
    this.upColor = Colors.green,
    this.downColor = Colors.red,
    this.showGrid = true,
    this.showLabels = true,
    this.height,
    this.width,
    this.timeframe = '1Ч', // По умолчанию часовой таймфрейм
    this.currentPrice, // Текущая цена (опционально)
    this.rightPadding = 0, // По умолчанию 0
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        size: Size.infinite,
        painter: _CandlestickPainter(
          klines: klines,
          upColor: upColor,
          downColor: downColor,
          showGrid: showGrid,
          showLabels: showLabels,
          timeframe: timeframe, // Передаем таймфрейм
          currentPrice: currentPrice, // Передаем текущую цену
          rightPadding: rightPadding, // Прокидываем
        ),
      ),
    );
  }
}

class _CandlestickPainter extends CustomPainter {
  final List<List<dynamic>> klines;
  final Color upColor;
  final Color downColor;
  final bool showGrid;
  final bool showLabels;
  final String timeframe; // Добавляем параметр для таймфрейма
  final double? currentPrice; // Добавляем параметр для текущей цены
  final double rightPadding; // Новый параметр

  _CandlestickPainter({
    required this.klines,
    required this.upColor,
    required this.downColor,
    required this.showGrid,
    required this.showLabels,
    required this.timeframe, // Требуем параметр таймфрейма
    this.currentPrice, // Текущая цена (опционально)
    this.rightPadding = 0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (klines.isEmpty) return;

    final double width = size.width;
    final double height = size.height;

    // Извлекаем данные из klines
    final List<double> opens = [];
    final List<double> highs = [];
    final List<double> lows = [];
    final List<double> closes = [];
    final List<String> timestamps = [];

    for (var kline in klines) {
      try {
        opens.add(double.parse(kline[1].toString()));
        highs.add(double.parse(kline[2].toString()));
        lows.add(double.parse(kline[3].toString()));
        closes.add(double.parse(kline[4].toString()));

        // Преобразуем timestamp в дату
        final timestamp = DateTime.fromMillisecondsSinceEpoch(kline[0] as int);
        // Форматируем метку времени в зависимости от таймфрейма
        timestamps.add(formatTimestamp(timestamp, timeframe));
      } catch (e) {
        // Пропускаем некорректные данные
        continue;
      }
    }

    if (opens.isEmpty) return;

    // Находим минимальное и максимальное значения
    final double minValue = lows.reduce((a, b) => a < b ? a : b);
    final double maxValue = highs.reduce((a, b) => a > b ? a : b);

    // Добавляем отступ к минимальному и максимальному значениям
    final padding = (maxValue - minValue) * 0.1;
    final adjustedMinValue = minValue - padding;
    final adjustedMaxValue = maxValue + padding;

    // Убеждаемся, что есть диапазон для работы
    final double valueRange = adjustedMaxValue - adjustedMinValue > 0.000001
        ? adjustedMaxValue - adjustedMinValue
        : 1.0;

    // Добавляем отступ справа для последней свечи/точки
    final double drawableWidth = width - rightPadding; // Доступная ширина для рисования

    // Рассчитываем масштабирующие факторы с учетом отступа
    final double candleTotalWidth = drawableWidth / klines.length;
    final double candleWidth = candleTotalWidth * 0.8;
    final double spacing = candleTotalWidth * 0.2;
    final double yScale = height / valueRange;

    // Рисуем сетку, если нужно
    if (showGrid) {
      _drawGrid(canvas, size, adjustedMinValue, adjustedMaxValue, drawableWidth, rightPadding);
    }

    // Рисуем свечи
    for (int i = 0; i < opens.length; i++) {
      final double open = opens[i];
      final double high = highs[i];
      final double low = lows[i];
      final double close = closes[i];

      final bool isUp = close >= open;
      final Color color = isUp ? upColor : downColor;

      // Рассчитываем позицию X с учетом отступа справа
      final double x = i * (candleWidth + spacing) + spacing / 2;
      final double candleTop = height - (Math.max(open, close) - adjustedMinValue) * yScale;
      final double candleBottom = height - (Math.min(open, close) - adjustedMinValue) * yScale;
      final double candleHeight = candleBottom - candleTop;

      final double highY = height - (high - adjustedMinValue) * yScale;
      final double lowY = height - (low - adjustedMinValue) * yScale;

      // Рисуем фитиль (вертикальную линию)
      final Paint wickPaint = Paint()
        ..color = color
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;

      canvas.drawLine(
        Offset(x + candleWidth / 2, highY),
        Offset(x + candleWidth / 2, lowY),
        wickPaint,
      );

      // Рисуем тело свечи
      final Paint candlePaint = Paint()
        ..color = color
        ..style = isUp ? PaintingStyle.stroke : PaintingStyle.fill;

      if (isUp) {
        candlePaint.strokeWidth = 1.0;
      }

      canvas.drawRect(
        Rect.fromLTWH(x, candleTop, candleWidth, candleHeight),
        candlePaint,
      );
    }

    // Рисуем метки, если нужно
    if (showLabels) {
      _drawLabels(canvas, size, adjustedMinValue, adjustedMaxValue, timestamps, drawableWidth, rightPadding);
    }
  }

  void _drawGrid(Canvas canvas, Size size, double minValue, double maxValue, double drawableWidth, double rightPadding) {
    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(51)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Рисуем горизонтальные линии сетки
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // Рисуем вертикальные линии сетки
    // Рисуем вертикальные линии сетки с учетом отступа справа
    for (int i = 0; i <= 6; i++) {
      // Распределяем линии по доступной ширине
      final x = drawableWidth * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }
  }

  void _drawLabels(Canvas canvas, Size size, double minValue, double maxValue, List<String> timestamps, double drawableWidth, double rightPadding) {
    final textPainter = TextPainter(
      textDirection: ui.TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxValue - (i / 4) * (maxValue - minValue);
      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );
      textPainter.layout();
      // Рисуем все ценовые метки у самого правого края (без rightPadding)
      textPainter.paint(canvas, Offset(size.width - textPainter.width - 4, y - textPainter.height / 2));
    }

    // --- Метка текущей цены всегда у самого правого края ---
    if (currentPrice != null) {
      if (currentPrice! >= minValue && currentPrice! <= maxValue) {
        final y = size.height - ((currentPrice! - minValue) / (maxValue - minValue) * size.height);
        final isLastCandleUp = klines.isNotEmpty &&
            double.parse(klines.last[4].toString()) >= double.parse(klines.last[1].toString());
        final lineColor = isLastCandleUp ? upColor : downColor;
        final linePaint = Paint()
          ..color = lineColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;
        // Пунктирная линия
        final dashWidth = 5.0;
        final dashSpace = 3.0;
        double startX = 0;
        while (startX < size.width) {
          canvas.drawLine(
            Offset(startX, y),
            Offset(startX + dashWidth, y),
            linePaint,
          );
          startX += dashWidth + dashSpace;
        }
        // Метка цены — всегда у самого правого края
        final bgPaint = Paint()
          ..color = Colors.black.withOpacity(0.7)
          ..style = PaintingStyle.fill;
        final pricePainter = TextPainter(
          textDirection: ui.TextDirection.ltr,
          textAlign: TextAlign.right,
        );
        pricePainter.text = TextSpan(
          text: _formatPrice(currentPrice!),
          style: TextStyle(color: lineColor, fontSize: 10, fontWeight: FontWeight.bold),
        );
        pricePainter.layout();
        final textBgRect = Rect.fromLTWH(
          size.width - pricePainter.width - 8, // без rightPadding
          y - pricePainter.height / 2 - 2,
          pricePainter.width + 8,
          pricePainter.height + 4,
        );
        canvas.drawRect(textBgRect, bgPaint);
        pricePainter.paint(canvas, Offset(size.width - pricePainter.width - 4, y - pricePainter.height / 2));
      }
    }

    // Рисуем метки дат внизу
    if (timestamps.isNotEmpty) {
      // Определяем количество меток в зависимости от таймфрейма
      int labelCount;
      switch (timeframe) {
        case '30М': // 30 минут
          labelCount = 6; // Показываем 6 меток для 30-минутного графика
          break;
        case '1Ч': // 1 час
          labelCount = 6; // Показываем 6 меток для часового графика
          break;
        case '4Ч': // 4 часа
          labelCount = 7; // Показываем 7 меток для 4-часового графика
          break;
        case '1Д': // 1 день
          labelCount = 8; // Показываем 8 меток для дневного графика
          break;
        case '1Н': // 1 неделя
          labelCount = 6; // Показываем 6 меток для недельного графика
          break;
        default:
          labelCount = 6;
      }

      // Выбираем равномерно распределенные метки
      final visibleDates = timestamps.length > labelCount ?
          _selectEvenlyDistributedLabels(timestamps, labelCount) :
          timestamps;

      for (int i = 0; i < visibleDates.length; i++) {
        // Распределяем метки по доступной ширине с учетом отступа справа
        final x = i == 0 ? 0 : drawableWidth * i / (visibleDates.length - 1);

        textPainter.text = TextSpan(
          text: visibleDates[i],
          style: const TextStyle(color: Colors.white, fontSize: 10),
        );

        textPainter.layout();
        textPainter.paint(
          canvas,
          // Смещаем метки дат вправо на половину отступа
          Offset(x - textPainter.width / 2 + rightPadding / 2, size.height + 4), // Добавляем половину отступа
        );
      }
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  // Метод для форматирования меток времени в зависимости от таймфрейма
  String formatTimestamp(DateTime date, String timeframe) {
    switch (timeframe) {
      case '30М': // 30 минут
        // Для 30-минутного графика показываем часы и минуты
        return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
      case '1Ч': // 1 час
        // Для часового графика показываем день и час
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')} ${date.hour}:00';
      case '4Ч': // 4 часа
        // Для 4-часового графика показываем день, месяц и час
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')} ${date.hour}:00';
      case '1Д': // 1 день
        // Для дневного графика показываем день и месяц
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}';
      case '1Н': // 1 неделя
        // Для недельного графика показываем месяц и год
        final months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май', 'Июн', 'Июл', 'Авг', 'Сен', 'Окт', 'Ноя', 'Дек'];
        return '${months[date.month - 1]} ${date.year}';
      default:
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}';
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;

  // Вспомогательный метод для выбора равномерно распределенных меток
  List<String> _selectEvenlyDistributedLabels(List<String> allLabels, int count) {
    if (allLabels.length <= count) return allLabels;

    List<String> result = [];
    // Всегда добавляем первую метку
    result.add(allLabels.first);

    // Вычисляем шаг для равномерного распределения
    final step = (allLabels.length - 1) / (count - 1);

    // Добавляем промежуточные метки
    for (int i = 1; i < count - 1; i++) {
      final index = (i * step).round();
      if (index < allLabels.length) {
        result.add(allLabels[index]);
      }
    }

    // Всегда добавляем последнюю метку
    result.add(allLabels.last);

    return result;
  }
}

// Вспомогательный класс для математических операций
class Math {
  static double max(double a, double b) => a > b ? a : b;
  static double min(double a, double b) => a < b ? a : b;
}
