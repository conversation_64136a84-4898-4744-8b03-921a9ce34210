import 'package:flutter/material.dart';

/// Виджет верхней навигационной панели, который будет отображаться на всех экранах
class TopNavigationBar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;

  const TopNavigationBar({
    Key? key,
    required this.selectedIndex,
    required this.onItemSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60, // Увеличиваем высоту для предотвращения переполнения
      decoration: BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade800,
            width: 1,
          ),
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Проверяем ширину экрана и адаптируем навигацию
          final bool isNarrow = constraints.maxWidth < 400;

          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNavItem(0, isNarrow ? 'Все' : 'Все', Icons.apps),
              _buildNavItem(1, isNarrow ? 'Крипто' : 'Крипто', Icons.currency_bitcoin),
              _buildNavItem(2, isNarrow ? 'Акции' : 'Акции', Icons.trending_up),
              if (!isNarrow) _buildNavItem(3, 'Whales', Icons.water),
            ],
          );
        },
      ),
    );
  }

  Widget _buildNavItem(int index, String title, IconData icon) {
    final bool isSelected = selectedIndex == index;

    return InkWell(
      onTap: () => onItemSelected(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4), // Уменьшаем отступы
        decoration: BoxDecoration(
          border: isSelected
              ? const Border(
                  bottom: BorderSide(
                    color: Colors.blue,
                    width: 3,
                  ),
                )
              : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min, // Минимальный размер по вертикали
          mainAxisAlignment: MainAxisAlignment.center, // Центрируем содержимое
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.blue : Colors.grey,
              size: 18, // Уменьшаем размер иконки
            ),
            const SizedBox(height: 2),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey,
                fontSize: 11, // Уменьшаем размер шрифта
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.center, // Центрируем текст
            ),
          ],
        ),
      ),
    );
  }
}
