import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Класс для представления облака
class Cloud {
  /// Позиция облака
  Offset position;
  
  /// Размер облака
  final double size;
  
  /// Скорость движения облака
  final double speed;
  
  /// Непрозрачность облака
  final double opacity;
  
  /// Форма облака (0.0 - 1.0, где 0.0 - круглое, 1.0 - вытянутое)
  final double shape;
  
  Cloud({
    required this.position,
    required this.size,
    required this.speed,
    required this.opacity,
    required this.shape,
  });
  
  /// Обновление позиции облака
  void update(double deltaTime, Size screenSize) {
    // Двигаем облако слева направо
    position = Offset(
      position.dx + speed * deltaTime,
      position.dy,
    );
    
    // Если облако вышло за пределы экрана, возвращаем его в начало
    if (position.dx > screenSize.width + size) {
      position = Offset(
        -size,
        position.dy,
      );
    }
  }
}

/// Виджет для отображения движущихся облаков
class MovingClouds extends StatefulWidget {
  /// Количество облаков
  final int count;
  
  /// Минимальный размер облака
  final double minSize;
  
  /// Максимальный размер облака
  final double maxSize;
  
  /// Минимальная скорость движения облака
  final double minSpeed;
  
  /// Максимальная скорость движения облака
  final double maxSpeed;
  
  /// Минимальная непрозрачность облака
  final double minOpacity;
  
  /// Максимальная непрозрачность облака
  final double maxOpacity;
  
  /// Цвет облаков
  final Color cloudColor;
  
  const MovingClouds({
    super.key,
    this.count = 10,
    this.minSize = 50.0,
    this.maxSize = 200.0,
    this.minSpeed = 5.0,
    this.maxSpeed = 15.0,
    this.minOpacity = 0.1,
    this.maxOpacity = 0.3,
    this.cloudColor = Colors.white,
  });

  @override
  State<MovingClouds> createState() => _MovingCloudsState();
}

class _MovingCloudsState extends State<MovingClouds> with SingleTickerProviderStateMixin {
  /// Список облаков
  late List<Cloud> _clouds;
  
  /// Контроллер анимации
  late AnimationController _controller;
  
  /// Время последнего обновления
  int _lastUpdateTime = 0;
  
  @override
  void initState() {
    super.initState();
    
    // Инициализируем контроллер анимации
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 16), // ~60 FPS
    )..repeat();
    
    // Инициализируем облака
    _initClouds();
    
    // Инициализируем время
    _lastUpdateTime = DateTime.now().millisecondsSinceEpoch;
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  /// Инициализация облаков
  void _initClouds() {
    final random = math.Random();
    _clouds = List.generate(widget.count, (_) {
      // Случайная позиция
      final position = Offset(
        random.nextDouble() * 1.0, // Нормализованная позиция по X (0.0 - 1.0)
        random.nextDouble() * 1.0, // Нормализованная позиция по Y (0.0 - 1.0)
      );
      
      // Случайный размер
      final size = widget.minSize + random.nextDouble() * (widget.maxSize - widget.minSize);
      
      // Случайная скорость
      final speed = widget.minSpeed + random.nextDouble() * (widget.maxSpeed - widget.minSpeed);
      
      // Случайная непрозрачность
      final opacity = widget.minOpacity + random.nextDouble() * (widget.maxOpacity - widget.minOpacity);
      
      // Случайная форма
      final shape = random.nextDouble();
      
      return Cloud(
        position: position,
        size: size,
        speed: speed,
        opacity: opacity,
        shape: shape,
      );
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final size = MediaQuery.of(context).size;
        final currentTime = DateTime.now().millisecondsSinceEpoch;
        final deltaTime = (currentTime - _lastUpdateTime) / 1000.0; // в секундах
        
        // Обновляем облака
        for (var cloud in _clouds) {
          cloud.update(deltaTime, size);
        }
        
        // Обновляем время последнего обновления
        _lastUpdateTime = currentTime;
        
        return CustomPaint(
          size: size,
          painter: CloudsPainter(_clouds, size, widget.cloudColor),
        );
      },
    );
  }
}

/// Painter для отрисовки облаков
class CloudsPainter extends CustomPainter {
  final List<Cloud> clouds;
  final Size screenSize;
  final Color cloudColor;
  
  CloudsPainter(this.clouds, this.screenSize, this.cloudColor);
  
  @override
  void paint(Canvas canvas, Size size) {
    for (var cloud in clouds) {
      // Преобразуем нормализованные координаты в реальные
      final realPosition = Offset(
        cloud.position.dx * screenSize.width,
        cloud.position.dy * screenSize.height,
      );
      
      // Рисуем облако
      _drawCloud(canvas, realPosition, cloud);
    }
  }
  
  /// Рисование облака
  void _drawCloud(Canvas canvas, Offset center, Cloud cloud) {
    // Создаем paint для облака
    final paint = Paint()
      ..color = cloudColor.withOpacity(cloud.opacity)
      ..style = PaintingStyle.fill
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, cloud.size * 0.2);
    
    // Рисуем основную часть облака
    canvas.drawCircle(
      center,
      cloud.size * 0.5,
      paint,
    );
    
    // Рисуем дополнительные части облака
    final random = math.Random(center.dx.toInt() + center.dy.toInt());
    final numParts = 3 + random.nextInt(3); // От 3 до 5 частей
    
    for (var i = 0; i < numParts; i++) {
      final angle = random.nextDouble() * math.pi * 2;
      final distance = cloud.size * 0.3 * random.nextDouble();
      final partSize = cloud.size * (0.3 + random.nextDouble() * 0.4);
      
      final partCenter = Offset(
        center.dx + math.cos(angle) * distance,
        center.dy + math.sin(angle) * distance,
      );
      
      canvas.drawCircle(
        partCenter,
        partSize,
        paint,
      );
    }
    
    // Если облако вытянутое, добавляем дополнительные части
    if (cloud.shape > 0.5) {
      final stretchFactor = 0.5 + cloud.shape * 0.5; // От 0.5 до 1.0
      final stretchAngle = random.nextDouble() * math.pi * 2;
      
      for (var i = 0; i < 2; i++) {
        final angle = stretchAngle + i * math.pi;
        final distance = cloud.size * stretchFactor;
        final partSize = cloud.size * (0.4 + random.nextDouble() * 0.3);
        
        final partCenter = Offset(
          center.dx + math.cos(angle) * distance,
          center.dy + math.sin(angle) * distance,
        );
        
        canvas.drawCircle(
          partCenter,
          partSize,
          paint,
        );
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant CloudsPainter oldDelegate) => true;
}
