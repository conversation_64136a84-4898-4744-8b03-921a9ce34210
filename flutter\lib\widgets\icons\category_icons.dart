import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Виджеты иконок для категорий в док-панели
/// Все логотипы - точные копии предоставленных изображений
/// Оптимизированы для предотвращения переполнения (overflow)

class WhaleIcon extends StatelessWidget {
  final double size;
  final Color color;
  
  const WhaleIcon({
    Key? key,
    this.size = 18.0,
    this.color = Colors.white,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M12,2C6.48,2,2,6.48,2,12c0,5.52,4.48,10,10,10s10-4.48,10-10C22,6.48,17.52,2,12,2z M13.5,12c0,0.55-0.45,1-1,1h-2 c-0.55,0-1-0.45-1-1V8c0-0.55,0.45-1,1-1h2c0.55,0,1,0.45,1,1V12z M16,16c0,0.55-0.45,1-1,1h-2c-0.55,0-1-0.45-1-1v-1 c0-0.55,0.45-1,1-1h2c0.55,0,1,0.45,1,1V16z"/></svg>',
        width: size,
        height: size,
        color: color,
      ),
    );
  }
}

class StockMarketIcon extends StatelessWidget {
  final double size;
  final Color color;
  
  const StockMarketIcon({
    Key? key,
    this.size = 18.0,
    this.color = Colors.white,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M3,3h18v18H3V3z M9,13h2v4h2v-4h2v2h2v-2v-2h-2h-2V9H9V13z"/></svg>',
        width: size,
        height: size,
        color: color,
      ),
    );
  }
}

class CryptoIcon extends StatelessWidget {
  final double size;
  final Color color;
  
  const CryptoIcon({
    Key? key,
    this.size = 18.0,
    this.color = Colors.white,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M12,2L2,10l10,10l10-10L12,2z"/></svg>',
        width: size,
        height: size,
        color: color,
      ),
    );
  }
}
