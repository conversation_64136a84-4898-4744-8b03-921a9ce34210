import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Анимированный индикатор графика роста для положительных новостей
class AnimatedUpwardChartIndicator extends StatefulWidget {
  final double width;
  final double height;

  const AnimatedUpwardChartIndicator({
    Key? key,
    this.width = 30.0,
    this.height = 30.0,
  }) : super(key: key);

  @override
  _AnimatedUpwardChartIndicatorState createState() => _AnimatedUpwardChartIndicatorState();
}

class _AnimatedUpwardChartIndicatorState extends State<AnimatedUpwardChartIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _lineAnimation;
  late Animation<double> _arrowAnimation;
  Timer? _animationTimer;

  @override
  void initState() {
    super.initState();

    // Настройка контроллера анимации с увеличенной длительностью
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    // Анимация рисования линии (первые 80% времени)
    _lineAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller, 
        curve: const Interval(0.0, 0.8, curve: Curves.easeInOut),
      ),
    );
    
    // Анимация появления стрелки (последние 30% времени) - небольшое перекрытие с линией
    _arrowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller, 
        curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
      ),
    );

    // Настройка таймера для запуска анимации каждые 8 секунд
    _animationTimer = Timer.periodic(const Duration(seconds: 8), (_) {
      if (mounted) {
        _controller.reset();
        _controller.forward();
      }
    });

    // Запускаем анимацию сразу для демонстрации
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(4.0),
            border: Border.all(
              color: const Color(0xFF002800), // Темно-зеленая граница
              width: 1.0,
            ),
          ),
          child: CustomPaint(
            painter: AnimatedUpwardChartPainter(
              lineProgress: _lineAnimation.value,
              arrowProgress: _arrowAnimation.value,
            ),
            size: Size(widget.width, widget.height),
          ),
        );
      },
    );
  }
}

/// Кастомный painter для рисования анимированного графика роста
class AnimatedUpwardChartPainter extends CustomPainter {
  final double lineProgress;
  final double arrowProgress;

  AnimatedUpwardChartPainter({
    required this.lineProgress,
    required this.arrowProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    
    // Точки для графика роста (в отличие от падения, здесь график идет вверх)
    final List<Offset> points = [
      Offset(width * 0.15, height * 0.7),  // Начальная точка
      Offset(width * 0.3, height * 0.7),   // Горизонтальная линия
      Offset(width * 0.5, height * 0.5),   // Начало роста
      Offset(width * 0.7, height * 0.3),   // Продолжение роста
      Offset(width * 0.85, height * 0.3),  // Закрепление высокого уровня
    ];
    
    // ========== ЭТАП 1: РИСУЕМ ЛИНИЮ ==========
    if (lineProgress > 0) {
      // Настраиваем стиль линии
      final linePaint = Paint()
        ..color = const Color(0xFF52C41A) // Зеленый цвет для положительного тренда
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5
        ..strokeCap = StrokeCap.round;
      
      // Определяем сколько точек должно быть видимо на основе прогресса
      final double totalPointsToShow = (points.length - 1) * lineProgress;
      final int fullSegments = totalPointsToShow.floor();
      final double partialSegment = totalPointsToShow - fullSegments;
      
      // Если есть хоть какой-то прогресс, рисуем линию
      if (totalPointsToShow > 0) {
        final path = Path();
        path.moveTo(points[0].dx, points[0].dy);
        
        // Рисуем все полные сегменты
        for (int i = 1; i <= fullSegments && i < points.length; i++) {
          path.lineTo(points[i].dx, points[i].dy);
        }
        
        // Рисуем частичный последний сегмент, если он есть
        if (fullSegments < points.length - 1 && partialSegment > 0) {
          final startPoint = points[fullSegments];
          final endPoint = points[fullSegments + 1];
          
          final partialX = startPoint.dx + (endPoint.dx - startPoint.dx) * partialSegment;
          final partialY = startPoint.dy + (endPoint.dy - startPoint.dy) * partialSegment;
          
          path.lineTo(partialX, partialY);
        }
        
        // Рисуем путь на холсте
        canvas.drawPath(path, linePaint);
        
        // Определяем последнюю видимую точку для размещения точки-маркера
        Offset lastVisiblePoint;
        if (fullSegments >= points.length - 1) {
          // Если дошли до конца линии
          lastVisiblePoint = points.last;
        } else {
          // Если рисуем частичный сегмент
          final startPoint = points[fullSegments];
          final endPoint = points[fullSegments + 1];
          
          lastVisiblePoint = Offset(
            startPoint.dx + (endPoint.dx - startPoint.dx) * partialSegment,
            startPoint.dy + (endPoint.dy - startPoint.dy) * partialSegment
          );
        }
        
        // Рисуем точку в конце видимой части линии
        final dotPaint = Paint()
          ..color = const Color(0xFF52C41A)
          ..style = PaintingStyle.fill;
          
        canvas.drawCircle(lastVisiblePoint, 1.5, dotPaint);
      }
    }
    
    // ========== ЭТАП 2: РИСУЕМ СТРЕЛКУ ==========
    if (arrowProgress > 0) {
      // Определяем конечную и предпоследнюю точки для вычисления направления стрелки
      // Для стрелки всегда используем последний сегмент линии, независимо от прогресса линии
      Offset endPoint = points.last;
      Offset prevPoint = points[points.length - 2];
      
      // Вычисляем угол направления стрелки
      final dx = endPoint.dx - prevPoint.dx;
      final dy = endPoint.dy - prevPoint.dy;
      final angle = math.atan2(dy, dx);
      
      // Настраиваем стиль стрелки
      final arrowPaint = Paint()
        ..color = const Color(0xFF52C41A).withOpacity(arrowProgress)
        ..style = PaintingStyle.fill;
      
      // Создаем путь для стрелки
      final arrowPath = Path();
      
      // Размер стрелки (увеличивается с прогрессом анимации)
      final arrowSize = 4.0 * arrowProgress;
      
      // Смещение от конца линии
      final arrowOffset = 2.0;
      
      // Кончик стрелки (с учетом смещения от конца линии)
      final tipPoint = Offset(
        endPoint.dx + math.cos(angle) * arrowOffset,
        endPoint.dy + math.sin(angle) * arrowOffset,
      );
      
      // Точки основания стрелки
      final basePoint1 = Offset(
        tipPoint.dx - math.cos(angle + math.pi/2) * arrowSize - math.cos(angle) * arrowSize,
        tipPoint.dy - math.sin(angle + math.pi/2) * arrowSize - math.sin(angle) * arrowSize,
      );
      
      final basePoint2 = Offset(
        tipPoint.dx - math.cos(angle - math.pi/2) * arrowSize - math.cos(angle) * arrowSize,
        tipPoint.dy - math.sin(angle - math.pi/2) * arrowSize - math.sin(angle) * arrowSize,
      );
      
      // Рисуем треугольник стрелки
      arrowPath.moveTo(tipPoint.dx, tipPoint.dy);
      arrowPath.lineTo(basePoint1.dx, basePoint1.dy);
      arrowPath.lineTo(basePoint2.dx, basePoint2.dy);
      arrowPath.close();
      
      canvas.drawPath(arrowPath, arrowPaint);
    }
  }

  @override
  bool shouldRepaint(covariant AnimatedUpwardChartPainter oldDelegate) {
    return oldDelegate.lineProgress != lineProgress || 
           oldDelegate.arrowProgress != arrowProgress;
  }
}
