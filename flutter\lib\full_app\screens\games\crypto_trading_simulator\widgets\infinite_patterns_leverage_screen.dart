import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../providers/trading_simulator_provider.dart';

class InfinitePatternsLeverageScreen extends StatelessWidget {
  const InfinitePatternsLeverageScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<TradingSimulatorProvider>(context);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black,
            Colors.purple.shade900.withOpacity(0.7),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Infinite Patterns Mode',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ).animate().fadeIn(duration: 500.ms),

              const SizedBox(height: 8),

              const Text(
                'Select your leverage to begin',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ).animate().fadeIn(duration: 500.ms, delay: 200.ms),

              const SizedBox(height: 32),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoCard()
                          .animate()
                          .fadeIn(duration: 500.ms, delay: 300.ms),

                      const SizedBox(height: 32),

                      const Text(
                        'Select Leverage',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ).animate().fadeIn(duration: 500.ms, delay: 400.ms),

                      const SizedBox(height: 16),

                      _buildLeverageGrid(context, provider)
                          .animate()
                          .fadeIn(duration: 500.ms, delay: 500.ms),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: provider.selectedLeverage > 0
                      ? () => provider.startGame()
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 4,
                  ),
                  child: provider.isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                          'Start Trading',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ).animate().fadeIn(duration: 500.ms, delay: 600.ms),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade900.withOpacity(0.7),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.purple.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.all_inclusive,
                  color: Colors.purple,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Infinite Patterns Mode',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoItem(
            icon: Icons.shuffle,
            title: 'Random Cryptocurrencies',
            description: 'Each round features a different cryptocurrency pair.',
          ),
          const SizedBox(height: 12),
          _buildInfoItem(
            icon: Icons.access_time,
            title: 'Random Timeframes',
            description: 'Timeframes change between rounds to test your skills.',
          ),
          const SizedBox(height: 12),
          _buildInfoItem(
            icon: Icons.trending_up,
            title: 'Persistent Balance',
            description: 'Your balance carries over between rounds.',
          ),
          const SizedBox(height: 12),
          _buildInfoItem(
            icon: Icons.bar_chart,
            title: 'Performance Tracking',
            description: 'Track your win rate, streaks, and other statistics.',
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: Colors.purple.shade300,
          size: 18,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLeverageGrid(BuildContext context, TradingSimulatorProvider provider) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.5,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: provider.availableLeverages.length,
      itemBuilder: (context, index) {
        final leverage = provider.availableLeverages[index];
        final isSelected = leverage == provider.selectedLeverage;

        Color cardColor;
        if (leverage <= 5) {
          cardColor = Colors.green;
        } else if (leverage <= 25) {
          cardColor = Colors.orange;
        } else {
          cardColor = Colors.red;
        }

        return InkWell(
          onTap: () => provider.selectLeverage(leverage),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            decoration: BoxDecoration(
              color: isSelected ? cardColor.withOpacity(0.3) : Colors.grey.shade800,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? cardColor : Colors.transparent,
                width: 2,
              ),
            ),
            child: Center(
              child: Text(
                '${leverage}x',
                style: TextStyle(
                  color: isSelected ? cardColor : Colors.white,
                  fontSize: 18,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
