import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentiment_history_model.dart';
import 'stable_market_analytics.dart';
import 'stable_prediction_engine.dart';

/// Стабильный сервис настроений без случайных элементов
/// Интегрирует стабильную аналитику и прогнозирование
class StableSentimentService {
  static const String _historyKey = 'stable_sentiment_history';
  static const String _lastIndicatorKey = 'stable_last_indicator';
  static const String _lastUpdateKey = 'stable_last_update';

  /// Получает текущие метрики и индикатор настроения
  Future<Map<String, dynamic>> getCurrentSentimentData() async {
    debugPrint('=== STABLE SENTIMENT SERVICE ===');
    
    try {
      // Получаем стабильные метрики
      final metrics = await StableMarketAnalytics.fetchStableMetrics();
      
      // Рассчитываем стабильный индикатор
      final indicator = StableMarketAnalytics.calculateStableIndicator(metrics);
      
      // Сохраняем последний индикатор
      await _saveLastIndicator(indicator);
      
      // Преобразуем метрики в формат для UI
      final uiMetrics = _convertMetricsForUI(metrics);
      
      debugPrint('Current stable sentiment: $indicator');
      debugPrint('UI metrics: $uiMetrics');
      debugPrint('================================');
      
      return {
        'indicator': indicator,
        'metrics': uiMetrics,
        'raw_metrics': metrics,
      };
      
    } catch (e) {
      debugPrint('Error getting stable sentiment data: $e');
             return {
         'indicator': 50.0,
         'metrics': _getDefaultUIMetrics(),
         'raw_metrics': _getDefaultUIMetrics(),
       };
    }
  }

  /// Получает стабильные прогнозы для указанного режима
  Future<List<SentimentHistoryEntry>> getPredictions(int daysAhead, {bool isEnhanced = false}) async {
    debugPrint('Getting ${isEnhanced ? 'enhanced' : 'regular'} predictions for $daysAhead days');
    
    try {
      // Получаем текущие данные
      final sentimentData = await getCurrentSentimentData();
      final currentValue = sentimentData['indicator'] as double;
      final rawMetrics = sentimentData['raw_metrics'] as Map<String, double>;
      
      // Генерируем прогнозы в зависимости от режима
      final predictions = isEnhanced
          ? await StablePredictionEngine.generateEnhancedPredictions(currentValue, rawMetrics, daysAhead)
          : await StablePredictionEngine.generateRegularPredictions(currentValue, rawMetrics, daysAhead);
      
      debugPrint('Generated ${predictions.length} ${isEnhanced ? 'enhanced' : 'regular'} predictions');
      return predictions;
      
    } catch (e) {
      debugPrint('Error getting predictions: $e');
      return [];
    }
  }

  /// Получает расширенные технические индикаторы для Enhanced режима
  Map<String, double> getEnhancedMetrics(double currentValue, Map<String, double> rawMetrics) {
    return StableMarketAnalytics.getEnhancedTechnicalIndicators(currentValue, rawMetrics);
  }

  /// Получает последний рассчитанный индикатор
  Future<double?> getLastCalculatedIndicator() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(_lastIndicatorKey);
    } catch (e) {
      debugPrint('Error getting last indicator: $e');
      return null;
    }
  }

  /// Сохраняет текущее значение в историю
  Future<void> saveToHistory(double value, Map<String, double> metrics) async {
    try {
      final today = DateTime.now();
      final entry = SentimentHistoryEntry(
        date: today,
        value: value,
        metrics: metrics,
      );
      
      // Загружаем существующую историю
      final history = await loadHistory();
      
      // Удаляем запись за сегодня, если она есть
      history.removeWhere((e) => 
        e.date.year == today.year &&
        e.date.month == today.month &&
        e.date.day == today.day
      );
      
      // Добавляем новую запись
      history.add(entry);
      
      // Ограничиваем историю 30 днями
      if (history.length > 30) {
        history.removeRange(0, history.length - 30);
      }
      
      // Сохраняем обновленную историю
      await _saveHistory(history);
      
      debugPrint('Saved sentiment history entry: value=$value, date=${today.toIso8601String()}');
      
    } catch (e) {
      debugPrint('Error saving to history: $e');
    }
  }

  /// Загружает историю настроений
  Future<List<SentimentHistoryEntry>> loadHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_historyKey);
      
      if (jsonString == null) {
        debugPrint('No sentiment history found');
        return [];
      }
      
      final jsonList = jsonDecode(jsonString) as List;
      final history = jsonList
          .map((json) => SentimentHistoryEntry.fromJson(json))
          .toList();
      
      // Сортируем по дате
      history.sort((a, b) => a.date.compareTo(b.date));
      
      debugPrint('Loaded ${history.length} sentiment history entries');
      return history;
      
    } catch (e) {
      debugPrint('Error loading sentiment history: $e');
      return [];
    }
  }

  /// Получает вчерашнюю запись
  Future<SentimentHistoryEntry?> getYesterdayEntry() async {
    try {
      final history = await loadHistory();
      if (history.isEmpty) return null;
      
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      
      // Ищем запись за вчера
      for (final entry in history.reversed) {
        if (entry.date.year == yesterday.year &&
            entry.date.month == yesterday.month &&
            entry.date.day == yesterday.day) {
          return entry;
        }
      }
      
      // Если нет записи за вчера, возвращаем последнюю доступную
      return history.isNotEmpty ? history.last : null;
      
    } catch (e) {
      debugPrint('Error getting yesterday entry: $e');
      return null;
    }
  }

  /// Получает запись за прошлую неделю
  Future<SentimentHistoryEntry?> getLastWeekEntry() async {
    try {
      final history = await loadHistory();
      if (history.isEmpty) return null;
      
      final lastWeek = DateTime.now().subtract(const Duration(days: 7));
      
      // Ищем запись за прошлую неделю (±1 день)
      for (final entry in history.reversed) {
        final daysDiff = DateTime.now().difference(entry.date).inDays;
        if (daysDiff >= 6 && daysDiff <= 8) {
          return entry;
        }
      }
      
      // Если нет подходящей записи, возвращаем самую старую
      return history.isNotEmpty ? history.first : null;
      
    } catch (e) {
      debugPrint('Error getting last week entry: $e');
      return null;
    }
  }

  /// Очищает кэш (для тестирования)
  Future<void> clearCache() async {
    try {
      await StableMarketAnalytics.clearCache();
      await StablePredictionEngine.clearCache();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastIndicatorKey);
      await prefs.remove(_lastUpdateKey);
      
      debugPrint('Stable sentiment service cache cleared');
    } catch (e) {
      debugPrint('Error clearing stable sentiment cache: $e');
    }
  }

  /// Преобразует метрики в формат для UI
  Map<String, double> _convertMetricsForUI(Map<String, double> rawMetrics) {
    return {
      'Fear & Greed Index': rawMetrics['fearGreedIndex'] ?? 50.0,
      'Volume Score': rawMetrics['volumeScore'] ?? 50.0,
      'Holders Score': rawMetrics['holdersScore'] ?? 50.0,
      'Social Engagement': rawMetrics['socialEngagement'] ?? 50.0,
      'Price Volatility': rawMetrics['priceVolatility'] ?? 50.0,
      'News Sentiment': rawMetrics['newsSentiment'] ?? 50.0,
      'Bitcoin Dominance': rawMetrics['bitcoinDominance'] ?? 50.0,
    };
  }

  /// Возвращает метрики по умолчанию для UI
  Map<String, double> _getDefaultUIMetrics() {
    return {
      'Fear & Greed Index': 52.3,
      'Volume Score': 54.7,
      'Holders Score': 51.8,
      'Social Engagement': 56.2,
      'Price Volatility': 48.9,
      'News Sentiment': 54.1,
      'Bitcoin Dominance': 53.6,
    };
  }

  /// Сохраняет последний индикатор
  Future<void> _saveLastIndicator(double indicator) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_lastIndicatorKey, indicator);
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('Error saving last indicator: $e');
    }
  }

  /// Сохраняет историю
  Future<void> _saveHistory(List<SentimentHistoryEntry> history) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = history.map((entry) => entry.toJson()).toList();
      await prefs.setString(_historyKey, jsonEncode(jsonList));
    } catch (e) {
      debugPrint('Error saving sentiment history: $e');
    }
  }
} 