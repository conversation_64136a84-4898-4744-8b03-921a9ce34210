import 'package:flutter/material.dart';
import '../models/trading_simulator_models.dart';
import 'dart:math' as math;

/// A candlestick chart widget for the trading simulators
class SimulatorCandlestickChart extends StatefulWidget {
  final List<CandleData> candles;
  final Color upColor;
  final Color downColor;
  final bool showGrid;
  final bool showLabels;
  final double? height;
  final double? width;
  final CandleData? highlightedCandle;
  final bool showFutureCandles;
  final int visibleCandleCount;
  final bool showEntryCrosshair;
  final double zoomFactor;
  final double verticalZoomFactor;
  final bool showEntryMarkers;
  final double? entryPrice;
  final bool centerLastCandle;
  final double? dragOffset;
  final ValueChanged<double>? onDragOffsetChanged;

  const SimulatorCandlestickChart({
    super.key,
    required this.candles,
    this.upColor = Colors.green,
    this.downColor = Colors.red,
    this.showGrid = true,
    this.showLabels = true,
    this.height,
    this.width,
    this.highlightedCandle,
    this.showFutureCandles = false,
    this.visibleCandleCount = 100,
    this.showEntryCrosshair = true,
    this.zoomFactor = 2.0, // Default to 200% zoom
    this.verticalZoomFactor = 1.0, // Default to normal vertical zoom
    this.showEntryMarkers = false,
    this.entryPrice,
    this.centerLastCandle = false,
    this.dragOffset,
    this.onDragOffsetChanged,
  });
  
  // Factory constructor for results screen
  factory SimulatorCandlestickChart.forResults({
    required List<CandleData> candles,
    Color upColor = Colors.green,
    Color downColor = Colors.red,
    double? height,
    double? width,
    CandleData? highlightedCandle,
    int visibleCandleCount = 100,
    bool showEntryMarkers = false,
    double? entryPrice,
  }) {
    return SimulatorCandlestickChart(
      candles: candles,
      upColor: upColor,
      downColor: downColor,
      height: height,
      width: width,
      highlightedCandle: highlightedCandle,
      showFutureCandles: false,
      visibleCandleCount: visibleCandleCount,
      showEntryCrosshair: true,
      zoomFactor: 0.5, // 50% zoom (отдаление)
      verticalZoomFactor: 1.3, // 130% вертикальное приближение
      showEntryMarkers: showEntryMarkers,
      entryPrice: entryPrice,
      centerLastCandle: true, // Enable centering for results screen
    );
  }

  @override
  State<SimulatorCandlestickChart> createState() => _SimulatorCandlestickChartState();
}

class _SimulatorCandlestickChartState extends State<SimulatorCandlestickChart> {
  double _dragOffset = 0.0;
  double _maxOffset = 0.0;
  double _minOffset = 0.0;
  double _contentWidth = 0.0;
  double _candleWidth = 1.0;
  double _spacing = 1.0;
  int _displayCount = 1;

  @override
  void didUpdateWidget(covariant SimulatorCandlestickChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.dragOffset != null && widget.dragOffset != _dragOffset) {
      _dragOffset = widget.dragOffset!;
    }
  }

  void _updateOffsets(Size size) {
    final visibleCount = (widget.visibleCandleCount / widget.zoomFactor).round();
    final displayCount = widget.candles.length < visibleCount ? widget.candles.length : visibleCount;
    _displayCount = displayCount;
    _candleWidth = size.width / displayCount;
    _spacing = 0.0;
    _contentWidth = displayCount * _candleWidth;
    // Ограничения для dragOffset
    if (_contentWidth <= size.width) {
      _minOffset = _maxOffset = (size.width - _contentWidth) / 2;
      _dragOffset = _minOffset;
    } else {
      _minOffset = size.width - _contentWidth; // крайняя правая свеча у правого края
      _maxOffset = 0.0; // крайняя левая свеча у левого края
      _dragOffset = _dragOffset.clamp(_minOffset, _maxOffset);
    }
  }

  @override
  Widget build(BuildContext context) {
    print('candles: [36m${widget.candles.length}[0m, first: [36m${widget.candles.isNotEmpty ? widget.candles.first : 'none'}[0m');
    if (widget.candles.isEmpty) {
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.black,
        child: const Center(
          child: Text(
            'No data available',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        final visibleCount = widget.visibleCandleCount;
        final totalCount = widget.candles.length;
        // Добавляем пустые бары справа, чтобы последняя свеча была по центру
        final emptyBars = visibleCount ~/ 2;
        final extendedCandles = [
          ...widget.candles,
          ...List<CandleData?>.filled(emptyBars, null),
        ];
        final candleWidth = constraints.maxWidth / visibleCount;
        final contentWidth = extendedCandles.length * candleWidth;
        // dragOffset не ограничиваем (свободный скролл)
        final dragOffset = widget.dragOffset ?? 0.0;
        return GestureDetector(
          onHorizontalDragUpdate: (details) {
            final newOffset = dragOffset + details.delta.dx;
            if (widget.onDragOffsetChanged != null) {
              widget.onDragOffsetChanged!(newOffset);
            }
          },
          child: ClipRect(
            child: Container(
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                size: Size.infinite,
                painter: _CandlestickChartPainter(
                  candles: extendedCandles,
                  upColor: const Color(0xFF4ADE80), // зелёный для тёмной темы
                  downColor: const Color(0xFFF87171), // красный для тёмной темы
                  showGrid: widget.showGrid,
                  showLabels: widget.showLabels,
                  highlightedCandle: widget.highlightedCandle,
                  showFutureCandles: widget.showFutureCandles,
                  visibleCandleCount: visibleCount,
                  showEntryCrosshair: widget.showEntryCrosshair,
                  zoomFactor: widget.zoomFactor,
                  verticalZoomFactor: widget.verticalZoomFactor,
                  showEntryMarkers: widget.showEntryMarkers,
                  entryPrice: widget.entryPrice,
                  centerLastCandle: false, // теперь всегда используем empty bars
                  dragOffset: dragOffset,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _CandlestickChartPainter extends CustomPainter {
  final List<CandleData?> candles;
  final Color upColor;
  final Color downColor;
  final bool showGrid;
  final bool showLabels;
  final CandleData? highlightedCandle;
  final bool showFutureCandles;
  final int visibleCandleCount;
  final bool showEntryCrosshair;
  final double zoomFactor;
  final double verticalZoomFactor;
  final bool showEntryMarkers;
  final double? entryPrice;
  final bool centerLastCandle;
  final double dragOffset;

  _CandlestickChartPainter({
    required this.candles,
    required this.upColor,
    required this.downColor,
    required this.showGrid,
    required this.showLabels,
    this.highlightedCandle,
    required this.showFutureCandles,
    required this.visibleCandleCount,
    this.showEntryCrosshair = true,
    this.zoomFactor = 2.0,
    this.verticalZoomFactor = 1.0,
    this.showEntryMarkers = false,
    this.entryPrice,
    this.centerLastCandle = false,
    required this.dragOffset,
  });

  double _calculateTranslateX({
    required double sizeWidth,
    required double contentWidth,
    required double candleWidth,
    required double spacing,
    required int displayCandlesLength,
    required bool centerLastCandle,
  }) {
    // Если график короче контейнера — центрируем
    if (contentWidth <= sizeWidth) {
      return (sizeWidth - contentWidth) / 2;
    }
    // Если график длиннее — последняя свеча строго у правой границы (не выходит за пределы)
    return (sizeWidth - contentWidth).clamp(double.negativeInfinity, 0.0);
  }

  @override
  void paint(Canvas canvas, Size size) {
    if (candles.isEmpty) return;

    // Determine which candles to display
    final List<CandleData?> displayCandles;
    if (showFutureCandles) {
      displayCandles = candles;
    } else {
      final endIndex = candles.length;
      // Adjust the visible count based on zoom factor
      final adjustedVisibleCount = (visibleCandleCount / zoomFactor).round();
      final startIndex = endIndex - adjustedVisibleCount;
      displayCandles = candles.sublist(
        startIndex > 0 ? startIndex : 0,
        endIndex,
      );
    }
    // Если все свечи null или первая свеча null — не рисуем ничего
    if (displayCandles.isEmpty || displayCandles.where((c) => c != null).isEmpty || displayCandles.first == null) return;
    // Для режима centerLastCandle добавить пустые бары справа, чтобы последняя свеча была по центру
    List<CandleData?> extendedCandles = List<CandleData?>.from(displayCandles);
    if (centerLastCandle) {
      final candleWidth = size.width / (extendedCandles.length + 10); // оценка
      final lastCandleX = extendedCandles.length * candleWidth;
      final centerX = size.width / 2;
      final targetX = centerX;
      int emptyBars = ((targetX - lastCandleX) / candleWidth).ceil();
      if (emptyBars < 0) emptyBars = 0;
      extendedCandles.addAll(List<CandleData?>.filled(emptyBars, null));
    }
    final candleWidth = size.width / extendedCandles.length;
    final spacing = 0.0;
    final contentWidth = extendedCandles.length * candleWidth;
    double translateX = dragOffset;
    if (centerLastCandle) {
      final lastCandleX = (extendedCandles.length - 1) * candleWidth;
      final centerX = size.width / 2;
      final targetX = centerX;
      translateX = targetX - lastCandleX;
    }
    canvas.save();
    canvas.translate(translateX, 0);

    // --- ВАЖНО: диапазон цен только по реальным свечам ---
    double minPrice = double.infinity;
    double maxPrice = -double.infinity;
    for (final candle in displayCandles) {
      if (candle == null) continue;
      if (candle.low < minPrice) minPrice = candle.low;
      if (candle.high > maxPrice) maxPrice = candle.high;
    }
    if (minPrice == double.infinity || maxPrice == -double.infinity) {
      canvas.restore();
      return;
    }
    final priceRange = maxPrice - minPrice;
    final priceMidpoint = minPrice + priceRange / 2;
    final zoomedPriceRange = priceRange / verticalZoomFactor;
    minPrice = priceMidpoint - zoomedPriceRange / 2;
    maxPrice = priceMidpoint + zoomedPriceRange / 2;
    final adjustedPriceRange = maxPrice - minPrice;
    final paddingFactor = 0.1;
    maxPrice += adjustedPriceRange * paddingFactor;
    minPrice -= adjustedPriceRange * paddingFactor;
    final finalPriceRange = maxPrice - minPrice;

    // Draw grid lines
    if (showGrid) {
      _drawGrid(canvas, size, minPrice, maxPrice, contentWidth);
    }

    // Draw price labels
    if (showLabels) {
      _drawPriceLabels(canvas, size, minPrice, maxPrice);
    }

    // Draw entry price horizontal line if specified
    if (showEntryMarkers && entryPrice != null) {
      final entryY = size.height - ((entryPrice! - minPrice) / finalPriceRange * size.height);
      _drawEntryLine(canvas, entryY, contentWidth, entryPrice!);
    }

    CandleData? lastVisibleCandle;
    for (int i = 0; i < extendedCandles.length; i++) {
      final candle = extendedCandles[i];
      final x = i * (candleWidth + spacing);
      if (candle == null) {
        // Пустой бар — ничего не рисуем
        continue;
      }
      // Track the last visible non-future candle
      if (!showFutureCandles && i == displayCandles.length - 1) {
        lastVisibleCandle = candle;
      }
      // Convert prices to y-coordinates using finalPriceRange
      final highY = size.height - ((candle.high - minPrice) / finalPriceRange * size.height);
      final lowY = size.height - ((candle.low - minPrice) / finalPriceRange * size.height);
      final openY = size.height - ((candle.open - minPrice) / finalPriceRange * size.height);
      final closeY = size.height - ((candle.close - minPrice) / finalPriceRange * size.height);

      // Determine candle color
      final color = candle.isGreen ? upColor : downColor;

      // Check if this is a future candle (only relevant when showFutureCandles is true)
      final isFutureCandle = showFutureCandles && 
          highlightedCandle != null && 
          candle.time.isAfter(highlightedCandle!.time);

      // Draw highlighted candle with a different appearance
      if (highlightedCandle != null && candle.time == highlightedCandle!.time) {
        // Draw highlighted candle
        _drawHighlightedCandle(
          canvas, 
          x, 
          candleWidth, 
          highY, 
          lowY, 
          openY, 
          closeY, 
          color,
        );
        
        // Draw entry arrow above the highlighted candle if needed
        if (showEntryMarkers) {
          _drawEntryArrow(canvas, x + candleWidth / 2, highY - 15, color);
        }
      } else if (isFutureCandle) {
        // Draw future candle with transparency
        _drawFutureCandle(
          canvas, 
          x, 
          candleWidth, 
          highY, 
          lowY, 
          openY, 
          closeY, 
          color,
        );
      } else {
        // Draw regular candle
        _drawCandle(
          canvas, 
          x, 
          candleWidth, 
          highY, 
          lowY, 
          openY, 
          closeY, 
          color,
        );
      }
    }
    
    // Draw crosshair for entry point on last visible candle
    if (showEntryCrosshair && !showFutureCandles && lastVisibleCandle != null) {
      final index = displayCandles.indexOf(lastVisibleCandle);
      final x = index * (candleWidth + spacing) + candleWidth / 2;
      final closeY = size.height - ((lastVisibleCandle.close - minPrice) / finalPriceRange * size.height);
      
      _drawCrosshair(canvas, x, closeY, size, lastVisibleCandle.isGreen ? upColor : downColor);
    }
    
    canvas.restore();
  }

  void _drawCrosshair(Canvas canvas, double x, double y, Size size, Color color) {
    final crosshairPaint = Paint()
      ..color = Colors.white.withOpacity(0.4)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;
      
    // Draw horizontal line
    canvas.drawLine(
      Offset(0, y),
      Offset(size.width, y),
      crosshairPaint,
    );
    
    // Draw vertical line
    canvas.drawLine(
      Offset(x, 0),
      Offset(x, size.height),
      crosshairPaint,
    );
    
    // Draw center dot (полупрозрачный белый контур, без цветной точки)
    final dotPaint = Paint()
      ..color = Colors.white.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    canvas.drawCircle(Offset(x, y), 5.0, dotPaint);
    
    // Draw outer ring for better visibility (тоже полупрозрачный)
    final outerRingPaint = Paint()
      ..color = Colors.white.withOpacity(0.25)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    canvas.drawCircle(Offset(x, y), 8.0, outerRingPaint);
    // Не рисуем надпись ENTRY и цветную точку
  }

  void _drawGrid(Canvas canvas, Size size, double minPrice, double maxPrice, double contentWidth) {
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Use the smaller of content width or actual size width to prevent drawing outside boundaries
    final visibleWidth = math.min(contentWidth, size.width);

    // Draw horizontal grid lines
    final numHorizontalLines = 5;
    for (int i = 0; i <= numHorizontalLines; i++) {
      final y = size.height / numHorizontalLines * i;
      canvas.drawLine(
        Offset(0, y),
        Offset(visibleWidth, y),
        gridPaint,
      );
    }

    // Draw vertical grid lines
    final numVerticalLines = 6;
    for (int i = 0; i <= numVerticalLines; i++) {
      final x = visibleWidth / numVerticalLines * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
  }

  void _drawPriceLabels(Canvas canvas, Size size, double minPrice, double maxPrice) {
    final textStyle = TextStyle(
      color: Colors.grey[400],
      fontSize: 10,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Draw price labels on the left side (previously on the right side)
    final numLabels = 5;
    for (int i = 0; i <= numLabels; i++) {
      final y = size.height / numLabels * i;
      final price = maxPrice - (maxPrice - minPrice) * (i / numLabels);
      
      // Format price based on its magnitude
      String priceText;
      if (price >= 1000) {
        priceText = price.toStringAsFixed(0);
      } else if (price >= 1) {
        priceText = price.toStringAsFixed(2);
      } else {
        priceText = price.toStringAsFixed(price < 0.001 ? 6 : 4);
      }
      
      textPainter.text = TextSpan(
        text: priceText,
        style: textStyle,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(4, y - textPainter.height / 2), // Changed from right to left side
      );
    }
  }

  void _drawCandle(
    Canvas canvas,
    double x,
    double candleWidth,
    double highY,
    double lowY,
    double openY,
    double closeY,
    Color color,
  ) {
    // Draw wick (vertical line from high to low)
    final wickPaint = Paint()
      ..color = color
      ..strokeWidth = 1.0;
    
    canvas.drawLine(
      Offset(x + candleWidth / 2, highY),
      Offset(x + candleWidth / 2, lowY),
      wickPaint,
    );

    // Draw body (rectangle from open to close)
    final bodyPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    final bodyTop = openY < closeY ? openY : closeY;
    final bodyBottom = openY < closeY ? closeY : openY;
    final bodyHeight = (bodyBottom - bodyTop).abs();

    canvas.drawRect(
      Rect.fromLTWH(x, bodyTop, candleWidth, bodyHeight),
      bodyPaint,
    );
  }

  void _drawHighlightedCandle(
    Canvas canvas,
    double x,
    double candleWidth,
    double highY,
    double lowY,
    double openY,
    double closeY,
    Color color,
  ) {
    // Draw wick with thicker line
    final wickPaint = Paint()
      ..color = color
      ..strokeWidth = 2.0;
    
    canvas.drawLine(
      Offset(x + candleWidth / 2, highY),
      Offset(x + candleWidth / 2, lowY),
      wickPaint,
    );

    // Draw body with border
    final bodyPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    final bodyTop = openY < closeY ? openY : closeY;
    final bodyBottom = openY < closeY ? closeY : openY;
    final bodyHeight = (bodyBottom - bodyTop).abs();

    // Draw body
    canvas.drawRect(
      Rect.fromLTWH(x, bodyTop, candleWidth, bodyHeight),
      bodyPaint,
    );
    
    // Draw border around body
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    
    canvas.drawRect(
      Rect.fromLTWH(x, bodyTop, candleWidth, bodyHeight),
      borderPaint,
    );
  }

  void _drawFutureCandle(
    Canvas canvas,
    double x,
    double candleWidth,
    double highY,
    double lowY,
    double openY,
    double closeY,
    Color color,
  ) {
    // Draw wick with semi-transparent color
    final wickPaint = Paint()
      ..color = color.withOpacity(0.5)
      ..strokeWidth = 1.0;
    
    canvas.drawLine(
      Offset(x + candleWidth / 2, highY),
      Offset(x + candleWidth / 2, lowY),
      wickPaint,
    );

    // Draw body with semi-transparent color
    final bodyPaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    
    final bodyTop = openY < closeY ? openY : closeY;
    final bodyBottom = openY < closeY ? closeY : openY;
    final bodyHeight = (bodyBottom - bodyTop).abs();

    canvas.drawRect(
      Rect.fromLTWH(x, bodyTop, candleWidth, bodyHeight),
      bodyPaint,
    );
  }

  // Draw entry horizontal line with price label
  void _drawEntryLine(Canvas canvas, double y, double width, double price) {
    final linePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    // Limit width to visible area
    final visibleWidth = math.min(width, 10000); // Use a large enough number as we're in a clipped context
    
    // Draw dashed line
    final dashWidth = 8.0;
    final dashSpace = 4.0;
    double startX = 0;
    
    while (startX < visibleWidth) {
      canvas.drawLine(
        Offset(startX, y),
        Offset(startX + dashWidth, y),
        linePaint,
      );
      startX += dashWidth + dashSpace;
    }
    
    // Draw price label
    final textStyle = TextStyle(
      color: Colors.white,
      backgroundColor: Colors.black45,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );
    
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );
    
    // Format price
    String priceText;
    if (price >= 1000) {
      priceText = price.toStringAsFixed(0);
    } else if (price >= 1) {
      priceText = price.toStringAsFixed(2);
    } else {
      priceText = price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
    
    textPainter.text = TextSpan(
      text: "ENTRY: $priceText",
      style: textStyle,
    );
    
    textPainter.layout();
    
    // Ensure label is visible within the chart area
    double labelX = visibleWidth - textPainter.width - 8;
    if (labelX < 4) labelX = 4; // Minimum padding from left edge
    
    textPainter.paint(
      canvas,
      Offset(labelX, y - textPainter.height - 4),
    );
  }
  
  // Draw arrow pointing to entry candle
  void _drawEntryArrow(Canvas canvas, double x, double y, Color color) {
    final arrowPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    // Draw arrow head
    final path = Path();
    path.moveTo(x, y);
    path.lineTo(x - 8, y - 12);
    path.lineTo(x + 8, y - 12);
    path.close();
    
    canvas.drawPath(path, arrowPaint);
    
    // Draw arrow outline
    final outlinePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    
    canvas.drawPath(path, outlinePaint);
    
    // Draw "ENTRY" text above arrow
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );
    
    textPainter.text = TextSpan(
      text: "ENTRY",
      style: TextStyle(
        color: Colors.white,
        fontSize: 10,
        fontWeight: FontWeight.bold,
        backgroundColor: Colors.black54,
      ),
    );
    
    textPainter.layout();
    
    // Center text above arrow
    final textX = x - textPainter.width / 2;
    final textY = y - 32; // Position above arrow
    
    textPainter.paint(canvas, Offset(textX, textY));
  }

  @override
  bool shouldRepaint(_CandlestickChartPainter oldDelegate) {
    return oldDelegate.candles != candles ||
        oldDelegate.upColor != upColor ||
        oldDelegate.downColor != downColor ||
        oldDelegate.highlightedCandle != highlightedCandle ||
        oldDelegate.showFutureCandles != showFutureCandles ||
        oldDelegate.zoomFactor != zoomFactor ||
        oldDelegate.showEntryCrosshair != showEntryCrosshair ||
        oldDelegate.showEntryMarkers != showEntryMarkers ||
        oldDelegate.entryPrice != entryPrice ||
        oldDelegate.centerLastCandle != centerLastCandle ||
        oldDelegate.dragOffset != dragOffset;
  }
}
