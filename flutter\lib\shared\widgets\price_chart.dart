import 'package:flutter/material.dart';
import '../models/crypto_currency.dart';
import 'dart:math' as math;

class Price<PERSON>hart extends StatelessWidget {
  final CryptoCurrency crypto;
  final String timeInterval;

  const PriceChart({
    super.key,
    required this.crypto,
    required this.timeInterval,
  });

  @override
  Widget build(BuildContext context) {
    // Filter price points based on selected time interval
    final filteredPoints = _getFilteredPricePoints();
    
    if (filteredPoints.isEmpty) {
      return const Center(child: Text('No data available for this time interval'));
    }

    // Calculate min and max values for scaling
    double minPrice = filteredPoints.map((p) => p.price).reduce(math.min);
    double maxPrice = filteredPoints.map((p) => p.price).reduce(math.max);
    
    // Add some padding to min and max
    final padding = (maxPrice - minPrice) * 0.1;
    minPrice -= padding;
    maxPrice += padding;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Price and change
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '\$${crypto.price.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      Icon(
                        crypto.priceChangePercentage24h >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                        color: crypto.priceChangePercentage24h >= 0 ? Colors.green : Colors.red,
                        size: 16,
                      ),
                      Text(
                        '${crypto.priceChangePercentage24h.abs().toStringAsFixed(2)}%',
                        style: TextStyle(
                          color: crypto.priceChangePercentage24h >= 0 ? Colors.green : Colors.red,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              // Favorite button
              IconButton(
                icon: Icon(
                  crypto.isFavorite ? Icons.star : Icons.star_border,
                  color: crypto.isFavorite ? Colors.amber : Colors.grey,
                ),
                onPressed: () {
                  // Toggle favorite status (would be implemented with state management)
                },
              ),
            ],
          ),
        ),
        
        // Chart
        Container(
          height: 200,
          padding: const EdgeInsets.all(16.0),
          child: CustomPaint(
            size: const Size(double.infinity, 200),
            painter: ChartPainter(
              pricePoints: filteredPoints,
              minPrice: minPrice,
              maxPrice: maxPrice,
              lineColor: crypto.priceChangePercentage24h >= 0 ? Colors.green : Colors.red,
            ),
          ),
        ),
        
        // Time interval selector
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildIntervalButton(context, '1h'),
              _buildIntervalButton(context, '24h'),
              _buildIntervalButton(context, '7d'),
              _buildIntervalButton(context, '30d'),
              _buildIntervalButton(context, '1y'),
              _buildIntervalButton(context, 'All'),
            ],
          ),
        ),
        
        // Market info
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildInfoRow('Market Cap', '\$${_formatLargeNumber(crypto.marketCap)}'),
              const SizedBox(height: 8),
              _buildInfoRow('24h Volume', '\$${_formatLargeNumber(crypto.volume24h)}'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildIntervalButton(BuildContext context, String interval) {
    final isSelected = timeInterval == interval;
    
    return ElevatedButton(
      onPressed: () {
        // Change time interval (would be implemented with state management)
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Theme.of(context).primaryColor : Colors.grey[200],
        foregroundColor: isSelected ? Colors.white : Colors.black,
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
      ),
      child: Text(interval),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 14.0,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14.0,
          ),
        ),
      ],
    );
  }

  List<PricePoint> _getFilteredPricePoints() {
    // In a real app, this would filter based on the actual time interval
    // For this mock, we'll just return a subset of the data
    switch (timeInterval) {
      case '1h':
        return crypto.priceHistory.take(12).toList();
      case '24h':
        return crypto.priceHistory.skip(12).take(24).toList();
      case '7d':
        return crypto.priceHistory.skip(36).take(7).toList();
      case '30d':
        return crypto.priceHistory.skip(43).take(30).toList();
      case '1y':
      case 'All':
      default:
        return crypto.priceHistory;
    }
  }

  String _formatLargeNumber(double number) {
    if (number >= 1000000000) {
      return '${(number / 1000000000).toStringAsFixed(2)}B';
    } else if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(2)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(2)}K';
    } else {
      return number.toString();
    }
  }
}

class ChartPainter extends CustomPainter {
  final List<PricePoint> pricePoints;
  final double minPrice;
  final double maxPrice;
  final Color lineColor;

  ChartPainter({
    required this.pricePoints,
    required this.minPrice,
    required this.maxPrice,
    required this.lineColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = lineColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final path = Path();
    
    // Draw the line chart
    for (int i = 0; i < pricePoints.length; i++) {
      final x = size.width * i / (pricePoints.length - 1);
      final y = size.height - (pricePoints[i].price - minPrice) / (maxPrice - minPrice) * size.height;
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    
    canvas.drawPath(path, paint);
    
    // Draw area under the line
    final fillPath = Path.from(path);
    fillPath.lineTo(size.width, size.height);
    fillPath.lineTo(0, size.height);
    fillPath.close();
    
    final fillPaint = Paint()
      ..color = lineColor.withOpacity(0.2)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(fillPath, fillPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
