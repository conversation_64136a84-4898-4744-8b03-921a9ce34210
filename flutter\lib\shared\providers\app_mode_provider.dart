import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppMode { full, lite }

class AppModeProvider extends ChangeNotifier {
  AppMode _currentMode = AppMode.full;
  bool _isInitialized = false;

  AppMode get currentMode => _currentMode;
  bool get isLiteMode => _currentMode == AppMode.lite;
  bool get isFullMode => _currentMode == AppMode.full;
  bool get isInitialized => _isInitialized;

  // Инициализация провайдера
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedMode = prefs.getString('app_mode') ?? 'full';
      _currentMode = savedMode == 'lite' ? AppMode.lite : AppMode.full;
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Ошибка инициализации AppModeProvider: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  // Переключение режима
  Future<void> switchMode(bool toLite) async {
    final newMode = toLite ? AppMode.lite : AppMode.full;
    
    if (newMode == _currentMode) return;

    try {
      // Сохраняем выбор пользователя
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('app_mode', toLite ? 'lite' : 'full');
      
      // Показываем индикатор загрузки
      _showModeChangeIndicator();
      
      // Имитируем перезагрузку приложения
      await Future.delayed(const Duration(milliseconds: 500));
      
      _currentMode = newMode;
      notifyListeners();
      
      // Вибрация для подтверждения
      HapticFeedback.lightImpact();
      
    } catch (e) {
      debugPrint('Ошибка переключения режима: $e');
    }
  }

  // Быстрое переключение без сохранения (для тестирования)
  void toggleModeTemporary() {
    _currentMode = _currentMode == AppMode.full ? AppMode.lite : AppMode.full;
    notifyListeners();
    HapticFeedback.selectionClick();
  }

  // Установка режима без сохранения
  void setModeTemporary(AppMode mode) {
    if (_currentMode != mode) {
      _currentMode = mode;
      notifyListeners();
    }
  }

  // Сброс к режиму по умолчанию
  Future<void> resetToDefault() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('app_mode');
      _currentMode = AppMode.full;
      notifyListeners();
    } catch (e) {
      debugPrint('Ошибка сброса режима: $e');
    }
  }

  // Получение описания текущего режима
  String get currentModeDescription {
    switch (_currentMode) {
      case AppMode.full:
        return 'Полная версия с расширенным функционалом';
      case AppMode.lite:
        return 'Упрощенная версия для мобильных устройств';
    }
  }

  // Получение иконки текущего режима
  IconData get currentModeIcon {
    switch (_currentMode) {
      case AppMode.full:
        return Icons.desktop_windows;
      case AppMode.lite:
        return Icons.phone_android;
    }
  }

  // Получение цвета текущего режима
  Color get currentModeColor {
    switch (_currentMode) {
      case AppMode.full:
        return const Color(0xFF6366F1); // Primary blue
      case AppMode.lite:
        return const Color(0xFF10B981); // Success green
    }
  }

  // Проверка, поддерживается ли функция в текущем режиме
  bool isFeatureAvailable(String feature) {
    if (_currentMode == AppMode.full) return true;
    
    // Список функций, доступных в lite режиме
    const liteFeatures = [
      'market_view',
      'news_basic',
      'portfolio_basic',
      'price_alerts',
      'favorites',
      'basic_charts',
    ];
    
    return liteFeatures.contains(feature);
  }

  // Получение списка недоступных функций в lite режиме
  List<String> get unavailableFeaturesInLite => [
    'advanced_charts',
    'trading_simulator',
    'learning_materials',
    'advanced_analytics',
    'custom_indicators',
    'portfolio_analytics',
    'news_sentiment_analysis',
    'market_predictions',
  ];

  void _showModeChangeIndicator() {
    // Здесь можно добавить логику показа индикатора загрузки
    // Например, через overlay или глобальный state
    debugPrint('Переключение режима приложения...');
  }
}

// Миксин для виджетов, которые зависят от режима приложения
mixin AppModeMixin<T extends StatefulWidget> on State<T> {
  AppModeProvider? _appModeProvider;

  AppModeProvider get appMode => _appModeProvider!;
  bool get isLiteMode => appMode.isLiteMode;
  bool get isFullMode => appMode.isFullMode;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _appModeProvider = context.read<AppModeProvider>();
  }

  // Проверка доступности функции
  bool isFeatureAvailable(String feature) {
    return appMode.isFeatureAvailable(feature);
  }

  // Показ сообщения о недоступности функции
  void showFeatureUnavailableMessage(String featureName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '$featureName доступно только в Full режиме',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.orange,
        action: SnackBarAction(
          label: 'Переключить',
          textColor: Colors.white,
          onPressed: () {
            appMode.switchMode(false);
          },
        ),
      ),
    );
  }
}

// Виджет-обертка для условного отображения контента
class AppModeBuilder extends StatelessWidget {
  final Widget? fullModeChild;
  final Widget? liteModeChild;
  final Widget? fallback;
  final String? requiredFeature;

  const AppModeBuilder({
    Key? key,
    this.fullModeChild,
    this.liteModeChild,
    this.fallback,
    this.requiredFeature,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppModeProvider>(
      builder: (context, appMode, child) {
        // Если указана требуемая функция, проверяем её доступность
        if (requiredFeature != null && !appMode.isFeatureAvailable(requiredFeature!)) {
          return fallback ?? const SizedBox.shrink();
        }

        // Возвращаем соответствующий виджет в зависимости от режима
        if (appMode.isLiteMode && liteModeChild != null) {
          return liteModeChild!;
        }
        
        if (appMode.isFullMode && fullModeChild != null) {
          return fullModeChild!;
        }

        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}
