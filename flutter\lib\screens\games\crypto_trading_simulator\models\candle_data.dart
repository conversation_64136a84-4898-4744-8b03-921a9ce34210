class CandleData {
  final double time;
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;

  CandleData({
    required this.time,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });

  factory CandleData.fromJson(List<dynamic> json) {
    return CandleData(
      time: json[0] / 1000, // Convert milliseconds to seconds for chart library
      open: double.parse(json[1]),
      high: double.parse(json[2]),
      low: double.parse(json[3]),
      close: double.parse(json[4]),
      volume: double.parse(json[5]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'time': time,
      'open': open,
      'high': high,
      'low': low,
      'close': close,
      'volume': volume,
    };
  }

  // Convert to format expected by lightweight_charts
  Map<String, dynamic> toLightweightChartFormat() {
    return {
      'time': time,
      'open': open,
      'high': high,
      'low': low,
      'close': close,
    };
  }
}
