import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_mode_provider.dart';
import '../styles/app_colors.dart';

class AppModeSwitcher extends StatelessWidget {
  final bool showInDrawer;
  final bool showAsFloatingButton;

  const AppModeSwitcher({
    Key? key,
    this.showInDrawer = false,
    this.showAsFloatingButton = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppModeProvider>(
      builder: (context, appMode, child) {
        if (showAsFloatingButton) {
          return _buildFloatingButton(context, appMode);
        }
        
        if (showInDrawer) {
          return _buildDrawerItem(context, appMode);
        }
        
        return _buildInlineSwitch(context, appMode);
      },
    );
  }

  Widget _buildFloatingButton(BuildContext context, AppModeProvider appMode) {
    return Positioned(
      top: 50,
      right: 16,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.backgroundLight,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: _buildSwitchContent(context, appMode),
      ),
    );
  }

  Widget _buildDrawerItem(BuildContext context, AppModeProvider appMode) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Режим приложения',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          _buildSwitchContent(context, appMode),
        ],
      ),
    );
  }

  Widget _buildInlineSwitch(BuildContext context, AppModeProvider appMode) {
    return _buildSwitchContent(context, appMode);
  }

  Widget _buildSwitchContent(BuildContext context, AppModeProvider appMode) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildModeButton(
            context,
            appMode,
            isLite: false,
            icon: Icons.desktop_windows,
            label: 'Full',
            isSelected: !appMode.isLiteMode,
          ),
          _buildModeButton(
            context,
            appMode,
            isLite: true,
            icon: Icons.phone_android,
            label: 'Lite',
            isSelected: appMode.isLiteMode,
          ),
        ],
      ),
    );
  }

  Widget _buildModeButton(
    BuildContext context,
    AppModeProvider appMode, {
    required bool isLite,
    required IconData icon,
    required String label,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        if (isLite != appMode.isLiteMode) {
          _showSwitchDialog(context, appMode, isLite);
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.textPrimary : AppColors.textSecondary,
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppColors.textPrimary : AppColors.textSecondary,
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSwitchDialog(BuildContext context, AppModeProvider appMode, bool toLite) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.backgroundLight,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              toLite ? Icons.phone_android : Icons.desktop_windows,
              color: AppColors.primary,
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              'Переключить на ${toLite ? 'Lite' : 'Full'} режим?',
              style: const TextStyle(
                color: AppColors.textPrimary,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              toLite 
                ? 'Lite режим оптимизирован для мобильных устройств с упрощенным интерфейсом и базовым функционалом.'
                : 'Full режим включает все функции: расширенные чарты, торговый симулятор, обучающие материалы.',
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.surface.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.info,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Приложение будет перезапущено',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Отмена',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              appMode.switchMode(toLite);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textPrimary,
            ),
            child: Text(toLite ? 'Переключить на Lite' : 'Переключить на Full'),
          ),
        ],
      ),
    );
  }
}

// Floating Action Button версия
class AppModeFAB extends StatelessWidget {
  const AppModeFAB({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppModeProvider>(
      builder: (context, appMode, child) {
        return FloatingActionButton.extended(
          onPressed: () {
            _showQuickSwitchBottomSheet(context, appMode);
          },
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textPrimary,
          icon: Icon(
            appMode.isLiteMode ? Icons.phone_android : Icons.desktop_windows,
          ),
          label: Text(appMode.isLiteMode ? 'Lite Mode' : 'Full Mode'),
        );
      },
    );
  }

  void _showQuickSwitchBottomSheet(BuildContext context, AppModeProvider appMode) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppColors.backgroundLight,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.textTertiary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Режим приложения',
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildModeCard(
                    context,
                    appMode,
                    isLite: false,
                    icon: Icons.desktop_windows,
                    title: 'Full Mode',
                    description: 'Все функции\nДля desktop/web',
                    isSelected: !appMode.isLiteMode,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildModeCard(
                    context,
                    appMode,
                    isLite: true,
                    icon: Icons.phone_android,
                    title: 'Lite Mode',
                    description: 'Упрощенный\nДля мобильных',
                    isSelected: appMode.isLiteMode,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildModeCard(
    BuildContext context,
    AppModeProvider appMode, {
    required bool isLite,
    required IconData icon,
    required String title,
    required String description,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        if (isLite != appMode.isLiteMode) {
          appMode.switchMode(isLite);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withOpacity(0.1) : AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.surface,
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primary : AppColors.textTertiary,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                icon,
                color: AppColors.textPrimary,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? AppColors.primary : AppColors.textPrimary,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
