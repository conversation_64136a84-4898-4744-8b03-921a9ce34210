import 'package:flutter/material.dart';
import '../models/crypto_currency.dart';

class CryptoCarousel extends StatelessWidget {
  final List<CryptoCurrency> cryptos;
  final int selectedIndex;
  final Function(int) onCryptoSelected;

  const CryptoCarousel({
    super.key,
    required this.cryptos,
    required this.selectedIndex,
    required this.onCryptoSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: cryptos.length,
        itemBuilder: (context, index) {
          final crypto = cryptos[index];
          final isSelected = index == selectedIndex;
          
          return GestureDetector(
            onTap: () => onCryptoSelected(index),
            child: Container(
              width: 120,
              margin: const EdgeInsets.symmetric(horizontal: 8.0),
              decoration: BoxDecoration(
                color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.transparent,
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(
                  color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
                  width: 2.0,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.network(
                        crypto.imageUrl,
                        width: 24,
                        height: 24,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(Icons.currency_bitcoin, size: 24);
                        },
                      ),
                      const SizedBox(width: 4.0),
                      Text(
                        crypto.symbol,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Theme.of(context).primaryColor : Colors.black,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4.0),
                  Text(
                    '\$${crypto.price.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? Theme.of(context).primaryColor : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4.0),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        crypto.priceChangePercentage24h >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                        color: crypto.priceChangePercentage24h >= 0 ? Colors.green : Colors.red,
                        size: 12,
                      ),
                      Text(
                        '${crypto.priceChangePercentage24h.abs().toStringAsFixed(2)}%',
                        style: TextStyle(
                          color: crypto.priceChangePercentage24h >= 0 ? Colors.green : Colors.red,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
