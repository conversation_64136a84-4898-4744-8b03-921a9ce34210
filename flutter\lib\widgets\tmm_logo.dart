import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class TmmLogo extends StatelessWidget {
  final double size;
  final Color color;

  const TmmLogo({
    super.key,
    this.size = 100,
    this.color = Colors.black,
  });

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      'assets/images/tmm_logo.svg',
      width: size,
      height: size,
      colorFilter: color != Colors.black
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }
}
