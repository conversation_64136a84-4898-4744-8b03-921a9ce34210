import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/page_transitions.dart';
import '../screens/news_screen.dart';
import '../screens/charts_screen.dart';
import '../screens/reactor_sinusoid_screen.dart';
import '../screens/courses_screen.dart';
import '../screens/profile_screen.dart';

/// Улучшенный обработчик навигации с красивыми анимациями
class EnhancedNavigationHandler {
  
  /// Обрабатывает навигацию с красивыми анимациями в зависимости от типа перехода
  static void handleNavigation(
    BuildContext context,
    int currentIndex,
    int targetIndex, {
    bool useCustomAnimations = true,
  }) {
    if (currentIndex == targetIndex) return;

    // Добавляем тактильную обратную связь
    HapticFeedback.lightImpact();

    Widget targetScreen;
    PageRouteBuilder Function(Widget) transitionBuilder;

    // Определяем целевой экран
    switch (targetIndex) {
      case 0:
        targetScreen = const NewsScreen();
        break;
      case 1:
        targetScreen = const ChartsScreen();
        break;
      case 2:
        targetScreen = const ReactorSinusoidScreen();
        break;
      case 3:
        targetScreen = const CoursesScreen();
        break;
      case 4:
        targetScreen = const ProfileScreen();
        break;
      default:
        return;
    }

    if (!useCustomAnimations) {
      // Стандартная навигация без анимаций
      Navigator.pushReplacementNamed(context, _getRouteName(targetIndex));
      return;
    }

    // Выбираем тип анимации в зависимости от направления навигации
    if (targetIndex > currentIndex) {
      // Движение вправо - элегантный слайд
      transitionBuilder = PageTransitions.elegantSlide;
    } else if (targetIndex < currentIndex) {
      // Движение влево - плавное масштабирование
      transitionBuilder = PageTransitions.smoothScale;
    } else {
      // Центральная навигация - мягкое затухание
      transitionBuilder = PageTransitions.softFade;
    }

    // Специальные анимации для определенных переходов
    if (targetIndex == 2) {
      // Sinusoid экран - элегантный поворот
      transitionBuilder = PageTransitions.elegantRotation;
    } else if (currentIndex == 2) {
      // Выход из Sinusoid экрана - переворот
      transitionBuilder = PageTransitions.flipTransition;
    } else if (targetIndex == 4 || currentIndex == 4) {
      // Profile экран - плавное масштабирование
      transitionBuilder = PageTransitions.smoothScale;
    }

    // Выполняем навигацию с анимацией
    Navigator.pushReplacement(
      context,
      transitionBuilder(targetScreen),
    );
  }

  /// Обрабатывает навигацию с определенным типом анимации
  static void handleNavigationWithAnimation(
    BuildContext context,
    int targetIndex,
    AnimationType animationType,
  ) {
    HapticFeedback.lightImpact();

    Widget targetScreen;
    PageRouteBuilder Function(Widget) transitionBuilder;

    // Определяем целевой экран
    switch (targetIndex) {
      case 0:
        targetScreen = const NewsScreen();
        break;
      case 1:
        targetScreen = const ChartsScreen();
        break;
      case 2:
        targetScreen = const ReactorSinusoidScreen();
        break;
      case 3:
        targetScreen = const CoursesScreen();
        break;
      case 4:
        targetScreen = const ProfileScreen();
        break;
      default:
        return;
    }

    // Выбираем анимацию
    switch (animationType) {
      case AnimationType.elegantSlide:
        transitionBuilder = PageTransitions.elegantSlide;
        break;
      case AnimationType.smoothSlideUp:
        transitionBuilder = PageTransitions.smoothSlideUp;
        break;
      case AnimationType.smoothScale:
        transitionBuilder = PageTransitions.smoothScale;
        break;
      case AnimationType.softFade:
        transitionBuilder = PageTransitions.softFade;
        break;
      case AnimationType.elegantRotation:
        transitionBuilder = PageTransitions.elegantRotation;
        break;
      case AnimationType.flipTransition:
        transitionBuilder = PageTransitions.flipTransition;
        break;
    }

    Navigator.pushReplacement(
      context,
      transitionBuilder(targetScreen),
    );
  }

  /// Получает имя маршрута по индексу
  static String _getRouteName(int index) {
    switch (index) {
      case 0:
        return '/news';
      case 1:
        return '/charts';
      case 2:
        return '/sinusoid';
      case 3:
        return '/courses';
      case 4:
        return '/profile';
      default:
        return '/news';
    }
  }

  /// Создает красивую анимацию появления для модальных окон
  static Future<T?> showAnimatedModal<T>(
    BuildContext context,
    Widget child, {
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) {
    return showGeneralDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? Colors.black54,
      barrierLabel: barrierLabel,
      transitionDuration: const Duration(milliseconds: 350),
      pageBuilder: (context, animation, secondaryAnimation) => child,
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        var scaleAnimation = Tween<double>(
          begin: 0.85,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutBack,
        ));

        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOut,
        ));

        var slideAnimation = Tween<Offset>(
          begin: const Offset(0.0, 0.1),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        ));

        return FadeTransition(
          opacity: fadeAnimation,
          child: SlideTransition(
            position: slideAnimation,
            child: ScaleTransition(
              scale: scaleAnimation,
              child: child,
            ),
          ),
        );
      },
    );
  }

  /// Создает красивую анимацию для bottom sheet
  static Future<T?> showAnimatedBottomSheet<T>(
    BuildContext context,
    Widget child, {
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: Colors.transparent,
      transitionAnimationController: AnimationController(
        duration: const Duration(milliseconds: 400),
        vsync: Navigator.of(context),
      ),
      builder: (context) => Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: child,
      ),
    );
  }
}

/// Типы анимаций для навигации
enum AnimationType {
  elegantSlide,
  smoothSlideUp,
  smoothScale,
  softFade,
  elegantRotation,
  flipTransition,
}

/// Расширение для BuildContext для удобного использования
extension NavigationExtensions on BuildContext {
  
  /// Навигация с автоматическим выбором анимации
  void navigateToTab(int currentIndex, int targetIndex) {
    EnhancedNavigationHandler.handleNavigation(this, currentIndex, targetIndex);
  }

  /// Навигация с определенной анимацией
  void navigateWithAnimation(int targetIndex, AnimationType animationType) {
    EnhancedNavigationHandler.handleNavigationWithAnimation(
      this,
      targetIndex,
      animationType,
    );
  }

  /// Показать анимированное модальное окно
  Future<T?> showAnimatedModal<T>(Widget child) {
    return EnhancedNavigationHandler.showAnimatedModal<T>(this, child);
  }

  /// Показать анимированный bottom sheet
  Future<T?> showAnimatedBottomSheet<T>(Widget child) {
    return EnhancedNavigationHandler.showAnimatedBottomSheet<T>(this, child);
  }
} 