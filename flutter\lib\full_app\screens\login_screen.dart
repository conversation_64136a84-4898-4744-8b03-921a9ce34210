import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import '../providers/auth_provider.dart';
import 'hyperjump_animation_screen.dart';
import '../widgets/hover_button.dart';
import '../config/design_system.dart';
import '../widgets/glassmorphic_card.dart';
import '../widgets/auth_decorative_pane.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  final bool _isLogin = true; // Login mode
  bool _isPasswordVisible = false;

  late AnimationController _starsController;
  final List<Star> _stars = [];
  final List<Color> _starColors = [
    Colors.white,
    Colors.white.withBlue(240),
    Colors.white.withRed(240),
    Colors.white.withGreen(240),
  ];

  @override
  void initState() {
    super.initState();

    // Создаем звезды для фона (увеличиваем количество до 200)
    for (int i = 0; i < 200; i++) {
      _stars.add(Star(
        x: math.Random().nextDouble() * 1.0,
        y: math.Random().nextDouble() * 1.0,
        size: math.Random().nextDouble() * 3.0 + 1.0, // Увеличиваем размер звезд
        blinkDuration: (math.Random().nextDouble() * 1.5 + 1.5) * 3000, // Замедляем мерцание в 3 раза
        color: _starColors[math.Random().nextInt(_starColors.length)], // Добавляем разные цвета
      ));
    }

    // Инициализируем контроллер анимации для звезд с замедленной анимацией
    _starsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 9000), // Замедляем анимацию в 3 раза (3000 * 3 = 9000)
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _starsController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (_isLogin) {
      // Login
      final success = await authProvider.login(
        _emailController.text.trim(),
        _passwordController.text.trim(),
      );

      if (success && mounted) {
        // Переход к анимации гиперпрыжка
        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      }
    } else {
      // Register
      final success = await authProvider.register(
        _emailController.text.split('@')[0], // Simple username from email
        _emailController.text.trim(),
        _passwordController.text.trim(),
      );

      if (success && mounted) {
        // Переход к анимации гиперпрыжка
        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      }
    }
  }

  Future<void> _loginWithSocial(String provider) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    bool success = false;

    // Вызываем соответствующий метод в зависимости от провайдера
    switch (provider) {
      case 'google':
        success = _isLogin
            ? await authProvider.loginWithGoogle()
            : await authProvider.registerWithGoogle();
        break;
      case 'facebook':
        success = _isLogin
            ? await authProvider.loginWithFacebook()
            : await authProvider.registerWithFacebook();
        break;
      case 'linkedin':
        success = _isLogin
            ? await authProvider.loginWithLinkedIn()
            : await authProvider.registerWithLinkedIn();
        break;
    }

    if (success && mounted) {
      // Переход к анимации гиперпрыжка
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final size = MediaQuery.of(context).size;
    // Calculate a size that fits on the screen without scrolling
    final cardHeight = math.min(size.height * 0.85, 650.0); // Reduced height to ensure buttons fit
    final cardWidth = math.min(size.width * 0.9, 820.0);

    return Scaffold(
      backgroundColor: const Color(0xFF22242B),
      body: Stack(
        children: [
          // Основной контент
          Center(
            child: SingleChildScrollView(
              child: Container(
                width: cardWidth,
                constraints: BoxConstraints(
                  maxHeight: cardHeight,
                ),
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF2A2C32).withOpacity(0.95),
                      const Color(0xFF23252B).withOpacity(0.9),
                      const Color(0xFF1B1D22).withOpacity(0.85),
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.15),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.4),
                      blurRadius: 30,
                      offset: const Offset(0, 15),
                      spreadRadius: -5,
                    ),
                    BoxShadow(
                      color: const Color(0xFF0F3460).withOpacity(0.3),
                      blurRadius: 60,
                      offset: const Offset(0, 30),
                      spreadRadius: -10,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Decorative side
                    if (cardWidth > 600)
                      const Expanded(
                        flex: 4,
                        child: AuthDecorativePane(),
                      ),
                    // Gap between panes
                    if (cardWidth > 600) const SizedBox(width: 24),
                    // Form side
                    Expanded(
                      flex: 5,
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Заголовок формы
                            Column(
                              children: [
                                Text(
                                  'Welcome back',
                                  style: DesignSystem.headingL.copyWith(
                                    fontSize: 28,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.white,
                                  ),
                                ).animate().fadeIn(duration: 500.ms, delay: 200.ms),

                                const SizedBox(height: 8),

                                Text(
                                  'Sign in to your account',
                                  style: DesignSystem.bodyM.copyWith(
                                    color: DesignSystem.textTertiary,
                                  ),
                                ).animate().fadeIn(duration: 500.ms, delay: 300.ms),
                              ],
                            ),

                            const SizedBox(height: 32),

                            // Форма с авторизацией
                            Form(
                              key: _formKey,
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  // Поле для email
                                  _ModernTextField(
                                    controller: _emailController,
                                    hintText: 'Email',
                                    keyboardType: TextInputType.emailAddress,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter your email';
                                      }
                                      if (!value.contains('@')) {
                                        return 'Please enter a valid email';
                                      }
                                      return null;
                                    },
                                  ).animate().fadeIn(duration: 500.ms, delay: 400.ms),

                                  const SizedBox(height: 20),

                                  // Поле для пароля
                                  _ModernTextField(
                                    controller: _passwordController,
                                    hintText: 'Password',
                                    isPassword: true,
                                    isPasswordVisible: _isPasswordVisible,
                                    onTogglePasswordVisibility: () {
                                      setState(() {
                                        _isPasswordVisible = !_isPasswordVisible;
                                      });
                                    },
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter your password';
                                      }
                                      if (value.length < 6) {
                                        return 'Password must be at least 6 characters';
                                      }
                                      return null;
                                    },
                                  ).animate().fadeIn(duration: 500.ms, delay: 600.ms),

                                  const SizedBox(height: 24),

                                  // Кнопка входа
                                  _ModernButton(
                                    text: 'Sign in to your account',
                                    isLoading: authProvider.isLoading,
                                    onPressed: _submit,
                                  ).animate().fadeIn(duration: 500.ms, delay: 700.ms),

                                  // Сообщение об ошибке
                                  if (authProvider.error.isNotEmpty)
                                    Container(
                                      margin: const EdgeInsets.only(top: 8),
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.red.shade50,
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(color: Colors.red.shade200),
                                      ),
                                      child: Text(
                                        authProvider.error,
                                        style: TextStyle(
                                          color: Colors.red.shade700,
                                          fontSize: 14,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ).animate().fadeIn(duration: 300.ms).shake(hz: 4),
                                ],
                              ),
                            ),

                            const SizedBox(height: 20),

                            // Разделитель
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 20),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Container(
                                      height: 1,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            Colors.transparent,
                                            Colors.white.withOpacity(0.2),
                                            Colors.transparent,
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 16),
                                    child: Text(
                                      'OR',
                                      style: DesignSystem.bodyS.copyWith(
                                        color: DesignSystem.textTertiary,
                                        fontWeight: FontWeight.w500,
                                        letterSpacing: 1.2,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(
                                      height: 1,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            Colors.transparent,
                                            Colors.white.withOpacity(0.2),
                                            Colors.transparent,
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ).animate().fadeIn(duration: 500.ms, delay: 800.ms),

                            // Кнопки социальных сетей
                            Column(
                              children: [
                                _ModernSocialButton(
                                  icon: Icons.g_mobiledata,
                                  text: 'Continue with Google',
                                  onPressed: () => _loginWithSocial('google'),
                                ).animate().fadeIn(duration: 500.ms, delay: 900.ms),

                                const SizedBox(height: 12),

                                _ModernSocialButton(
                                  icon: Icons.close,
                                  text: 'Continue with X',
                                  onPressed: () => _loginWithSocial('x'),
                                ).animate().fadeIn(duration: 500.ms, delay: 1000.ms),
                              ],
                            ),

                            // Ссылка на регистрацию
                            Padding(
                              padding: const EdgeInsets.only(top: 24),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Don\'t have an account? ',
                                    style: DesignSystem.bodyS.copyWith(
                                      color: DesignSystem.textTertiary,
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      Navigator.pushNamed(context, '/register');
                                    },
                                    child: Text(
                                      'Sign up',
                                      style: DesignSystem.bodyS.copyWith(
                                        color: const Color(0xFF00F5A0),
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ).animate().fadeIn(duration: 500.ms, delay: 1100.ms),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ).animate().fadeIn(duration: 800.ms),
            ),
          ),
        ],
      ),
    );
  }
}

// Современное поле ввода
class _ModernTextField extends StatefulWidget {
  final TextEditingController controller;
  final String hintText;
  final TextInputType? keyboardType;
  final bool isPassword;
  final bool isPasswordVisible;
  final VoidCallback? onTogglePasswordVisibility;
  final String? Function(String?)? validator;

  const _ModernTextField({
    Key? key,
    required this.controller,
    required this.hintText,
    this.keyboardType,
    this.isPassword = false,
    this.isPasswordVisible = false,
    this.onTogglePasswordVisibility,
    this.validator,
  }) : super(key: key);

  @override
  State<_ModernTextField> createState() => _ModernTextFieldState();
}

class _ModernTextFieldState extends State<_ModernTextField> {
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isFocused
            ? Colors.white.withOpacity(0.4)
            : Colors.white.withOpacity(0.1),
          width: 1,
        ),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF2C2F36),
            const Color(0xFF24262C),
          ],
        ),
      ),
      child: TextFormField(
        controller: widget.controller,
        focusNode: _focusNode,
        keyboardType: widget.keyboardType,
        obscureText: widget.isPassword && !widget.isPasswordVisible,
        style: DesignSystem.bodyL.copyWith(color: Colors.white),
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: DesignSystem.bodyL.copyWith(
            color: Colors.white.withOpacity(0.5),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          suffixIcon: widget.isPassword
            ? IconButton(
                icon: Icon(
                  widget.isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                  color: Colors.white.withOpacity(0.6),
                  size: 20,
                ),
                onPressed: widget.onTogglePasswordVisibility,
              )
            : null,
        ),
        validator: widget.validator,
      ),
    );
  }
}

// Современная кнопка
class _ModernButton extends StatefulWidget {
  final String text;
  final bool isLoading;
  final VoidCallback onPressed;

  const _ModernButton({
    Key? key,
    required this.text,
    required this.isLoading,
    required this.onPressed,
  }) : super(key: key);

  @override
  State<_ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<_ModernButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: _isHovered
              ? [
                  const Color(0xFF6B6F7B),
                  const Color(0xFF555962),
                ]
              : [
                  const Color(0xFF5C616D),
                  const Color(0xFF484C55),
                ],
            stops: const [0.0, 1.0],
          ),
          border: Border.all(
            color: _isHovered ? Colors.black.withOpacity(0.8) : Colors.black.withOpacity(0.6),
            width: 1.4,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
            if (_isHovered)
              BoxShadow(
                color: const Color(0xFF16213E).withOpacity(0.4),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: widget.isLoading ? null : widget.onPressed,
            child: Center(
              child: widget.isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    widget.text,
                    style: DesignSystem.bodyL.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
            ),
          ),
        ),
      ),
    );
  }
}

// Современная социальная кнопка
class _ModernSocialButton extends StatefulWidget {
  final IconData icon;
  final String text;
  final VoidCallback onPressed;

  const _ModernSocialButton({
    Key? key,
    required this.icon,
    required this.text,
    required this.onPressed,
  }) : super(key: key);

  @override
  State<_ModernSocialButton> createState() => _ModernSocialButtonState();
}

class _ModernSocialButtonState extends State<_ModernSocialButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _isHovered
              ? Colors.black.withOpacity(0.7)
              : Colors.black.withOpacity(0.5),
            width: 1.2,
          ),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: _isHovered
              ? [
                  Colors.black.withOpacity(0.7),
                  Colors.black.withOpacity(0.5),
                ]
              : [
                  Colors.black.withOpacity(0.5),
                  Colors.black.withOpacity(0.3),
                ],
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: widget.onPressed,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  widget.icon,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(
                  widget.text,
                  style: DesignSystem.bodyM.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Класс для представления звезды
class Star {
  final double x;
  final double y;
  final double size;
  final double blinkDuration;
  final Color color;

  Star({
    required this.x,
    required this.y,
    required this.size,
    required this.blinkDuration,
    this.color = Colors.white,
  });
}

// Класс для представления кометы
class Comet {
  double x;
  double y;
  double speed;
  double angle;
  double tailLength;
  double size;
  double opacity;
  DateTime lastAppearance;
  int nextAppearanceSeconds; // Время до следующего появления в секундах
  bool isActive; // Флаг активности кометы (находится ли она на экране)

  Comet({
    required this.x,
    required this.y,
    required this.speed,
    required this.angle,
    required this.tailLength,
    required this.size,
  }) :
    opacity = 0.0,
    isActive = false,
    lastAppearance = DateTime.now().subtract(const Duration(seconds: 3)),
    nextAppearanceSeconds = 8 + math.Random().nextInt(8); // Инициализируем случайным значением от 8 до 15

  // Генерируем случайное время появления кометы (от 8 до 15 секунд)
  int _getRandomAppearanceTime() {
    return 8 + math.Random().nextInt(8); // от 8 до 15 секунд
  }

  // Генерируем случайное положение кометы, гарантируя, что она будет видна на экране
  void _setRandomPosition(Size canvasSize) {
    // Выбираем случайную сторону для появления кометы
    int side = math.Random().nextInt(4);

    // Гарантируем, что комета будет пересекать видимую часть экрана
    switch (side) {
      case 0: // Слева
        x = -tailLength;
        // Гарантируем, что комета пройдет через видимую часть экрана
        y = canvasSize.height * 0.2 + math.Random().nextDouble() * canvasSize.height * 0.6;
        // Направление вправо с небольшими вариациями, чтобы комета точно пересекла экран
        angle = -math.pi / 6 + math.Random().nextDouble() * math.pi / 3;
        break;
      case 1: // Справа
        x = canvasSize.width + tailLength;
        y = canvasSize.height * 0.2 + math.Random().nextDouble() * canvasSize.height * 0.6;
        // Направление влево с небольшими вариациями
        angle = math.pi - math.pi / 6 + math.Random().nextDouble() * math.pi / 3;
        break;
      case 2: // Сверху
        x = canvasSize.width * 0.2 + math.Random().nextDouble() * canvasSize.width * 0.6;
        y = -tailLength;
        // Направление вниз с небольшими вариациями
        angle = math.pi / 2 - math.pi / 6 + math.Random().nextDouble() * math.pi / 3;
        break;
      case 3: // Снизу
        x = canvasSize.width * 0.2 + math.Random().nextDouble() * canvasSize.width * 0.6;
        y = canvasSize.height + tailLength;
        // Направление вверх с небольшими вариациями
        angle = -math.pi / 2 - math.pi / 6 + math.Random().nextDouble() * math.pi / 3;
        break;
    }

    // Устанавливаем оптимальную скорость для пересечения экрана
    // Более быстрая скорость для больших экранов
    double baseDiagonal = math.sqrt(math.pow(canvasSize.width, 2) + math.pow(canvasSize.height, 2));
    speed = baseDiagonal / 150 + math.Random().nextDouble() * 2.0; // Скорость зависит от размера экрана
  }

  void update(Size canvasSize, DateTime now) {
    // Проверяем, должна ли появиться новая комета
    final secondsSinceLastAppearance = now.difference(lastAppearance).inSeconds;

    if (secondsSinceLastAppearance >= nextAppearanceSeconds) {
      // Создаем новую комету
      isActive = true;
      opacity = 0.0;
      _setRandomPosition(canvasSize);
      lastAppearance = now;
      // Устанавливаем время до следующего появления
      nextAppearanceSeconds = _getRandomAppearanceTime();
    }

    if (isActive) {
      // Обновляем позицию кометы
      x += speed * math.cos(angle);
      y += speed * math.sin(angle);

      // Определяем, находится ли комета в пределах видимой части экрана
      bool isInVisibleArea =
          (x + tailLength > 0 && x - tailLength < canvasSize.width) &&
          (y + tailLength > 0 && y - tailLength < canvasSize.height);

      // Быстрое появление при входе в видимую область
      if (isInVisibleArea && opacity < 1.0) {
        opacity = math.min(1.0, opacity + 0.1); // Быстрое появление
      }

      // Быстрое исчезновение при выходе из видимой области
      if (!isInVisibleArea && opacity > 0.0) {
        opacity = math.max(0.0, opacity - 0.1); // Быстрое исчезновение
      }

      // Проверяем, полностью ли комета вышла за пределы экрана с запасом
      if (x < -tailLength * 2 ||
          x > canvasSize.width + tailLength * 2 ||
          y < -tailLength * 2 ||
          y > canvasSize.height + tailLength * 2) {
        isActive = false; // Комета больше не активна
      }
    }
  }

  // Метод для проверки, видима ли комета в данный момент
  bool isVisible() {
    return isActive && opacity > 0.0;
  }
}

// Кастомный painter для отрисовки звезд и кометы
class StarsPainter extends CustomPainter {
  final List<Star> stars;
  final double animationValue;
  static final Comet _comet = Comet(
    x: -100, // Начальная позиция за пределами экрана
    y: 100,
    speed: 10.0, // Скорость движения
    angle: math.pi * 0.15, // Угол движения (немного вниз)
    tailLength: 150.0, // Длина хвоста
    size: 3.0, // Размер головы кометы
  );

  StarsPainter(this.stars, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // Обновляем состояние кометы
    _comet.update(size, DateTime.now());

    // Рисуем звезды
    for (var star in stars) {
      // Вычисляем текущую прозрачность звезды (усиливаем мерцание)
      final opacity = 0.1 + (0.9 - 0.1) *
          (math.sin(2 * math.pi * (animationValue + star.x * star.y) % 1) * 0.5 + 0.5);

      // Используем цвет звезды вместо белого по умолчанию
      paint.color = star.color.withAlpha((opacity * 255).toInt());

      // Добавляем эффект свечения для больших звезд
      if (star.size > 2.5) {
        // Рисуем свечение
        final glowPaint = Paint()
          ..color = star.color.withAlpha((opacity * 100).toInt())
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

        canvas.drawCircle(
          Offset(star.x * size.width, star.y * size.height),
          star.size * 1.8,
          glowPaint,
        );
      }

      // Рисуем саму звезду
      canvas.drawCircle(
        Offset(star.x * size.width, star.y * size.height),
        star.size,
        paint,
      );
    }

    // Рисуем комету, если она видима
    if (_comet.isVisible()) {
      _drawComet(canvas, _comet);
    }
  }

  // Метод для отрисовки кометы
  void _drawComet(Canvas canvas, Comet comet) {
    // Создаем градиент для хвоста кометы
    final tailGradient = ui.Gradient.linear(
      Offset(comet.x, comet.y),
      Offset(comet.x - comet.tailLength * math.cos(comet.angle),
             comet.y - comet.tailLength * math.sin(comet.angle)),
      [
        Colors.white.withAlpha((comet.opacity * 255).toInt()),
        Colors.white.withAlpha((comet.opacity * 0.8 * 255).toInt()),
        Colors.blue.withAlpha((comet.opacity * 0.6 * 255).toInt()),
        Colors.transparent,
      ],
      [0.0, 0.3, 0.6, 1.0],
    );

    // Рисуем хвост кометы
    final tailPaint = Paint()
      ..shader = tailGradient
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    final path = Path();
    path.moveTo(comet.x, comet.y);
    path.lineTo(
      comet.x - comet.tailLength * math.cos(comet.angle),
      comet.y - comet.tailLength * math.sin(comet.angle),
    );

    canvas.drawPath(path, tailPaint);

    // Рисуем свечение вокруг головы кометы
    final glowPaint = Paint()
      ..color = Colors.white.withAlpha((comet.opacity * 0.7 * 255).toInt())
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0);

    canvas.drawCircle(
      Offset(comet.x, comet.y),
      comet.size * 2.5,
      glowPaint,
    );

    // Рисуем голову кометы
    final headPaint = Paint()
      ..color = Colors.white.withAlpha((comet.opacity * 255).toInt());

    canvas.drawCircle(
      Offset(comet.x, comet.y),
      comet.size,
      headPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

