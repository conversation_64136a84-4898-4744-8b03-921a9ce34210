import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../config/design_system.dart';

class BottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const BottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Define colors from the design spec
    const Color navBarBackground = Color(0xFF1C1C1E);
    const Color inactiveColor = Color(0xFF8E8E93);
    const Color activeColor = Color(0xFF4CAF50); // Green accent color
    const double navBarHeight = 60.0;
    
    return Container(
      height: navBarHeight + MediaQuery.of(context).padding.bottom,
      decoration: const BoxDecoration(
        color: navBarBackground,
        border: Border(
          top: BorderSide(
            color: Color(0xFF3A3A3C),
            width: 0.5, // Thinner border
          ),
        ),
      ),
      child: Safe<PERSON>rea(
        top: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(
              icon: Icons.newspaper_outlined,
              label: 'News',
              index: 0,
              activeColor: activeColor,
              inactiveColor: inactiveColor,
            ),
            _buildNavItem(
              icon: Icons.bar_chart_outlined,
              label: 'Charts',
              index: 1,
              activeColor: activeColor,
              inactiveColor: inactiveColor,
            ),
            _buildNavItem(
              icon: Icons.waves_outlined,
              label: 'Sinusoid',
              index: 2,
              activeColor: activeColor,
              inactiveColor: inactiveColor,
            ),
            _buildNavItem(
              icon: Icons.school_outlined,
              label: 'Learn',
              index: 3,
              activeColor: activeColor,
              inactiveColor: inactiveColor,
            ),
            _buildNavItem(
              icon: Icons.person_outline,
              label: 'Profile',
              index: 4,
              activeColor: activeColor,
              inactiveColor: inactiveColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
    required Color activeColor,
    required Color inactiveColor,
  }) {
    final bool isActive = currentIndex == index;
    
    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: SizedBox(
        height: 60,
        width: 70, // Slightly wider for better spacing
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Active indicator line
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: isActive ? 2 : 0,
              width: 20,
              margin: const EdgeInsets.only(bottom: 4),
              decoration: BoxDecoration(
                color: activeColor,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
            
            // Icon with animations
            Icon(
              icon,
              size: 24,
              color: isActive ? activeColor : inactiveColor,
            )
            .animate(target: isActive ? 1 : 0)
            .scale(
              begin: const Offset(1.0, 1.0),
              end: const Offset(1.1, 1.1),
              duration: 300.ms,
              curve: Curves.easeOutBack,
            )
            .fadeIn(
              duration: 200.ms,
            ),
            
            const SizedBox(height: 4),
            
            // Text with animations
            Text(
              label,
              style: TextStyle(
                fontFamily: DesignSystem.fontFamily,
                fontSize: 10,
                fontWeight: isActive ? DesignSystem.medium : DesignSystem.regular,
                color: isActive ? activeColor : inactiveColor,
              ),
            )
            .animate(target: isActive ? 1 : 0)
            .fadeIn(
              duration: 300.ms,
            ),
          ],
        ),
      ),
    );
  }
} 