import 'dart:math' as math;

/// A simple linear regression implementation for predicting future values
class LinearRegression {
  late double _slope;
  late double _intercept;
  bool _trained = false;

  /// Get the slope of the regression line
  double get slope => _slope;

  /// Get the intercept of the regression line
  double get intercept => _intercept;

  /// Check if the model has been trained
  bool get isTrained => _trained;

  /// Train the linear regression model with x and y data points
  void train(List<double> x, List<double> y) {
    if (x.length != y.length || x.isEmpty) {
      throw ArgumentError('Input lists must have the same non-zero length');
    }

    final n = x.length;
    double sumX = 0;
    double sumY = 0;
    double sumXY = 0;
    double sumX2 = 0;

    for (int i = 0; i < n; i++) {
      sumX += x[i];
      sumY += y[i];
      sumXY += x[i] * y[i];
      sumX2 += x[i] * x[i];
    }

    final xMean = sumX / n;
    final yMean = sumY / n;

    // Calculate slope and intercept
    final numerator = sumXY - n * xMean * yMean;
    final denominator = sumX2 - n * xMean * xMean;

    // Handle the case where all x values are the same
    if (denominator.abs() < 1e-10) {
      _slope = 0;
      _intercept = yMean;
    } else {
      _slope = numerator / denominator;
      _intercept = yMean - _slope * xMean;
    }

    _trained = true;
  }

  /// Train the linear regression model with weighted data points
  /// Weights determine the importance of each data point in the regression
  void trainWeighted(List<double> x, List<double> y, List<double> weights) {
    if (x.length != y.length || x.length != weights.length || x.isEmpty) {
      throw ArgumentError('Input lists must have the same non-zero length');
    }

    final n = x.length;
    double sumW = 0;
    double sumWX = 0;
    double sumWY = 0;
    double sumWXY = 0;
    double sumWX2 = 0;

    for (int i = 0; i < n; i++) {
      final w = weights[i];
      sumW += w;
      sumWX += w * x[i];
      sumWY += w * y[i];
      sumWXY += w * x[i] * y[i];
      sumWX2 += w * x[i] * x[i];
    }

    final xMean = sumWX / sumW;
    final yMean = sumWY / sumW;

    // Calculate weighted slope and intercept
    final numerator = sumWXY - xMean * sumWY;
    final denominator = sumWX2 - xMean * sumWX;

    // Handle the case where all x values are the same
    if (denominator.abs() < 1e-10) {
      _slope = 0;
      _intercept = yMean;
    } else {
      _slope = numerator / denominator;
      _intercept = yMean - _slope * xMean;
    }

    _trained = true;
  }

  /// Predict y value for a given x value
  double predict(double x) {
    if (!_trained) {
      throw StateError('Model must be trained before making predictions');
    }
    return _slope * x + _intercept;
  }

  /// Predict multiple y values for a list of x values
  List<double> predictMultiple(List<double> xValues) {
    return xValues.map((x) => predict(x)).toList();
  }

  /// Set the parameters of the model directly (for loading from saved state)
  void setParameters(double slope, double intercept) {
    _slope = slope;
    _intercept = intercept;
    _trained = true;
  }

  /// Calculate the R-squared value (coefficient of determination)
  double calculateRSquared(List<double> x, List<double> y) {
    if (!_trained || x.length != y.length || x.isEmpty) {
      throw ArgumentError('Model must be trained and input lists must have the same non-zero length');
    }

    final n = x.length;
    final yMean = y.reduce((a, b) => a + b) / n;

    double totalSumOfSquares = 0;
    double residualSumOfSquares = 0;

    for (int i = 0; i < n; i++) {
      final predicted = predict(x[i]);
      totalSumOfSquares += (y[i] - yMean) * (y[i] - yMean);
      residualSumOfSquares += (y[i] - predicted) * (y[i] - predicted);
    }

    if (totalSumOfSquares.abs() < 1e-10) {
      return 1.0; // All y values are the same
    }

    return 1 - (residualSumOfSquares / totalSumOfSquares);
  }

  /// Calculate the standard error of the regression
  double calculateStandardError(List<double> x, List<double> y) {
    if (!_trained || x.length != y.length || x.isEmpty) {
      throw ArgumentError('Model must be trained and input lists must have the same non-zero length');
    }

    final n = x.length;
    if (n < 3) {
      return double.infinity; // Not enough data points for standard error
    }

    double sumSquaredResiduals = 0;

    for (int i = 0; i < n; i++) {
      final predicted = predict(x[i]);
      final residual = y[i] - predicted;
      sumSquaredResiduals += residual * residual;
    }

    // Standard error of the regression (SER)
    // Degrees of freedom = n - 2 (for slope and intercept)
    return math.sqrt(sumSquaredResiduals / (n - 2));
  }

  /// Calculate prediction confidence for a future x value
  /// Returns a value between 0 and 1, where 1 is highest confidence
  double calculatePredictionConfidence(double futureX, List<double> x, List<double> y) {
    if (!_trained || x.length != y.length || x.isEmpty) {
      throw ArgumentError('Model must be trained and input lists must have the same non-zero length');
    }

    final n = x.length;
    if (n < 3) {
      return 0.5; // Not enough data points for confidence calculation
    }

    // Calculate mean of x values
    final xMean = x.reduce((a, b) => a + b) / n;

    // Расчет вариации данных не используется напрямую, но учитывается через maxDistance

    // Calculate standard error of regression
    final standardError = calculateStandardError(x, y);
    if (standardError == double.infinity) {
      return 0.5;
    }

    // Calculate R-squared
    final rSquared = calculateRSquared(x, y);

    // Calculate prediction variance factor
    // This increases as the prediction point moves further from the mean of the data
    final distanceFromMean = (futureX - xMean).abs();
    final maxDistance = x.map((val) => (val - xMean).abs()).reduce((a, b) => a > b ? a : b);
    final normalizedDistance = distanceFromMean / (maxDistance > 0 ? maxDistance : 1);

    // Calculate data quality factor (based on number of points and their distribution)
    final dataQualityFactor = (n / 20).clamp(0.0, 1.0); // Max quality at 20+ points

    // Calculate time factor (confidence decreases for predictions further in the future)
    final timeFactor = (1 - normalizedDistance).clamp(0.3, 1.0);

    // Calculate model quality factor (based on R-squared)
    final modelQualityFactor = (rSquared).clamp(0.0, 1.0);

    // Combine factors to get final confidence
    // Weight factors based on their importance
    final confidence = (
      modelQualityFactor * 0.4 +  // Model quality is most important
      dataQualityFactor * 0.3 +   // Data quality is next
      timeFactor * 0.3            // Time factor is also important
    ).clamp(0.0, 1.0);

    return confidence;
  }
}
