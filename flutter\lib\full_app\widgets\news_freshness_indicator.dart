import 'package:flutter/material.dart';
import '../models/news_item.dart';

/// Виджет для отображения индикатора свежести новости
class NewsFreshnessIndicator extends StatelessWidget {
  final NewsItem newsItem;
  final bool showText;
  final double iconSize;

  const NewsFreshnessIndicator({
    Key? key,
    required this.newsItem,
    this.showText = true,
    this.iconSize = 12,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final freshness = _calculateFreshness();
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          freshness.icon,
          color: freshness.color,
          size: iconSize,
        ),
        if (showText) ...[
          const SizedBox(width: 4),
          Text(
            freshness.text,
            style: TextStyle(
              color: freshness.color,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  NewsFreshness _calculateFreshness() {
    final now = DateTime.now();
    final publishedDiff = now.difference(newsItem.publishedAt);
    final fetchedDiff = now.difference(newsItem.fetchedAt);
    
    // Если новость была опубликована менее часа назад
    if (publishedDiff.inHours < 1) {
      return NewsFreshness(
        icon: Icons.fiber_new_rounded,
        color: const Color(0xFF34C759), // Зеленый
        text: 'Новое',
      );
    }
    
    // Если новость была получена сервером недавно (в течение 30 минут)
    if (fetchedDiff.inMinutes < 30) {
      return NewsFreshness(
        icon: Icons.update_rounded,
        color: const Color(0xFF007AFF), // Синий
        text: 'Обновлено',
      );
    }
    
    // Если новость опубликована сегодня
    if (publishedDiff.inHours < 24) {
      return NewsFreshness(
        icon: Icons.schedule_rounded,
        color: const Color(0xFFFF9500), // Оранжевый
        text: 'Сегодня',
      );
    }
    
    // Старые новости
    return NewsFreshness(
      icon: Icons.history_rounded,
      color: const Color(0xFF8E8E93), // Серый
      text: 'Архив',
    );
  }
}

class NewsFreshness {
  final IconData icon;
  final Color color;
  final String text;

  NewsFreshness({
    required this.icon,
    required this.color,
    required this.text,
  });
}

/// Утилитарный класс для работы с временем новостей
class NewsTimeUtils {
  /// Возвращает время для отображения - приоритет cachedAt над publishedAt
  static DateTime getDisplayTime(NewsItem newsItem) {
    return newsItem.cachedAt ?? newsItem.publishedAt;
  }
  
  /// Форматирует время в читаемый вид
  static String formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final diff = now.difference(dateTime);
    
    if (diff.inMinutes < 1) {
      return 'Только что';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes} мин назад';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}ч назад';
    } else if (diff.inDays == 1) {
      return 'Вчера';
    } else if (diff.inDays < 7) {
      return '${diff.inDays} дн назад';
    } else if (diff.inDays < 30) {
      return '${(diff.inDays / 7).floor()} нед назад';
    } else {
      // Для старых новостей показываем дату
      return '${dateTime.day}.${dateTime.month.toString().padLeft(2, '0')}.${dateTime.year}';
    }
  }
  
  /// Возвращает краткое описание времени публикации
  static String getTimeDescription(NewsItem newsItem) {
    final publishedDiff = DateTime.now().difference(newsItem.publishedAt);
    final fetchedDiff = DateTime.now().difference(newsItem.fetchedAt);
    
    if (publishedDiff.inHours < 1) {
      return 'Опубликовано недавно';
    } else if (fetchedDiff.inMinutes < 30) {
      return 'Только что получено';
    } else if (publishedDiff.inHours < 24) {
      return 'Опубликовано сегодня';
    } else {
      return 'Архивная новость';
    }
  }
}
