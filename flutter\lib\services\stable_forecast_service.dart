import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';

/// Простая модель прогноза на один день
class DailyForecast {
  final DateTime date;
  final double predictedScore;

  DailyForecast({required this.date, required this.predictedScore});

  Map<String, dynamic> toJson() => {
    'date': date.toIso8601String(),
    'predictedScore': predictedScore,
  };

  factory DailyForecast.fromJson(Map<String, dynamic> json) => DailyForecast(
    date: DateTime.parse(json['date']),
    predictedScore: json['predictedScore'],
  );
}

/// Сервис для стабильного прогнозирования рыночного настроения
class StableForecastService {
  static const String _cacheKey = 'stable_forecasts';
  final Map<String, DailyForecast> _cache = {};
  final int windowSize;

  /// [windowSize] — число последних дней для расчёта скользящего среднего
  StableForecastService({this.windowSize = 14});

  /// Основной метод: возвращает прогнозы на [daysAhead] дней
  Future<List<DailyForecast>> getForecasts({
    required List<double> historicalScores,
    required DateTime lastDate,
    required int daysAhead,
  }) async {
    await _loadCache();
    List<DailyForecast> result = [];
    // Повторные запросы за один и тот же промежуток — из кеша
    for (int i = 1; i <= daysAhead; i++) {
      final forecastDate = DateTime(
        lastDate.year,
        lastDate.month,
        lastDate.day + i,
      );
      final key = forecastDate.toIso8601String().split('T').first;
      if (_cache.containsKey(key)) {
        result.add(_cache[key]!);
      } else {
        final score = _computeMovingAverage(historicalScores, i);
        final forecast = DailyForecast(
          date: forecastDate,
          predictedScore: score,
        );
        _cache[key] = forecast;
        result.add(forecast);
      }
    }
    await _saveCache();
    return result;
  }

  /// Простое скользящее среднее с линейным снижением веса
  double _computeMovingAverage(List<double> history, int offset) {
    if (history.isEmpty) return 50.0;
    final n = min(windowSize, history.length);
    final sub = history.sublist(history.length - n);
    // Линейный регрессия: y = a*x + b
    final x = List<double>.generate(sub.length, (i) => i.toDouble());
    final y = sub;
    final xMean = x.reduce((a, b) => a + b) / x.length;
    final yMean = y.reduce((a, b) => a + b) / y.length;
    double num = 0, den = 0;
    for (var i = 0; i < x.length; i++) {
      num += (x[i] - xMean) * (y[i] - yMean);
      den += (x[i] - xMean) * (x[i] - xMean);
    }
    final a = den != 0 ? num / den : 0;
    final b = yMean - a * xMean;
    // Прогнозирует на offset вперёд
    final prediction = a * (x.length + offset - 1) + b;
    return prediction.clamp(0.0, 100.0);
  }

  Future<void> _loadCache() async {
    final prefs = await SharedPreferences.getInstance();
    final raw = prefs.getString(_cacheKey);
    if (raw != null) {
      final Map<String, dynamic> data = jsonDecode(raw);
      data.forEach((k, v) {
        _cache[k] = DailyForecast.fromJson(v);
      });
    }
  }

  Future<void> _saveCache() async {
    final prefs = await SharedPreferences.getInstance();
    final Map<String, dynamic> toStore = {};
    _cache.forEach((k, v) {
      toStore[k] = v.toJson();
    });
    await prefs.setString(_cacheKey, jsonEncode(toStore));
  }
}
