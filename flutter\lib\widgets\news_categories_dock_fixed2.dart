import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Добавляем импорт для HapticFeedback
import 'package:finance_ai/models/sentiment_types.dart';

/// Виджет топбара для категорий новостей с анимированным эффектом при нажатии
class NewsCategoriesDock extends StatefulWidget {
  final Function(String) onCategoryChanged;
  final String selectedCategory;
  final Color selectedColor;
  final Color unselectedColor;
  final double itemSpacing;
  final double height;
  
  const NewsCategoriesDock({
    Key? key,
    required this.onCategoryChanged,
    this.selectedCategory = 'all',
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.itemSpacing = 16.0,
    this.height = 56.0,
  }) : super(key: key);

  @override
  State<NewsCategoriesDock> createState() => _NewsCategoriesDockState();
}

class _NewsCategoriesDockState extends State<NewsCategoriesDock> with SingleTickerProviderStateMixin {
  String? _hoveredCategory;
  late AnimationController _animationController;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildCategoryItem(
                'all', 
                'All', 
                Icons.public,
              ),
              SizedBox(width: widget.itemSpacing),
              _buildCategoryItem(
                'crypto', 
                'Crypto', 
                Icons.trending_up,
              ),
              SizedBox(width: widget.itemSpacing),
              _buildCategoryItem(
                'stock', 
                'Stock', 
                Icons.show_chart,
              ),
              SizedBox(width: widget.itemSpacing),
              _buildCategoryItem(
                'whales', 
                'Whales', 
                Icons.water,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryItem(String category, String label, IconData icon) {
    final bool isSelected = widget.selectedCategory == category;
    final bool isHovered = _hoveredCategory == category;
    
    return GestureDetector(
      onTap: () => widget.onCategoryChanged(category),
      child: MouseRegion(
        onEnter: (_) => setState(() {
          _hoveredCategory = category;
          _animationController.forward();
        }),
        onExit: (_) => setState(() {
          _hoveredCategory = null;
          _animationController.reverse();
        }),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          transform: Matrix4.identity()
            ..scale(isHovered || isSelected ? 1.1 : 1.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? widget.selectedColor : widget.unselectedColor,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? widget.selectedColor : widget.unselectedColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Константы для док-панели
const double kDefaultDockHeight = 70.0;
const double kDefaultDockWidth = 380.0;
const double kExpandedDockWidth = 450.0;
const double kDefaultIconSize = 40.0;
const double kMaxIconSize = 80.0;
const double kHoverDistance = 150.0;
const Duration kAnimationDuration = Duration(milliseconds: 400);

/// Более продвинутая версия с анимацией в стиле док-панели MacOS
class AnimatedNewsCategoriesDock extends StatefulWidget {
  final Function(String) onCategoryChanged;
  final String selectedCategory;
  final Color selectedColor;
  final Color unselectedColor;
  final double itemSpacing;
  final double height;
  final double defaultWidth;
  final double expandedWidth;
  final double maxScale;
  final List<Color> gradientColors;
  final Duration animationDuration;
  final bool useGlow;

  const AnimatedNewsCategoriesDock({
    Key? key,
    required this.onCategoryChanged,
    this.selectedCategory = 'all',
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.itemSpacing = 16.0,
    this.height = kDefaultDockHeight,
    this.defaultWidth = kDefaultDockWidth,
    this.expandedWidth = kExpandedDockWidth,
    this.maxScale = 1.5,
    this.gradientColors = const [Color(0xFF1F1F1F), Color(0xFF2D2D2D)],
    this.animationDuration = kAnimationDuration,
    this.useGlow = true,
  }) : super(key: key);

  @override
  State<AnimatedNewsCategoriesDock> createState() => _AnimatedNewsCategoriesDockState();
}

class _AnimatedNewsCategoriesDockState extends State<AnimatedNewsCategoriesDock> with SingleTickerProviderStateMixin {
  final Map<String, GlobalKey> _keys = {
    'all': GlobalKey(),
    'crypto': GlobalKey(),
    'stock': GlobalKey(),
    'whales': GlobalKey(),
  };
  
  Offset? _mousePosition;
  String? _hoveredCategory;
  bool _isDockHovered = false;
  
  // Animation controller for the dock width
  late AnimationController _widthController;
  late Animation<double> _widthAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _widthController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _widthAnimation = Tween<double>(
      begin: widget.defaultWidth,
      end: widget.expandedWidth,
    ).animate(CurvedAnimation(
      parent: _widthController,
      curve: Curves.easeOutQuint,
      reverseCurve: Curves.easeInQuint,
    ));
    
    // Анимация для эффекта свечения
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: widget.useGlow ? 1.0 : 0.0,
    ).animate(CurvedAnimation(
      parent: _widthController,
      curve: Curves.easeOut,
    ));
  }
  
  @override
  void dispose() {
    _widthController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: MouseRegion(
        onEnter: (_) {
          setState(() {
            _isDockHovered = true;
          });
          _widthController.forward();
        },
        onHover: (event) {
          setState(() {
            // Запоминаем глобальную позицию для правильного расчёта
            _mousePosition = event.position;
          });
        },
        onExit: (_) {
          setState(() {
            _mousePosition = null;
            _isDockHovered = false;
          });
          _widthController.reverse();
        },
        child: RepaintBoundary( // Добавляем для исправления пиксельных артефактов
          child: AnimatedBuilder(
            animation: Listenable.merge([_widthAnimation, _glowAnimation]),
            builder: (context, child) {
              return Container(
                height: widget.height,
                width: _widthAnimation.value,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: widget.gradientColors,
                  ),
                  borderRadius: BorderRadius.circular(22),
                  boxShadow: [
                    // Основная тень
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                    // Свечение при наведении
                    if (widget.useGlow) BoxShadow(
                      color: widget.selectedColor.withOpacity(0.3 * _glowAnimation.value),
                      blurRadius: 12,
                      spreadRadius: 2 * _glowAnimation.value,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(22),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildAnimatedCategoryItem(
                          'all', 
                          'All', 
                          Icons.public,
                        ),
                        SizedBox(width: widget.itemSpacing),
                        _buildAnimatedCategoryItem(
                          'crypto', 
                          'Crypto', 
                          Icons.trending_up,
                        ),
                        SizedBox(width: widget.itemSpacing),
                        _buildAnimatedCategoryItem(
                          'stock', 
                          'Stock', 
                          Icons.show_chart,
                        ),
                        SizedBox(width: widget.itemSpacing),
                        _buildAnimatedCategoryItem(
                          'whales', 
                          'Whales', 
                          Icons.water,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedCategoryItem(String category, String label, IconData icon) {
    final bool isSelected = widget.selectedCategory == category;
    final key = _keys[category]!;
    
    // Расчет увеличения в зависимости от позиции мыши
    double scale = 1.0;
    if (_mousePosition != null && _isDockHovered) {
      final RenderBox? renderBox = key.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final position = renderBox.localToGlobal(Offset.zero);
        final size = renderBox.size;
        final itemCenterX = position.dx + size.width / 2;
        
        // Расстояние между центром элемента и курсором мыши
        final distance = (_mousePosition!.dx - itemCenterX).abs();
        
        // Максимальное расстояние для эффекта увеличения - увеличиваем для более заметной анимации
        const double maxDistance = 120.0;
        
        if (distance < maxDistance) {
          // Плавное увеличение в зависимости от близости курсора - увеличиваем интенсивность
          scale = 1.0 + (widget.maxScale - 1.0) * (1 - distance / maxDistance) * 1.2;
          // Обновляем состояние при изменении расстояния для более плавной анимации
          WidgetsBinding.instance.addPostFrameCallback((_) => setState(() {}));
        }
      }
    }
    
    // Если элемент выбран, придаем ему минимальное увеличение
    if (isSelected && scale < 1.1) {
      scale = 1.1;
    }
    
    final bool isHovered = _hoveredCategory == category;
    
    // На сколько ярким и насыщенным должен быть цвет (0.0 - 1.0)
    final double colorIntensity = isSelected ? 1.0 : (isHovered ? 0.95 : 0.7);
    final Color itemColor = isSelected ? widget.selectedColor : widget.unselectedColor;
    
    return GestureDetector(
      onTap: () => widget.onCategoryChanged(category),
      child: MouseRegion(
        onEnter: (_) => setState(() {
          _hoveredCategory = category;
          // Добавляем небольшую вибрацию при наведении для привлечения внимания
          try {
            HapticFeedback.lightImpact();
          } catch (e) {
            // Игнорируем ошибки вибрации
          }
        }),
        onExit: (_) => setState(() {
          _hoveredCategory = null;
        }),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          // Добавляем margin в зависимости от масштаба для лучшего размещения элементов
          margin: EdgeInsets.symmetric(horizontal: scale > 1.1 ? (scale - 1.0) * 6.0 : 0),
          child: Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.center,
            children: <Widget>[
              // Подсветка под элементом при выделении
              if (isSelected || isHovered)
                Positioned.fill(
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 200),
                    opacity: isSelected ? 0.15 : (isHovered ? 0.08 : 0),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: itemColor,
                      ),
                    ),
                  ),
                ),

              // Tooltip
              if (isHovered && _isDockHovered)
                Positioned(
                  top: -30,
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 200),
                    opacity: isHovered ? 1.0 : 0.0,
                    child: TweenAnimationBuilder<double>(
                      tween: Tween(begin: 0.0, end: 1.0),
                      duration: const Duration(milliseconds: 150),
                      curve: Curves.easeOutQuint,
                      builder: (context, value, child) {
                        return Transform.translate(
                          offset: Offset(0, (1.0 - value) * 8),
                          child: child,
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              itemColor.withOpacity(0.8),
                              itemColor.withOpacity(0.6),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                          borderRadius: BorderRadius.circular(6),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.25),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            )
                          ],
                        ),
                        child: Text(
                          label,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            letterSpacing: 0.3,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              
              // Icon with dynamic scaling and beautiful effects
              AnimatedContainer(
                key: key,
                duration: const Duration(milliseconds: 150),
                curve: Curves.easeOutCubic,
                transform: Matrix4.identity()..scale(scale),
                padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    // Иконка с эффектом свечения
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      height: 44,
                      width: 44,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected || isHovered 
                            ? itemColor.withOpacity(0.15) 
                            : Colors.transparent,
                        boxShadow: isSelected || isHovered ? [
                          BoxShadow(
                            color: itemColor.withOpacity(0.4),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ] : null,
                      ),
                      child: Center(
                        child: TweenAnimationBuilder<double>(
                          tween: Tween(begin: 0.9, end: isSelected || isHovered ? 1.1 : 0.9),
                          duration: const Duration(milliseconds: 400),
                          curve: Curves.easeInOut,
                          builder: (context, value, child) {
                            return Transform.scale(
                              scale: value,
                              child: Icon(
                                icon,
                                color: itemColor.withOpacity(colorIntensity),
                                size: 26,
                              ),
                            );
                          }
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Текст с плавным переходом цвета и анимацией
                    AnimatedDefaultTextStyle(
                      duration: const Duration(milliseconds: 200),
                      style: TextStyle(
                        color: itemColor.withOpacity(colorIntensity),
                        fontWeight: isSelected ? FontWeight.bold : 
                                     isHovered ? FontWeight.w600 : FontWeight.normal,
                        fontSize: isSelected || isHovered ? 14 : 13,
                        letterSpacing: 0.3,
                      ),
                      child: Text(label),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
