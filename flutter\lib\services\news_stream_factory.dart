import 'package:flutter/foundation.dart';
import 'news_stream_service.dart';

// Условный импорт для Web
import 'news_stream_service_web.dart' if (dart.library.io) 'news_stream_service.dart' as web_service;

/// Фабрика для создания правильного сервиса потока новостей
class NewsStreamFactory {
  static dynamic createNewsStreamService() {
    if (kIsWeb) {
      // Для Web используем специальную реализацию
      return web_service.NewsStreamServiceWeb();
    } else {
      // Для мобильных платформ используем обычную реализацию
      return NewsStreamService();
    }
  }
}
