import 'package:flutter/material.dart';

class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF6366F1);
  static const Color primaryDark = Color(0xFF4F46E5);
  static const Color primaryLight = Color(0xFF818CF8);
  
  // Secondary colors
  static const Color secondary = Color(0xFF10B981);
  static const Color secondaryDark = Color(0xFF059669);
  static const Color secondaryLight = Color(0xFF34D399);
  
  // Background colors
  static const Color background = Color(0xFF0F172A);
  static const Color backgroundLight = Color(0xFF1E293B);
  static const Color surface = Color(0xFF334155);
  static const Color surfaceLight = Color(0xFF475569);
  
  // Text colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFCBD5E1);
  static const Color textTertiary = Color(0xFF94A3B8);
  
  // Status colors
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);
  
  // Chart colors
  static const Color bullish = Color(0xFF10B981);
  static const Color bearish = Color(0xFFEF4444);
  static const Color neutral = Color(0xFF6B7280);
  
  // Gradient colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [background, backgroundLight],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient cardGradient = LinearGradient(
    colors: [surface, surfaceLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Crypto specific colors
  static const Color bitcoin = Color(0xFFF7931A);
  static const Color ethereum = Color(0xFF627EEA);
  static const Color binanceCoin = Color(0xFFF3BA2F);
  static const Color cardano = Color(0xFF0033AD);
  static const Color solana = Color(0xFF9945FF);
  
  // News sentiment colors
  static const Color veryPositive = Color(0xFF059669);
  static const Color positive = Color(0xFF10B981);
  static const Color neutral2 = Color(0xFF6B7280);
  static const Color negative = Color(0xFFEF4444);
  static const Color veryNegative = Color(0xFFDC2626);
  
  // Opacity variants
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  // Dark theme colors
  static const Color darkBackground = Color(0xFF000000);
  static const Color darkSurface = Color(0xFF1A1A1A);
  static const Color darkPrimary = Color(0xFF8B5CF6);
}
