import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:webview_flutter/webview_flutter.dart';
import '../models/candle.dart';
import '../models/chart_state.dart';

/// Улучшенный виджет для отображения графика TradingView
/// с поддержкой маскировки будущих свечей
class ImprovedTradingViewChart extends StatefulWidget {
  final List<Candle> allCandles; // Все 250 свечей
  final Function(double price, int time)? onEntryPointSet;
  final Function(bool isUp, double percentChange, double finalPrice)? onTradeResult;

  const ImprovedTradingViewChart({
    super.key,
    required this.allCandles,
    this.onEntryPointSet,
    this.onTradeResult,
  });

  @override
  State<ImprovedTradingViewChart> createState() => ImprovedTradingViewChartState();
}

class ImprovedTradingViewChartState extends State<ImprovedTradingViewChart> {
  WebViewController? _controller;
  bool _isWebViewLoaded = false;
  bool _areAllCandlesShown = false;
  List<Candle> _currentCandles = [];

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  @override
  void didUpdateWidget(ImprovedTradingViewChart oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Если изменились свечи, загружаем их заново
    if (widget.allCandles != oldWidget.allCandles) {
      _loadAllCandles();
    }
  }

  Future<void> _initWebView() async {
    try {
      debugPrint('Initializing WebView with draggable chart');

      // Загружаем HTML-файл с улучшенной функциональностью
      String htmlContent;
      try {
        htmlContent = await rootBundle.loadString('assets/tradingview_chart_simple.html');
        debugPrint('Successfully loaded simple chart HTML with length: ${htmlContent.length}');
      } catch (e) {
        // Если не удалось загрузить новый файл, пробуем загрузить старый
        debugPrint('Failed to load simple HTML file: $e, trying old one');
        try {
          htmlContent = await rootBundle.loadString('assets/tradingview_chart_new.html');
          debugPrint('Loaded old chart HTML with length: ${htmlContent.length}');
        } catch (e2) {
          // Если и старый файл не загрузился, используем встроенный HTML
          debugPrint('Failed to load old HTML file: $e2, using embedded HTML');
          htmlContent = _getEmbeddedHtml();
          debugPrint('Using embedded HTML with length: ${htmlContent.length}');
        }
      }
      debugPrint('HTML content loaded, length: ${htmlContent.length}');

      // Создаем контроллер WebView с оптимальными настройками
      final controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(const Color(0xFF131722))
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageFinished: (String url) {
              debugPrint('WebView page loaded: $url');
              setState(() {
                _isWebViewLoaded = true;
              });

              // Проверяем, что JavaScript работает
              _controller?.runJavaScript('console.log("WebView JavaScript is working")');
            },
            onWebResourceError: (WebResourceError error) {
              debugPrint('WebView error: ${error.description}');
            },
          ),
        )
        ..addJavaScriptChannel(
          'FlutterChannel',
          onMessageReceived: _handleJavaScriptMessage,
        )
        ..loadHtmlString(htmlContent);

      setState(() {
        _controller = controller;
      });

      // Не загружаем свечи здесь, они будут загружены после получения события chartInitialized
    } catch (e) {
      debugPrint('Error initializing WebView: $e');
    }
  }

  // Встроенный HTML для случаев, когда не удается загрузить файлы
  String _getEmbeddedHtml() {
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Simple TradingView Chart</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #131722;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            cursor: grab;
        }

        #chart-container:active {
            cursor: grabbing;
        }

        #result-popup {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 20px;
            color: white;
            text-align: center;
            display: none;
            z-index: 1000;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }

        .result-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .result-profit {
            font-size: 24px;
            font-weight: bold;
            margin-top: 10px;
        }

        .result-profit.positive {
            color: #26a69a;
        }

        .result-profit.negative {
            color: #ef5350;
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="result-popup">
        <div class="result-title">Result</div>
        <div id="result-status"></div>
        <div id="result-profit" class="result-profit"></div>
    </div>

    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Global variables
        let chart;
        let candleSeries;
        let allCandles = [];
        let visibleCandlesCount = 243;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing chart...');
            initChart();
        });

        // Handle messages from Flutter
        window.addEventListener('message', function(event) {
            console.log('Received message from parent:', event.data);
            try {
                const message = JSON.parse(event.data);

                switch (message.action) {
                    case 'loadCandles':
                        loadCandles(message.data);
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'showAllCandles':
                        showAllCandles(message.data);
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'determineResult':
                        determineResult();
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                    case 'resetChartPosition':
                        resetChartPosition();
                        break;
                    case 'centerLastCandle':
                        centerLastCandle();
                        break;
                    case 'saveChartState':
                        saveChartState();
                        break;
                    case 'restoreChartState':
                        restoreChartState(message.data);
                        break;
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });

        // Initialize chart
        function initChart() {
            console.log('Initializing chart...');
            const container = document.getElementById('chart-container');

            if (!container) {
                console.error('Chart container not found!');
                return;
            }

            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: container.clientHeight,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: {
                        color: 'rgba(42, 46, 57, 0.5)',
                    },
                    horzLines: {
                        color: 'rgba(42, 46, 57, 0.5)',
                    },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    borderColor: '#2a2e39',
                },
                rightPriceScale: {
                    borderColor: '#2a2e39',
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                handleScroll: true,
                handleScale: true,
            });

            // Create candlestick series
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (chart) {
                    chart.resize(
                        container.clientWidth,
                        container.clientHeight
                    );
                }
            });

            // Notify Flutter that chart is initialized
            sendMessageToFlutter('chartInitialized', []);
        }

        // Load candles data
        function loadCandles(candles) {
            console.log('Loading all candles, type:', typeof candles);

            try {
                allCandles = candles;
                console.log('Parsed all candles, count:', allCandles.length);

                // Show only initial candles (first 243)
                showInitialCandles();

                // Notify Flutter that candles are loaded
                sendMessageToFlutter('candlesLoaded', [allCandles.length]);
            } catch (e) {
                console.error('Error loading candles:', e);
            }
        }

        // Show only initial candles (first 243)
        function showInitialCandles() {
            if (!chart || !candleSeries || !allCandles.length) return;

            console.log('Showing initial candles (243)');

            // Get first 243 candles
            const initialCandles = allCandles.slice(0, visibleCandlesCount);

            // Set data to chart
            candleSeries.setData(initialCandles);
            console.log('Initial candles set to chart:', initialCandles.length);

            // Fit content to view
            chart.timeScale().fitContent();

            // Center the chart with multiple attempts (more reliable)
            setTimeout(() => chart.timeScale().scrollToPosition(50, false), 0);
            setTimeout(() => chart.timeScale().scrollToPosition(50, false), 300);

            // Notify Flutter that initial candles are shown
            sendMessageToFlutter('initialCandlesShown', []);
        }

        // Show all candles (visible + future)
        function showAllCandles(candles) {
            if (!chart || !candleSeries) return;

            console.log('Showing all candles (visible + future)');

            // If candles are provided, use them instead of allCandles
            const allCandlesToUse = candles || allCandles;

            if (!allCandlesToUse || !allCandlesToUse.length) {
                console.error('No candles available to show');
                return;
            }

            // Calculate how many candles to show
            // We want to show exactly visibleCandlesCount + 7 candles
            const totalCandlesToShow = visibleCandlesCount + 7;

            // Make sure we don't exceed the available candles
            const actualCandlesToShow = Math.min(totalCandlesToShow, allCandlesToUse.length);

            // Get the candles to show (first actualCandlesToShow candles)
            const candlesToShow = allCandlesToUse.slice(0, actualCandlesToShow);

            console.log('Showing exactly', candlesToShow.length, 'candles');
            console.log('Visible candles:', visibleCandlesCount);
            console.log('Future candles:', candlesToShow.length - visibleCandlesCount);

            // Set candles to chart
            candleSeries.setData(candlesToShow);

            // Ensure the chart is properly centered
            chart.timeScale().fitContent();

            // If we have an entry point, make sure it's visible
            if (entryPointPrice && entryPointTime) {
                // Re-create the entry point line if it was removed
                if (!horizontalLine) {
                    horizontalLine = candleSeries.createPriceLine({
                        price: entryPointPrice,
                        color: '#2196F3',
                        lineWidth: 2,
                        lineStyle: LightweightCharts.LineStyle.Solid,
                        axisLabelVisible: true,
                        title: 'Entry',
                    });
                }

                // Scroll to make entry point visible with multiple attempts
                setTimeout(() => chart.timeScale().scrollToPosition(50, false), 0);
                setTimeout(() => chart.timeScale().scrollToPosition(50, false), 300);
            }

            // Determine the result after showing all candles
            determineResult();

            // Notify Flutter that all candles are shown
            sendMessageToFlutter('allCandlesShown', []);
        }

        // Set entry point at the last visible candle
        function setEntryPoint() {
            if (!chart || !candleSeries || !allCandles.length) return;

            console.log('Setting entry point');

            // Clear previous entry point if exists
            if (horizontalLine) {
                chart.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Get entry point candle - ALWAYS use the last visible candle (243rd candle)
            // Make sure we're using the correct index (visibleCandlesCount - 1)
            const entryPointIndex = visibleCandlesCount - 1;
            const entryCandle = allCandles[entryPointIndex];

            console.log('Entry point set at candle index:', entryPointIndex);
            console.log('Entry point candle:', entryCandle);
            console.log('Total candles:', allCandles.length);
            console.log('Visible candles count:', visibleCandlesCount);

            if (!entryCandle) {
                console.error('Entry candle not found at index:', entryPointIndex);
                return;
            }

            // Set entry point price and time
            entryPointPrice = entryCandle.close;
            entryPointTime = entryCandle.time;

            // Add horizontal line at entry price with improved visibility
            horizontalLine = candleSeries.createPriceLine({
                price: entryPointPrice,
                color: '#2196F3',
                lineWidth: 2,
                lineStyle: LightweightCharts.LineStyle.Solid,
                axisLabelVisible: true,
                title: 'Entry',
            });

            // Ensure the entry point is visible by scrolling to it
            chart.timeScale().scrollToPosition(50, false);

            // Add a small delay to ensure the chart is properly rendered
            setTimeout(() => {
                // Scroll to position again to ensure entry point is visible
                chart.timeScale().scrollToPosition(50, false);

                // Notify Flutter about entry point
                sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);
            }, 100);
        }

        // Determine trade result
        function determineResult() {
            if (!entryPointPrice || !entryPointTime) return;

            try {
                // Get entry point index (last visible candle)
                const entryPointIndex = visibleCandlesCount - 1;

                // Get 7th candle after entry point (entry point + 7)
                const resultIndex = entryPointIndex + 7;
                const resultCandle = allCandles[resultIndex];

                console.log('Entry point index:', entryPointIndex);
                console.log('Result index:', resultIndex);
                console.log('Result candle:', resultCandle);

                if (!resultCandle) {
                    console.error('Result candle not found at index:', resultIndex);
                    return;
                }

                // Compare closing price with entry price
                const priceChange = resultCandle.close - entryPointPrice;
                const percentChange = (priceChange / entryPointPrice) * 100;
                const isUp = priceChange > 0;

                console.log('Result determined:', {
                    isUp: isUp,
                    percentChange: percentChange.toFixed(2) + '%',
                    finalPrice: resultCandle.close,
                    entryPrice: entryPointPrice
                });

                // Show result popup
                showResultPopup(isUp, percentChange);

                // Notify Flutter about result
                sendMessageToFlutter('tradeResult', [
                    isUp,
                    percentChange,
                    resultCandle.close
                ]);
            } catch (e) {
                console.error('Error determining result:', e);
            }
        }

        // Show result popup
        function showResultPopup(isUp, percentChange) {
            const popup = document.getElementById('result-popup');
            const status = document.getElementById('result-status');
            const profit = document.getElementById('result-profit');

            status.textContent = isUp ? 'Price went UP' : 'Price went DOWN';
            profit.textContent = percentChange.toFixed(2) + '%';
            profit.className = 'result-profit ' + (isUp ? 'positive' : 'negative');

            popup.style.display = 'block';

            // Hide popup after 3 seconds
            setTimeout(() => {
                popup.style.display = 'none';
            }, 3000);
        }

        // Clear chart elements
        function clearChartElements() {
            if (!chart || !candleSeries) return;

            console.log('Clearing chart elements');

            // Remove horizontal line
            if (horizontalLine) {
                chart.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Reset entry point
            entryPointPrice = null;
            entryPointTime = null;

            // Hide result popup
            document.getElementById('result-popup').style.display = 'none';
        }

        // Reset chart position
        function resetChartPosition() {
            if (!chart) return;

            console.log('Resetting chart position');

            // Fit content to view
            chart.timeScale().fitContent();

            // Center the chart with multiple attempts (more reliable)
            setTimeout(() => chart.timeScale().scrollToPosition(50, false), 0);
            setTimeout(() => chart.timeScale().scrollToPosition(50, false), 300);
            setTimeout(() => chart.timeScale().scrollToPosition(50, false), 600);

            // Notify Flutter that chart position is reset
            sendMessageToFlutter('chartPositionReset', []);
        }

        // Center last candle
        function centerLastCandle() {
            if (!chart || !candleSeries || !allCandles.length) return;

            console.log('Centering last candle');

            // Fit content to view
            chart.timeScale().fitContent();

            // Center the chart with multiple attempts (more reliable)
            setTimeout(() => chart.timeScale().scrollToPosition(50, false), 0);
            setTimeout(() => chart.timeScale().scrollToPosition(50, false), 300);
            setTimeout(() => chart.timeScale().scrollToPosition(50, false), 600);
        }

        // Save chart state
        function saveChartState() {
            if (!chart || !candleSeries || !allCandles.length) return;

            console.log('Saving chart state');

            // Chart state is already saved in global variables:
            // - allCandles: all candles
            // - visibleCandlesCount: number of visible candles
            // - entryPointPrice: entry point price
            // - entryPointTime: entry point time

            // Notify Flutter that chart state is saved
            sendMessageToFlutter('chartStateSaved', []);
        }

        // Restore chart state
        function restoreChartState(data) {
            if (!chart || !candleSeries) return;

            console.log('Restoring chart state');

            try {
                // Restore candles
                if (data.candles && data.candles.length) {
                    allCandles = data.candles;
                    console.log('Restored candles:', allCandles.length);
                }

                // Restore visible candles count
                if (data.lastVisibleIndex) {
                    visibleCandlesCount = data.lastVisibleIndex + 1;
                    console.log('Restored visible candles count:', visibleCandlesCount);
                }

                // Restore entry point
                if (data.entryPrice && data.entryTime) {
                    entryPointPrice = data.entryPrice;
                    entryPointTime = data.entryTime;
                    console.log('Restored entry point:', entryPointPrice, entryPointTime);
                }

                // Show initial candles
                showInitialCandles();

                // Restore entry point marker
                if (entryPointPrice && entryPointTime) {
                    // Clear previous entry point if exists
                    if (horizontalLine) {
                        chart.removePriceLine(horizontalLine);
                        horizontalLine = null;
                    }

                    // Add horizontal line at entry price
                    horizontalLine = candleSeries.createPriceLine({
                        price: entryPointPrice,
                        color: '#2196F3',
                        lineWidth: 2,
                        lineStyle: LightweightCharts.LineStyle.Solid,
                        axisLabelVisible: true,
                        title: 'Entry',
                    });
                }

                // Notify Flutter that chart state is restored
                sendMessageToFlutter('chartStateRestored', []);
            } catch (e) {
                console.error('Error restoring chart state:', e);
            }
        }

        // Send message to Flutter
        function sendMessageToFlutter(handler, args) {
            try {
                if (window.flutter_inappwebview) {
                    // For mobile platforms
                    window.flutter_inappwebview.callHandler(handler, ...args);
                } else if (window.FlutterChannel) {
                    // For WebView on mobile
                    window.FlutterChannel.postMessage(JSON.stringify({
                        handler: handler,
                        args: args
                    }));
                } else if (window.parent && window.parent !== window) {
                    // For web platform (iframe)
                    window.parent.postMessage(JSON.stringify({
                        handler: handler,
                        args: args
                    }), '*');
                } else {
                    console.log('Flutter interface not available');
                }
            } catch (e) {
                console.error('Error sending message to Flutter:', e);
            }
        }
    </script>
</body>
</html>
    ''';
  }

  void _handleJavaScriptMessage(JavaScriptMessage message) {
    try {
      debugPrint('Received message from JavaScript: ${message.message}');
      final Map<String, dynamic> data = jsonDecode(message.message);

      final String handler = data['handler'];
      final List<dynamic> args = data['args'];

      switch (handler) {
        case 'chartInitialized':
          debugPrint('Chart initialized');
          // Загружаем свечи с задержкой, чтобы дать время для инициализации графика
          Future.delayed(const Duration(milliseconds: 500), () {
            _loadAllCandles();
          });
          break;
        case 'candlesLoaded':
          debugPrint('Candles loaded: ${args[0]} candles');
          break;
        case 'allCandlesShown':
          debugPrint('All candles shown');
          setState(() {
            _areAllCandlesShown = true;
          });
          break;
        case 'entryPointSet':
          if (widget.onEntryPointSet != null && args.length >= 2) {
            widget.onEntryPointSet!(
              args[0] is num ? args[0].toDouble() : 0.0,
              args[1] is num ? args[1].toInt() : 0,
            );
          }
          break;
        case 'tradeResult':
          if (widget.onTradeResult != null && args.length >= 3) {
            widget.onTradeResult!(
              args[0] is bool ? args[0] : false,
              args[1] is num ? args[1].toDouble() : 0.0,
              args[2] is num ? args[2].toDouble() : 0.0,
            );
          }
          break;
        default:
          debugPrint('Unknown handler: $handler');
      }
    } catch (e) {
      debugPrint('Error handling JavaScript message: $e');
    }
  }

  // Загрузка всех свечей (250) - улучшенная версия
  Future<void> _loadAllCandles() async {
    if (!_isWebViewLoaded || _controller == null || widget.allCandles.isEmpty) {
      debugPrint('Cannot load candles: webview loaded: $_isWebViewLoaded, controller null: ${_controller == null}, candles empty: ${widget.allCandles.isEmpty}');
      return;
    }

    debugPrint('Loading ${widget.allCandles.length} candles to WebView');

    try {
      // Преобразуем свечи в формат для TradingView
      final List<Map<String, dynamic>> candlesData = widget.allCandles.map((candle) => {
        'time': candle.time,
        'open': candle.open,
        'high': candle.high,
        'low': candle.low,
        'close': candle.close,
        'volume': candle.volume,
      }).toList();

      final String jsonData = jsonEncode(candlesData);
      debugPrint('Candles JSON data length: ${jsonData.length}');

      // Создаем сообщение для отправки в JavaScript
      final String message = jsonEncode({
        'action': 'loadCandles',
        'data': candlesData,
      });

      // Загружаем свечи в WebView через postMessage для большей надежности
      await _controller!.runJavaScript(
        'window.postMessage(${jsonEncode(message)}, "*")',
      );

      debugPrint('All candles loaded to WebView');

      // Сбрасываем флаг отображения всех свечей
      setState(() {
        _areAllCandlesShown = false;
      });

      // Сохраняем текущие свечи для использования при показе всех свечей
      _currentCandles = List.from(widget.allCandles);

      // Даем время для обработки свечей
      await Future.delayed(const Duration(milliseconds: 500));

      // Проверяем, что свечи загружены
      await _controller!.runJavaScript('console.log("Candles loaded check: " + (allCandles ? allCandles.length : "null"))');
    } catch (e) {
      debugPrint('Error loading candles: $e');
    }
  }

  // Показать только первые 243 свечи - улучшенная версия
  Future<void> showInitialCandles() async {
    if (!_isWebViewLoaded || _controller == null) {
      debugPrint('Cannot show initial candles: webview loaded: $_isWebViewLoaded, controller null: ${_controller == null}');
      return;
    }

    debugPrint('Showing initial candles');

    try {
      // Создаем сообщение для отправки в JavaScript
      final String message = jsonEncode({
        'action': 'showInitialCandles',
      });

      // Отправляем сообщение через postMessage для большей надежности
      await _controller!.runJavaScript(
        'window.postMessage(${jsonEncode(message)}, "*")',
      );

      setState(() {
        _areAllCandlesShown = false;
      });

      // Проверяем, что команда выполнена
      await _controller!.runJavaScript('console.log("Initial candles command sent")');

      debugPrint('Initial candles shown');
    } catch (e) {
      debugPrint('Error showing initial candles: $e');
    }
  }

  // Показать все 250 свечей - улучшенная версия
  Future<void> showAllCandles() async {
    if (!_isWebViewLoaded || _controller == null) {
      debugPrint('Cannot show all candles: webview loaded: $_isWebViewLoaded, controller null: ${_controller == null}');
      return;
    }

    debugPrint('Showing all candles');

    try {
      // Используем сохраненные свечи, чтобы гарантировать, что мы показываем те же самые данные
      if (_currentCandles.isNotEmpty) {
        final List<Map<String, dynamic>> candlesData = _currentCandles.map((candle) => {
          'time': candle.time,
          'open': candle.open,
          'high': candle.high,
          'low': candle.low,
          'close': candle.close,
          'volume': candle.volume,
        }).toList();

        // Создаем сообщение для отправки в JavaScript с данными свечей
        final String message = jsonEncode({
          'action': 'showAllCandles',
          'data': candlesData,
        });

        // Отправляем сообщение через postMessage для большей надежности
        await _controller!.runJavaScript(
          'window.postMessage(${jsonEncode(message)}, "*")',
        );
      } else {
        // Если нет сохраненных свечей, используем стандартный метод
        final String message = jsonEncode({
          'action': 'showAllCandles',
        });

        // Отправляем сообщение через postMessage для большей надежности
        await _controller!.runJavaScript(
          'window.postMessage(${jsonEncode(message)}, "*")',
        );
      }

      setState(() {
        _areAllCandlesShown = true;
      });

      // Проверяем, что команда выполнена
      await _controller!.runJavaScript('console.log("All candles command sent")');

      debugPrint('All candles shown');
    } catch (e) {
      debugPrint('Error showing all candles: $e');
    }
  }

  // Установка точки входа - улучшенная версия
  Future<void> setEntryPoint() async {
    if (!_isWebViewLoaded || _controller == null) {
      debugPrint('Cannot set entry point: webview loaded: $_isWebViewLoaded, controller null: ${_controller == null}');
      return;
    }

    debugPrint('Setting entry point');

    try {
      // Создаем сообщение для отправки в JavaScript
      final String message = jsonEncode({
        'action': 'setEntryPoint',
      });

      // Отправляем сообщение через postMessage
      await _controller!.runJavaScript(
        'window.postMessage(${jsonEncode(message)}, "*")',
      );

      debugPrint('Entry point set command sent');
    } catch (e) {
      debugPrint('Error setting entry point: $e');
    }
  }

  // Определение результата - улучшенная версия
  Future<void> determineResult() async {
    if (!_isWebViewLoaded || _controller == null) {
      debugPrint('Cannot determine result: webview loaded: $_isWebViewLoaded, controller null: ${_controller == null}');
      return;
    }

    debugPrint('Determining result');

    try {
      // Создаем сообщение для отправки в JavaScript
      final String message = jsonEncode({
        'action': 'determineResult',
      });

      // Отправляем сообщение через postMessage
      await _controller!.runJavaScript(
        'window.postMessage(${jsonEncode(message)}, "*")',
      );

      debugPrint('Determine result command sent');
    } catch (e) {
      debugPrint('Error determining result: $e');
    }
  }

  // Очистка элементов графика - улучшенная версия
  Future<void> clearChartElements() async {
    if (!_isWebViewLoaded || _controller == null) {
      debugPrint('Cannot clear chart elements: webview loaded: $_isWebViewLoaded, controller null: ${_controller == null}');
      return;
    }

    debugPrint('Clearing chart elements');

    try {
      // Создаем сообщение для отправки в JavaScript
      final String message = jsonEncode({
        'action': 'clearChartElements',
      });

      // Отправляем сообщение через postMessage
      await _controller!.runJavaScript(
        'window.postMessage(${jsonEncode(message)}, "*")',
      );

      debugPrint('Clear chart elements command sent');
    } catch (e) {
      debugPrint('Error clearing chart elements: $e');
    }
  }

  // Метод для проверки состояния графика (упрощенная версия)
  Future<void> checkChartState() async {
    if (!_isWebViewLoaded || _controller == null) return;

    try {
      // Проверяем состояние графика
      await _controller!.runJavaScript(
        'console.log("Chart state check: " + '
        '(chart ? "Chart exists, draggable: " + chart.options().handleScroll : "Chart not initialized") + ", " + '
        '(candleSeries ? "Series exists with " + candleSeries.dataByIndex().length + " candles" : "Series not initialized"))'
      );
    } catch (e) {
      debugPrint('Error checking chart state: $e');
    }
  }

  // Сброс позиции графика
  Future<void> resetChartPosition() async {
    if (!_isWebViewLoaded || _controller == null) {
      debugPrint('Cannot reset chart position: webview loaded: $_isWebViewLoaded, controller null: ${_controller == null}');
      return;
    }

    debugPrint('Resetting chart position');

    try {
      // Создаем сообщение для отправки в JavaScript
      final String message = jsonEncode({
        'action': 'resetChartPosition',
      });

      // Отправляем сообщение через postMessage
      await _controller!.runJavaScript(
        'window.postMessage(${jsonEncode(message)}, "*")'
      );

      debugPrint('Chart position reset command sent');
    } catch (e) {
      debugPrint('Error resetting chart position: $e');
    }
  }

  // Метод для сохранения состояния графика
  Future<void> saveChartState() async {
    if (!_isWebViewLoaded || _controller == null) {
      debugPrint('Cannot save chart state: webview loaded: $_isWebViewLoaded, controller null: ${_controller == null}');
      return;
    }

    debugPrint('Saving chart state');

    try {
      final String message = jsonEncode({
        'action': 'saveChartState',
      });

      await _controller!.runJavaScript(
        'window.postMessage(${jsonEncode(message)}, "*")'
      );

      debugPrint('Save chart state command sent');
    } catch (e) {
      debugPrint('Error saving chart state: $e');
    }
  }

  // Метод для восстановления состояния графика
  Future<void> restoreChartState(ChartState chartState) async {
    if (!_isWebViewLoaded || _controller == null) {
      debugPrint('Cannot restore chart state: webview loaded: $_isWebViewLoaded, controller null: ${_controller == null}');
      return;
    }

    debugPrint('Restoring chart state');

    try {
      // Преобразуем свечи в формат для TradingView
      final List<Map<String, dynamic>> candlesData = chartState.allCandles.map((candle) => {
        'time': candle.time,
        'open': candle.open,
        'high': candle.high,
        'low': candle.low,
        'close': candle.close,
        'volume': candle.volume,
      }).toList();

      final String jsonData = jsonEncode({
        'candles': candlesData,
        'lastVisibleIndex': chartState.lastVisibleIndex,
        'entryPrice': chartState.entryPrice,
        'entryTime': chartState.entryTime,
      });

      final String message = jsonEncode({
        'action': 'restoreChartState',
        'data': jsonDecode(jsonData),
      });

      await _controller!.runJavaScript(
        'window.postMessage(${jsonEncode(message)}, "*")'
      );

      debugPrint('Restore chart state command sent');
    } catch (e) {
      debugPrint('Error restoring chart state: $e');
    }
  }

  @override
  Widget build(BuildContext context) {

    return _isWebViewLoaded && _controller != null
        ? WebViewWidget(controller: _controller!)
        : const Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Загрузка графика...',
                  style: TextStyle(color: Colors.white70),
                ),
              ],
            ),
          );
  }
}