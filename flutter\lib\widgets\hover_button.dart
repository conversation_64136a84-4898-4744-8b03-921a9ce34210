import 'package:flutter/material.dart';
import 'dart:ui';

class HoverButton extends StatefulWidget {
  final String text;
  final VoidCallback onPressed;
  final Color backgroundColor;
  final Color hoverColor;
  final Color textColor;
  final double width;
  final double height;
  final double fontSize;
  final FontWeight fontWeight;
  final BorderRadius borderRadius;
  final bool useIosBorder;
  final Color borderColor;
  final double borderWidth;

  const HoverButton({
    Key? key,
    required this.text,
    required this.onPressed,
    required this.backgroundColor,
    required this.hoverColor,
    required this.textColor,
    this.width = double.infinity,
    this.height = 50,
    this.fontSize = 16,
    this.fontWeight = FontWeight.bold,
    this.borderRadius = const BorderRadius.all(Radius.circular(12.5)), // Увеличиваем скругление на 25% (10 * 1.25 = 12.5)
    this.useIosBorder = true,
    this.borderColor = Colors.white30,
    this.borderWidth = 0.5,
  }) : super(key: key);

  @override
  State<HoverButton> createState() => _HoverButtonState();
}

class _HoverButtonState extends State<HoverButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: widget.width,
        height: _isHovered ? widget.height + 4 : widget.height, // Увеличиваем высоту при наведении
        decoration: BoxDecoration(
          color: _isHovered ? widget.hoverColor : widget.backgroundColor,
          borderRadius: widget.borderRadius,
          // Добавляем тонкую обводку в стиле iOS
          border: widget.useIosBorder
              ? Border.all(
                  color: _isHovered
                      ? widget.borderColor.withAlpha(180)
                      : widget.borderColor,
                  width: widget.borderWidth,
                )
              : null,
          boxShadow: _isHovered
              ? [
                  BoxShadow(
                    color: widget.hoverColor.withAlpha(128),
                    blurRadius: 10,
                    spreadRadius: 1,
                  )
                ]
              : [],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: widget.onPressed,
            borderRadius: widget.borderRadius,
            child: Center(
              child: Text(
                widget.text,
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: _isHovered ? widget.fontSize + 1 : widget.fontSize, // Увеличиваем шрифт при наведении
                  fontWeight: widget.fontWeight,
                  letterSpacing: _isHovered ? 1.2 : 1.0, // Увеличиваем расстояние между буквами при наведении
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
