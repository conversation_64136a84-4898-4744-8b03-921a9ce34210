import 'package:finance_ai/models/news_item.dart';
import 'package:finance_ai/utils/sentiment_analyzer.dart';
import 'package:finance_ai/utils/news_content_generator.dart';
import 'package:finance_ai/models/sentiment_data.dart';
import 'package:finance_ai/models/sentiment_types.dart';

class NewsDetailData {
  final String title;
  final String description;
  final String fullContent;
  final String source;
  final DateTime publishedAt;
  final String? imageUrl;
  final List<String> tags;
  final SentimentData sentiment;
  final List<String> relatedLinks;
  final Map<String, dynamic> metadata;
  final String url;
  final String? summary;
  final String? rewrittenContent;
  final DateTime fetchedAt;
  final DateTime? cachedAt; // Время сохранения в кэш

  const NewsDetailData({
    required this.title,
    required this.description,
    required this.fullContent,
    required this.source,
    required this.publishedAt,
    this.imageUrl,
    required this.tags,
    required this.sentiment,
    required this.relatedLinks,
    required this.metadata,
    required this.url,
    this.summary,
    this.rewrittenContent,
    required this.fetchedAt,
    this.cachedAt,
  });

  /// Asynchronous factory method that performs sentiment analysis
  static Future<NewsDetailData> fromNewsItem(NewsItem newsItem) async {
    // Используем sentimentData из newsItem если доступно, иначе создаём на лету на основе sentiment
    final sentiment = newsItem.sentimentData ??
        SentimentData(
          sentiment: newsItem.sentiment,
          impact: NewsImpact.medium,
          score: 0.5,
        );

    return NewsDetailData(
      title: newsItem.title,
      description: newsItem.description,
      fullContent: NewsContentGenerator.generateFullContent(newsItem),
      source: newsItem.source,
      publishedAt: newsItem.publishedAt,
      imageUrl: newsItem.imageUrl,
      tags: newsItem.tags,
      sentiment: sentiment,
      relatedLinks: NewsContentGenerator.generateRelatedLinks(newsItem),
      metadata: NewsContentGenerator.generateMetadata(newsItem),
      url: newsItem.url,
      summary: newsItem.summary,
      rewrittenContent: newsItem.rewrittenContent,
      fetchedAt: newsItem.fetchedAt,
      cachedAt: newsItem.cachedAt,
    );
  }

  /// Synchronous factory method that uses quick sentiment analysis
  factory NewsDetailData.fromNewsItemSync(NewsItem newsItem) {
    final sentiment = newsItem.sentimentData ??
        SentimentData(
          sentiment: newsItem.sentiment,
          impact: NewsImpact.medium,
          score: 0.5,
        );

    return NewsDetailData(
      title: newsItem.title,
      description: newsItem.description,
      fullContent: NewsContentGenerator.generateFullContent(newsItem),
      source: newsItem.source,
      publishedAt: newsItem.publishedAt,
      imageUrl: newsItem.imageUrl,
      tags: newsItem.tags,
      sentiment: sentiment,
      relatedLinks: NewsContentGenerator.generateRelatedLinks(newsItem),
      metadata: NewsContentGenerator.generateMetadata(newsItem),
      url: newsItem.url,
      summary: newsItem.summary,
      rewrittenContent: newsItem.rewrittenContent,
      fetchedAt: newsItem.fetchedAt,
      cachedAt: newsItem.cachedAt,
    );
  }

  factory NewsDetailData.fromJson(Map<String, dynamic> json) {
    return NewsDetailData(
      title: json['title'] as String,
      description: json['description'] as String,
      fullContent: json['fullContent'] as String,
      source: json['source'] as String,
      publishedAt: DateTime.parse(json['publishedAt'] as String),
      imageUrl: json['imageUrl'] as String?,
      tags: List<String>.from(json['tags'] as List),
      sentiment: SentimentData.fromJson(json['sentiment'] as Map<String, dynamic>),
      relatedLinks: List<String>.from(json['relatedLinks'] as List),
      metadata: json['metadata'] as Map<String, dynamic>,
      url: json['url'] as String,
      summary: json['summary'] as String?,
      rewrittenContent: json['rewrittenContent'] as String?,
      fetchedAt: DateTime.parse(json['fetchedAt'] as String),
      cachedAt: json['cachedAt'] != null ? DateTime.parse(json['cachedAt'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'fullContent': fullContent,
      'source': source,
      'publishedAt': publishedAt.toIso8601String(),
      'imageUrl': imageUrl,
      'tags': tags,
      'sentiment': sentiment.toJson(),
      'relatedLinks': relatedLinks,
      'metadata': metadata,
      'url': url,
      'summary': summary,
      'rewrittenContent': rewrittenContent,
      'fetchedAt': fetchedAt.toIso8601String(),
      'cachedAt': cachedAt?.toIso8601String(),
    };
  }
} 