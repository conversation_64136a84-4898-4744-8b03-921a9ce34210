import 'dart:math';
import 'dart:ui';
import 'package:flutter/material.dart';

class NewsBackgroundEffects extends StatefulWidget {
  final Widget child;
  
  const NewsBackgroundEffects({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<NewsBackgroundEffects> createState() => _NewsBackgroundEffectsState();
}

class _NewsBackgroundEffectsState extends State<NewsBackgroundEffects> 
    with TickerProviderStateMixin {
  late AnimationController _dataStreamController;
  late AnimationController _neuralNetworkController;
  late AnimationController _hologramController;
  late AnimationController _scanlineController;

  @override
  void initState() {
    super.initState();
    
    _dataStreamController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    )..repeat();
    
    _neuralNetworkController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 12),
    )..repeat();
    
    _hologramController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 6),
    )..repeat();
    
    _scanlineController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat();
  }

  @override
  void dispose() {
    _dataStreamController.dispose();
    _neuralNetworkController.dispose();
    _hologramController.dispose();
    _scanlineController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Основной контент
        widget.child,
        
        // Дополнительные эффекты
        IgnorePointer(
          child: Stack(
            children: [
              // Поток данных
              CustomPaint(
                painter: _DataStreamPainter(_dataStreamController),
                child: const SizedBox.expand(),
              ),
              
              // Нейронная сеть
              CustomPaint(
                painter: _NeuralNetworkPainter(_neuralNetworkController),
                child: const SizedBox.expand(),
              ),
              
              // Голографические эффекты
              CustomPaint(
                painter: _HologramPainter(_hologramController),
                child: const SizedBox.expand(),
              ),
              
              // Сканирующие линии
              CustomPaint(
                painter: _ScanlinePainter(_scanlineController),
                child: const SizedBox.expand(),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

// Рисовальщик потока данных
class _DataStreamPainter extends CustomPainter {
  final Animation<double> animation;
  
  _DataStreamPainter(this.animation) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final time = animation.value * 2 * pi;
    
    // Создаем поток бинарных данных
    for (int stream = 0; stream < 5; stream++) {
      final x = 50.0 + stream * 200.0;
      final streamSpeed = 100.0 + stream * 30.0;
      
      for (int i = 0; i < 20; i++) {
        final y = (time * streamSpeed + i * 50.0) % (size.height + 100) - 50;
        
        if (y >= -50 && y <= size.height) {
          final opacity = 0.3 - (y / size.height) * 0.3;
          final binary = (time + i + stream).floor() % 2 == 0 ? '1' : '0';
          
          final textPainter = TextPainter(
            text: TextSpan(
              text: binary,
              style: TextStyle(
                color: _getStreamColor(stream).withOpacity(opacity),
                fontSize: 12,
                fontFamily: 'monospace',
                fontWeight: FontWeight.w300,
              ),
            ),
            textDirection: TextDirection.ltr,
          );
          
          textPainter.layout();
          textPainter.paint(canvas, Offset(x, y));
        }
      }
    }
  }
  
  Color _getStreamColor(int stream) {
    final colors = [
      const Color(0xFF00ff41), // Matrix green
      const Color(0xFF3b82f6), // Blue
      const Color(0xFF06b6d4), // Cyan
      const Color(0xFF10b981), // Emerald
      const Color(0xFFf59e0b), // Amber
    ];
    return colors[stream % colors.length];
  }

  @override
  bool shouldRepaint(covariant _DataStreamPainter oldDelegate) => true;
}

// Рисовальщик нейронной сети
class _NeuralNetworkPainter extends CustomPainter {
  final Animation<double> animation;
  
  _NeuralNetworkPainter(this.animation) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final time = animation.value * 2 * pi;
    
    // Создаем сеть нейронов
    final nodes = <Offset>[];
    final layers = 4;
    final nodesPerLayer = 6;
    
    for (int layer = 0; layer < layers; layer++) {
      for (int node = 0; node < nodesPerLayer; node++) {
        final x = size.width * 0.2 + (layer * size.width * 0.6 / (layers - 1));
        final y = size.height * 0.2 + (node * size.height * 0.6 / (nodesPerLayer - 1));
        
        // Добавляем небольшое движение
        final offsetX = sin(time + layer + node) * 20;
        final offsetY = cos(time * 0.7 + layer + node) * 15;
        
        nodes.add(Offset(x + offsetX, y + offsetY));
      }
    }
    
    // Рисуем соединения между слоями
    for (int layer = 0; layer < layers - 1; layer++) {
      for (int fromNode = 0; fromNode < nodesPerLayer; fromNode++) {
        for (int toNode = 0; toNode < nodesPerLayer; toNode++) {
          final fromIndex = layer * nodesPerLayer + fromNode;
          final toIndex = (layer + 1) * nodesPerLayer + toNode;
          
          // Анимированная активность
          final activity = 0.5 + 0.5 * sin(time * 2 + fromIndex + toIndex);
          final opacity = activity * 0.2;
          
          final paint = Paint()
            ..color = const Color(0xFF3b82f6).withOpacity(opacity)
            ..strokeWidth = 1.0 + activity * 2.0
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1);
          
          canvas.drawLine(nodes[fromIndex], nodes[toIndex], paint);
          
          // Рисуем импульсы по соединениям
          if (activity > 0.8) {
            final progress = (time * 3 + fromIndex) % 1.0;
            final pulseX = nodes[fromIndex].dx + 
                          (nodes[toIndex].dx - nodes[fromIndex].dx) * progress;
            final pulseY = nodes[fromIndex].dy + 
                          (nodes[toIndex].dy - nodes[fromIndex].dy) * progress;
            
            final pulsePaint = Paint()
              ..color = const Color(0xFF00ff41).withOpacity(0.8)
              ..style = PaintingStyle.fill
              ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);
            
            canvas.drawCircle(Offset(pulseX, pulseY), 3.0, pulsePaint);
          }
        }
      }
    }
    
    // Рисуем узлы
    for (int i = 0; i < nodes.length; i++) {
      final activity = 0.3 + 0.7 * sin(time + i * 0.5);
      
      final paint = Paint()
        ..color = const Color(0xFF06b6d4).withOpacity(activity * 0.6)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);
      
      canvas.drawCircle(nodes[i], 4.0 + activity * 2.0, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _NeuralNetworkPainter oldDelegate) => true;
}

// Рисовальщик голографических эффектов
class _HologramPainter extends CustomPainter {
  final Animation<double> animation;
  
  _HologramPainter(this.animation) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final time = animation.value * 2 * pi;
    
    // Создаем голографические кольца
    for (int i = 0; i < 3; i++) {
      final centerX = size.width * (0.2 + i * 0.3);
      final centerY = size.height * (0.3 + i * 0.2);
      
      for (int ring = 0; ring < 5; ring++) {
        final radius = 30.0 + ring * 25.0 + sin(time + i + ring) * 10.0;
        final opacity = (0.4 - ring * 0.08) * (0.5 + 0.5 * sin(time * 2 + i));
        
        final paint = Paint()
          ..color = _getHologramColor(i).withOpacity(opacity)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.5
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);
        
        canvas.drawCircle(Offset(centerX, centerY), radius, paint);
      }
      
      // Добавляем радиальные линии
      for (int line = 0; line < 8; line++) {
        final angle = line * pi / 4 + time * 0.5;
        final startRadius = 20.0;
        final endRadius = 100.0 + sin(time + line) * 20.0;
        
        final startX = centerX + cos(angle) * startRadius;
        final startY = centerY + sin(angle) * startRadius;
        final endX = centerX + cos(angle) * endRadius;
        final endY = centerY + sin(angle) * endRadius;
        
        final linePaint = Paint()
          ..color = _getHologramColor(i).withOpacity(0.3)
          ..strokeWidth = 1.0
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1);
        
        canvas.drawLine(Offset(startX, startY), Offset(endX, endY), linePaint);
      }
    }
  }
  
  Color _getHologramColor(int index) {
    final colors = [
      const Color(0xFF00ffff), // Cyan
      const Color(0xFF0099ff), // Blue
      const Color(0xFF9933ff), // Purple
    ];
    return colors[index % colors.length];
  }

  @override
  bool shouldRepaint(covariant _HologramPainter oldDelegate) => true;
}

// Рисовальщик сканирующих линий
class _ScanlinePainter extends CustomPainter {
  final Animation<double> animation;
  
  _ScanlinePainter(this.animation) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final time = animation.value * 2 * pi;
    
    // Вертикальные сканирующие линии
    for (int i = 0; i < 3; i++) {
      final x = (time * 200 + i * 300) % (size.width + 100) - 50;
      
      if (x >= -50 && x <= size.width + 50) {
        final gradient = LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            const Color(0xFF00ff41).withOpacity(0.3),
            const Color(0xFF00ff41).withOpacity(0.6),
            const Color(0xFF00ff41).withOpacity(0.3),
            Colors.transparent,
          ],
          stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
        );
        
        final paint = Paint()
          ..shader = gradient.createShader(Rect.fromLTWH(x, 0, 4, size.height))
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);
        
        canvas.drawRect(Rect.fromLTWH(x, 0, 4, size.height), paint);
      }
    }
    
    // Горизонтальные сканирующие линии
    for (int i = 0; i < 2; i++) {
      final y = (time * 150 + i * 400) % (size.height + 100) - 50;
      
      if (y >= -50 && y <= size.height + 50) {
        final gradient = LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Colors.transparent,
            const Color(0xFF06b6d4).withOpacity(0.2),
            const Color(0xFF06b6d4).withOpacity(0.4),
            const Color(0xFF06b6d4).withOpacity(0.2),
            Colors.transparent,
          ],
          stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
        );
        
        final paint = Paint()
          ..shader = gradient.createShader(Rect.fromLTWH(0, y, size.width, 3))
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1);
        
        canvas.drawRect(Rect.fromLTWH(0, y, size.width, 3), paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant _ScanlinePainter oldDelegate) => true;
} 