import 'package:flutter/material.dart';
import 'dart:ui';

class GlassmorphicCard extends StatelessWidget {
  final Widget child;
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final BorderRadius borderRadius;
  final Gradient? gradient;
  final double blur;
  final double borderWidth;
  final Color borderColor;
  final Color shadowColor;
  final bool isResponsive;

  const GlassmorphicCard({
    Key? key,
    required this.child,
    this.width = double.infinity,
    this.height = double.infinity,
    this.padding = const EdgeInsets.all(24.0),
    this.borderRadius = const BorderRadius.all(Radius.circular(20)),
    this.gradient,
    this.blur = 20,
    this.borderWidth = 1.5,
    this.borderColor = Colors.white30,
    this.shadowColor = Colors.black54,
    this.isResponsive = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: shadowColor.withOpacity(0.25),
            blurRadius: 30,
            spreadRadius: -5,
          ),
        ],
        borderRadius: borderRadius,
      ),
      child: ClipRRect(
        borderRadius: borderRadius,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
          child: Container(
            decoration: BoxDecoration(
              gradient: gradient ?? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.1),
                  Colors.white.withOpacity(0.05),
                ],
                stops: const [0.0, 1.0],
              ),
              borderRadius: borderRadius,
              border: Border.all(
                width: borderWidth,
                color: borderColor,
              ),
            ),
            child: Stack(
              children: [
                // Inner highlight for 3D effect
                Positioned.fill(
                  child: CustomPaint(
                    painter: GlassmorphicPainter(
                      borderRadius: borderRadius,
                    ),
                    willChange: false, // Force immediate rendering
                  ),
                ),
                // Main content with padding
                Padding(
                  padding: padding,
                  child: child,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class GlassmorphicPainter extends CustomPainter {
  final BorderRadius borderRadius;

  GlassmorphicPainter({
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Add subtle edge highlights
    final edgeHighlightPaint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw top edge highlight
    final topPath = Path()
      ..moveTo(borderRadius.topLeft.x, 1)
      ..lineTo(size.width - borderRadius.topRight.x, 1);

    canvas.drawPath(topPath, edgeHighlightPaint);

    // Draw bottom edge shadow
    final edgeShadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    final bottomPath = Path()
      ..moveTo(borderRadius.bottomLeft.x, size.height - 1)
      ..lineTo(size.width - borderRadius.bottomRight.x, size.height - 1);

    canvas.drawPath(bottomPath, edgeShadowPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

// A futuristic container that combines GlassmorphicCard with the Apple-inspired design
class FuturisticContainer extends StatelessWidget {
  final Widget child;
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final BorderRadius? borderRadius;
  final Gradient? gradient;
  final bool showShadow;

  const FuturisticContainer({
    Key? key,
    required this.child,
    this.width = double.infinity,
    this.height = double.infinity,
    this.padding = const EdgeInsets.all(24.0),
    this.borderRadius,
    this.gradient,
    this.showShadow = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(24),
        gradient: gradient ?? LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1A1A2E).withOpacity(0.95),
            const Color(0xFF16213E).withOpacity(0.9),
            const Color(0xFF0F3460).withOpacity(0.85),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
        border: Border.all(
          color: Colors.white.withOpacity(0.15),
          width: 1,
        ),
        boxShadow: showShadow ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.4),
            blurRadius: 30,
            offset: const Offset(0, 15),
            spreadRadius: -5,
          ),
          BoxShadow(
            color: const Color(0xFF0F3460).withOpacity(0.3),
            blurRadius: 60,
            offset: const Offset(0, 30),
            spreadRadius: -10,
          ),
        ] : [],
      ),
      child: Container(
        padding: padding,
        child: child,
      ),
    );
  }
}

// A futuristic button inspired by Apple and futurism design
class FuturisticButton extends StatefulWidget {
  final String text;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final double width;
  final double height;
  final bool isPrimary;
  final IconData? icon;

  const FuturisticButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.width = 200,
    this.height = 48,
    this.isPrimary = true,
    this.icon,
  }) : super(key: key);

  @override
  State<FuturisticButton> createState() => _FuturisticButtonState();
}

class _FuturisticButtonState extends State<FuturisticButton> {
  bool _isPressed = false;
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final primaryGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: _isPressed
          ? [
              const Color(0xFF2A2A3E),
              const Color(0xFF1E1E2E),
              const Color(0xFF16213E),
            ]
          : _isHovered
              ? [
                  const Color(0xFF2A2A3E),
                  const Color(0xFF1E1E2E),
                  const Color(0xFF16213E),
                ]
              : [
                  const Color(0xFF1A1A2E),
                  const Color(0xFF16213E),
                  const Color(0xFF0F3460),
                ],
      stops: const [0.0, 0.5, 1.0],
    );

    final secondaryGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: _isPressed
          ? [
              Colors.black.withOpacity(0.8),
              Colors.black.withOpacity(0.6),
            ]
          : _isHovered
              ? [
                  Colors.black.withOpacity(0.7),
                  Colors.black.withOpacity(0.5),
                ]
              : [
                  Colors.black.withOpacity(0.5),
                  Colors.black.withOpacity(0.3),
                ],
    );

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTapDown: (_) => setState(() => _isPressed = true),
        onTapUp: (_) => setState(() => _isPressed = false),
        onTapCancel: () => setState(() => _isPressed = false),
        onTap: widget.onPressed,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            gradient: widget.backgroundColor != null
                ? null
                : (widget.isPrimary ? primaryGradient : secondaryGradient),
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(widget.height / 2),
            boxShadow: _isPressed
                ? []
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                    if (_isHovered)
                      BoxShadow(
                        color: (widget.isPrimary
                            ? const Color(0xFF16213E)
                            : Colors.black).withOpacity(0.4),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                  ],
            border: Border.all(
              color: Colors.white.withOpacity(_isPressed ? 0.1 : 0.2),
              width: 1,
            ),
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: widget.textColor ?? Colors.white,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Text(
                    widget.text,
                    style: TextStyle(
                      color: widget.textColor ?? Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.3,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}