import 'dart:convert';
import 'dart:math' show min;
import 'package:flutter/foundation.dart';

/// Model class for storing historical market sentiment data
class SentimentHistoryEntry {
  final DateTime date;
  final double value;
  final Map<String, double> metrics;

  SentimentHistoryEntry({
    required this.date,
    required this.value,
    required this.metrics,
  });

  /// Create a SentimentHistoryEntry from a JSON map
  factory SentimentHistoryEntry.fromJson(Map<String, dynamic> json) {
    try {
      // Handle date parsing
      DateTime date;
      try {
        date = DateTime.parse(json['date']);
      } catch (e) {
        debugPrint('Error parsing date: ${json['date']}. Using current date.');
        date = DateTime.now();
      }

      // Handle value parsing
      double value;
      try {
        if (json['value'] is int) {
          value = (json['value'] as int).toDouble();
        } else if (json['value'] is double) {
          value = json['value'];
        } else if (json['value'] is String) {
          value = double.parse(json['value']);
        } else {
          debugPrint('Invalid value type: ${json['value']}. Using default 50.0');
          value = 50.0;
        }
      } catch (e) {
        debugPrint('Error parsing value: ${json['value']}. Using default 50.0');
        value = 50.0;
      }

      // Handle metrics parsing
      Map<String, double> metrics = {};
      try {
        if (json['metrics'] != null) {
          final metricsJson = json['metrics'];
          if (metricsJson is Map) {
            metricsJson.forEach((key, value) {
              if (value is int) {
                metrics[key.toString()] = value.toDouble();
              } else if (value is double) {
                metrics[key.toString()] = value;
              } else if (value is String) {
                try {
                  metrics[key.toString()] = double.parse(value);
                } catch (e) {
                  // Skip invalid values
                }
              }
            });
          }
        }
      } catch (e) {
        debugPrint('Error parsing metrics: $e. Using empty metrics.');
      }

      return SentimentHistoryEntry(
        date: date,
        value: value,
        metrics: metrics,
      );
    } catch (e) {
      debugPrint('Error creating SentimentHistoryEntry from JSON: $e');
      // Return a default entry
      return SentimentHistoryEntry(
        date: DateTime.now(),
        value: 50.0,
        metrics: {},
      );
    }
  }

  /// Create a copy of this entry with updated values
  SentimentHistoryEntry copyWith({
    DateTime? date,
    double? value,
    Map<String, double>? metrics,
  }) {
    return SentimentHistoryEntry(
      date: date ?? this.date,
      value: value ?? this.value,
      metrics: metrics ?? this.metrics,
    );
  }

  /// Convert this SentimentHistoryEntry to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'value': value,
      'metrics': metrics,
    };
  }
}

/// Model class for storing a collection of historical sentiment data
class SentimentHistory {
  final List<SentimentHistoryEntry> entries;

  SentimentHistory({required this.entries});

  /// Create a SentimentHistory from a JSON string
  factory SentimentHistory.fromJson(String jsonString) {
    try {
      debugPrint('Parsing SentimentHistory from JSON: ${jsonString.substring(0, min(100, jsonString.length))}...');

      // Decode the JSON string
      final jsonData = json.decode(jsonString);
      List<dynamic> jsonList;

      // Handle different possible formats
      if (jsonData is List) {
        // Direct list of entries
        jsonList = jsonData;
        debugPrint('JSON is a list with ${jsonList.length} entries');
      } else if (jsonData is Map) {
        if (jsonData.containsKey('entries')) {
          // Object with 'entries' field
          jsonList = jsonData['entries'] as List;
          debugPrint('JSON is a map with entries field, ${jsonList.length} entries');
        } else if (jsonData.containsKey('data') && jsonData['data'] is List) {
          // Object with 'data' field
          jsonList = jsonData['data'] as List;
          debugPrint('JSON is a map with data field, ${jsonList.length} entries');
        } else {
          // Try to use the map itself as a single entry
          debugPrint('JSON is a map without entries field, trying to parse as single entry');
          try {
            // Convert to Map<String, dynamic> explicitly
            final Map<String, dynamic> entryMap = {};
            jsonData.forEach((key, value) {
              entryMap[key.toString()] = value;
            });

            final entry = SentimentHistoryEntry.fromJson(entryMap);
            return SentimentHistory(entries: [entry]);
          } catch (e) {
            debugPrint('Failed to parse map as single entry: $e');
            return SentimentHistory(entries: []);
          }
        }
      } else {
        debugPrint('Invalid JSON format for SentimentHistory: $jsonData');
        return SentimentHistory(entries: []);
      }

      // Parse each entry
      final entries = <SentimentHistoryEntry>[];
      for (var entryJson in jsonList) {
        try {
          final entry = SentimentHistoryEntry.fromJson(entryJson);
          entries.add(entry);
        } catch (e) {
          debugPrint('Error parsing entry: $e');
          // Continue with other entries
        }
      }

      debugPrint('Successfully parsed ${entries.length} entries');
      return SentimentHistory(entries: entries);
    } catch (e) {
      debugPrint('Error parsing SentimentHistory from JSON: $e');
      return SentimentHistory(entries: []);
    }
  }

  /// Convert this SentimentHistory to a JSON string
  String toJson() {
    try {
      final entriesJson = entries.map((entry) => entry.toJson()).toList();
      final json = {'entries': entriesJson};
      final jsonString = jsonEncode(json);
      debugPrint('Converted SentimentHistory to JSON: ${entries.length} entries');
      return jsonString;
    } catch (e) {
      debugPrint('Error converting SentimentHistory to JSON: $e');
      return '{"entries":[]}';
    }
  }

  /// Get the average sentiment value for a specific period
  double getAverageSentiment(int days) {
    if (entries.isEmpty) {
      return 50.0; // Default neutral value
    }

    final now = DateTime.now();
    final cutoffDate = now.subtract(Duration(days: days));

    final relevantEntries = entries.where((entry) => entry.date.isAfter(cutoffDate));
    if (relevantEntries.isEmpty) {
      return entries.first.value; // Return most recent if no entries in period
    }

    final sum = relevantEntries.fold(0.0, (sum, entry) => sum + entry.value);
    return sum / relevantEntries.length;
  }

  /// Get the trend direction over a specific period
  double getTrendDirection(int days) {
    if (entries.length < 2) {
      return 0.0; // No trend with less than 2 entries
    }

    final now = DateTime.now();
    final cutoffDate = now.subtract(Duration(days: days));

    final relevantEntries = entries
        .where((entry) => entry.date.isAfter(cutoffDate))
        .toList()
      ..sort((a, b) => a.date.compareTo(b.date)); // Sort by date (oldest first)

    if (relevantEntries.length < 2) {
      return 0.0; // No trend with less than 2 relevant entries
    }

    // Calculate linear regression
    double sumX = 0;
    double sumY = 0;
    double sumXY = 0;
    double sumX2 = 0;
    final n = relevantEntries.length;

    // Convert dates to days from first entry
    final firstDate = relevantEntries.first.date;
    final xValues = relevantEntries
        .map((entry) => entry.date.difference(firstDate).inDays.toDouble())
        .toList();
    final yValues = relevantEntries.map((entry) => entry.value).toList();

    for (int i = 0; i < n; i++) {
      sumX += xValues[i];
      sumY += yValues[i];
      sumXY += xValues[i] * yValues[i];
      sumX2 += xValues[i] * xValues[i];
    }

    // Calculate slope of the linear regression line
    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);

    return slope;
  }

  /// Get the entry for a specific date
  SentimentHistoryEntry? getEntryForDate(DateTime date) {
    final targetDate = DateTime(date.year, date.month, date.day);
    return entries.firstWhere(
      (entry) => DateTime(entry.date.year, entry.date.month, entry.date.day)
          .isAtSameMomentAs(targetDate),
      orElse: () => SentimentHistoryEntry(
        date: targetDate,
        value: 50.0, // Default neutral value
        metrics: {
          'Fear & Greed Index': 50.0,
          'News Sentiment': 50.0,
          'Holders Score': 50.0,
          'Volume Score': 50.0,
          'Social Engagement': 50.0,
          'Price Volatility': 50.0,
          'Bitcoin Dominance': 50.0,
        },
      ),
    );
  }

  /// Get the entry for yesterday
  SentimentHistoryEntry? getYesterdayEntry() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return getEntryForDate(yesterday);
  }

  /// Get the entry for last week
  SentimentHistoryEntry? getLastWeekEntry() {
    final lastWeek = DateTime.now().subtract(const Duration(days: 7));
    return getEntryForDate(lastWeek);
  }

  /// Add a new entry to the history
  void addEntry(SentimentHistoryEntry entry) {
    // Remove any existing entry for the same date
    entries.removeWhere((e) =>
      DateTime(e.date.year, e.date.month, e.date.day)
        .isAtSameMomentAs(DateTime(entry.date.year, entry.date.month, entry.date.day))
    );

    // Add the new entry
    entries.add(entry);

    // Sort entries by date (newest first)
    entries.sort((a, b) => b.date.compareTo(a.date));

    // Keep only the last 30 days of data
    if (entries.length > 30) {
      entries.removeRange(30, entries.length);
    }
  }
}
