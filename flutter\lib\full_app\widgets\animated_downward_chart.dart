import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Анимированный график падения
/// Показывает график с трендом падения и анимирует его
class AnimatedDown<PERSON><PERSON><PERSON> extends StatefulWidget {
  final Color color;
  final double width;
  final double height;
  final bool small;

  const AnimatedDownwardChart({
    Key? key,
    required this.color,
    this.width = 40.0,
    this.height = 20.0,
    this.small = false,
  }) : super(key: key);

  @override
  _AnimatedDownwardChartState createState() => _AnimatedDownwardChartState();
}

class _AnimatedDownwardChartState extends State<AnimatedDownwardChart> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _drawAnimation;
  late Animation<double> _pulseAnimation;
  Timer? _animationTimer;

  @override
  void initState() {
    super.initState();

    // Настройка контроллера анимации
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Анимация рисования линии
    _drawAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.7, curve: Curves.easeInOut),
      ),
    );

    // Анимация пульсации
    _pulseAnimation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween<double>(begin: 1.0, end: 1.3), weight: 1),
      TweenSequenceItem(tween: Tween<double>(begin: 1.3, end: 1.0), weight: 1),
    ]).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.7, 1.0, curve: Curves.easeInOut),
      ),
    );

    // Настройка таймера для запуска анимации каждые 15 секунд
    _animationTimer = Timer.periodic(const Duration(seconds: 15), (_) {
      if (mounted) {
        _controller.forward(from: 0.0);
      }
    });

    // Запускаем анимацию сразу для демонстрации
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _controller.forward(from: 0.0);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(4.0),
            border: Border.all(
              color: Colors.red.withOpacity(0.3),
              width: 0.5,
            ),
          ),
          child: CustomPaint(
            painter: DownwardChartPainter(
              color: widget.color,
              drawProgress: _drawAnimation.value,
              pulseScale: _pulseAnimation.value,
            ),
            size: Size(widget.width, widget.height),
          ),
        );
      },
    );
  }
}

/// Кастомный painter для рисования графика падения
class DownwardChartPainter extends CustomPainter {
  final Color color;
  final double drawProgress;
  final double pulseScale;

  DownwardChartPainter({
    required this.color,
    required this.drawProgress,
    required this.pulseScale,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;

    final path = Path();
    final width = size.width;
    final height = size.height;

    // Определяем точки для графика падения, как на изображении
    // Начинаем с горизонтальной линии, затем резкое падение
    final points = [
      Offset(0, height * 0.3),                  // Начальная точка
      Offset(width * 0.3, height * 0.3),        // Горизонтальная линия
      Offset(width * 0.5, height * 0.5),        // Начало падения
      Offset(width * 0.7, height * 0.8),        // Продолжение падения
      Offset(width, height * 0.7),              // Небольшой отскок в конце
    ];

    // Рисуем только часть пути в зависимости от прогресса анимации
    final visiblePoints = (points.length * drawProgress).ceil();
    if (visiblePoints > 0) {
      path.moveTo(points[0].dx, points[0].dy);

      for (int i = 1; i < visiblePoints; i++) {
        if (i == points.length - 1 && drawProgress < 1.0) {
          // Интерполируем последнюю видимую точку
          final lastVisiblePoint = Offset(
            points[i-1].dx + (points[i].dx - points[i-1].dx) * ((drawProgress * points.length) - (i-1)),
            points[i-1].dy + (points[i].dy - points[i-1].dy) * ((drawProgress * points.length) - (i-1)),
          );
          path.lineTo(lastVisiblePoint.dx, lastVisiblePoint.dy);
        } else {
          path.lineTo(points[i].dx, points[i].dy);
        }
      }

      canvas.drawPath(path, paint);

      // Рисуем пульсирующую точку в конце видимой части графика
      if (visiblePoints > 1) {
        final lastPoint = visiblePoints == points.length
            ? points[points.length - 1]
            : Offset(
                points[visiblePoints-1].dx + (points[visiblePoints].dx - points[visiblePoints-1].dx) * ((drawProgress * points.length) - (visiblePoints-1)),
                points[visiblePoints-1].dy + (points[visiblePoints].dy - points[visiblePoints-1].dy) * ((drawProgress * points.length) - (visiblePoints-1)),
              );

        // Рисуем пульсирующую точку
        final dotPaint = Paint()
          ..color = color
          ..style = PaintingStyle.fill;

        canvas.drawCircle(
          lastPoint,
          2.0 * (drawProgress >= 0.7 ? pulseScale : 1.0),
          dotPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant DownwardChartPainter oldDelegate) {
    return oldDelegate.drawProgress != drawProgress ||
           oldDelegate.pulseScale != pulseScale ||
           oldDelegate.color != color;
  }
}
