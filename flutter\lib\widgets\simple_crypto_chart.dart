import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/crypto_currency.dart';

class SimpleCryptoChart extends StatelessWidget {
  final List<double> prices;
  final Color color;
  final double strokeWidth;
  final bool showArea;
  final bool showGrid;
  final bool showLabels;
  final String timeframe;
  final double? height;
  final double? width;

  const SimpleCryptoChart({
    super.key,
    required this.prices,
    required this.color,
    this.strokeWidth = 2.0,
    this.showArea = true,
    this.showGrid = false,
    this.showLabels = false,
    this.timeframe = '1h',
    this.height,
    this.width,
  });

  /// Factory constructor to create a chart from a CryptoCurrency object
  factory SimpleCryptoChart.fromCrypto({
    required CryptoCurrency crypto,
    required String timeframe,
    Color? color,
    double strokeWidth = 2.0,
    bool showArea = true,
    bool showGrid = false,
    bool showLabels = false,
    double? height,
    double? width,
  }) {
    // Determine color based on price change if not provided
    final chartColor = color ??
        (crypto.priceChangePercentage24h >= 0 ? Colors.green : Colors.red);

    // Get price data based on timeframe
    final prices = _getPricesForTimeframe(crypto, timeframe);

    return SimpleCryptoChart(
      prices: prices,
      color: chartColor,
      strokeWidth: strokeWidth,
      showArea: showArea,
      showGrid: showGrid,
      showLabels: showLabels,
      timeframe: timeframe,
      height: height,
      width: width,
    );
  }

  /// Helper method to get prices for a specific timeframe
  static List<double> _getPricesForTimeframe(CryptoCurrency crypto, String timeframe) {
    // If we have price history, use it
    if (crypto.priceHistory.isNotEmpty) {
      // Получаем данные в зависимости от таймфрейма
      switch (timeframe) {
        case '1h':
          // Берем последние 12 точек для часового графика (с 5-минутными интервалами)
          // Ограничиваем количество точек до 12 для оптимальной производительности
          final points = crypto.priceHistory.take(12).map((p) => p.price).toList();

          // Если у нас меньше 12 точек, дублируем последнюю для заполнения
          if (points.length < 12) {
            final lastPrice = points.isNotEmpty ? points.last : crypto.price;
            while (points.length < 12) {
              points.add(lastPrice);
            }
          }

          return points;

        case '24h':
          // Берем последние 24 точки для дневного графика (с часовыми интервалами)
          final points = crypto.priceHistory.take(24).map((p) => p.price).toList();

          // Если у нас меньше 24 точек, дублируем последнюю для заполнения
          if (points.length < 24) {
            final lastPrice = points.isNotEmpty ? points.last : crypto.price;
            while (points.length < 24) {
              points.add(lastPrice);
            }
          }

          return points;

        case '7d':
          // Берем последние 7 точек для недельного графика (с дневными интервалами)
          final points = crypto.priceHistory.skip(24).take(7).map((p) => p.price).toList();

          // Если у нас меньше 7 точек, дублируем последнюю для заполнения
          if (points.length < 7) {
            final lastPrice = points.isNotEmpty ? points.last : crypto.price;
            while (points.length < 7) {
              points.add(lastPrice);
            }
          }

          return points;

        case '30d':
          // Берем последние 30 точек для месячного графика (с дневными интервалами)
          final points = crypto.priceHistory.skip(31).take(30).map((p) => p.price).toList();

          // Если у нас меньше 30 точек, дублируем последнюю для заполнения
          if (points.length < 30) {
            final lastPrice = points.isNotEmpty ? points.last : crypto.price;
            while (points.length < 30) {
              points.add(lastPrice);
            }
          }

          return points;

        case 'all':
        default:
          // Возвращаем всю историю цен
          return crypto.priceHistory.map((p) => p.price).toList();
      }
    }

    // Если нет истории цен, генерируем реалистичные данные для графика
    final basePrice = crypto.price;
    final priceChange = crypto.priceChangePercentage24h / 100; // Конвертируем в десятичное число

    // Количество точек для графика
    const int numPoints = 24;

    // Создаем список для хранения сгенерированных цен
    final List<double> prices = [];

    // Создаем генератор случайных чисел с фиксированным зерном для стабильности
    final random = math.Random(crypto.symbol.hashCode + timeframe.hashCode);

    // Определяем, является ли изменение цены положительным
    final bool isPositive = priceChange >= 0;

    // Создаем начальную и конечную цены на основе процентного изменения
    final double startPrice = basePrice / (1 + priceChange);
    final double endPrice = basePrice;

    // Определяем максимальное отклонение для колебаний цены
    final double maxDeviation = basePrice * 0.02; // 2% от базовой цены

    // Генерируем промежуточные точки с реалистичными колебаниями
    for (int i = 0; i < numPoints; i++) {
      // Нормализованная позиция от 0 до 1
      final double t = i / (numPoints - 1);

      // Базовая цена с линейной интерполяцией от начальной до конечной
      final double baseValue = startPrice + (endPrice - startPrice) * t;

      // Добавляем синусоидальные колебания для реалистичности
      // Амплитуда колебаний уменьшается к концу графика
      final double oscillation = math.sin(t * math.pi * 3) * maxDeviation * (1 - t * 0.5);

      // Добавляем небольшой случайный шум
      final double noise = (random.nextDouble() * 2 - 1) * maxDeviation * 0.2;

      // Вычисляем финальную цену
      double price = baseValue + oscillation + noise;

      // Убеждаемся, что цена не отклоняется слишком сильно от тренда
      if (isPositive) {
        // Для положительного тренда цена должна расти к концу
        price = math.max(price, startPrice + (endPrice - startPrice) * t * 0.8);
      } else {
        // Для отрицательного тренда цена должна падать к концу
        price = math.min(price, startPrice + (endPrice - startPrice) * t * 0.8);
      }

      // Добавляем цену в список
      prices.add(price);
    }

    // Убеждаемся, что последняя цена точно равна текущей цене
    if (prices.isNotEmpty) {
      prices[prices.length - 1] = basePrice;
    }

    return prices;
  }

  @override
  Widget build(BuildContext context) {
    // Если данных нет, генерируем базовые данные
    final List<double> chartPrices = prices.isEmpty
        ? _generateDefaultPrices()
        : prices;

    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        size: Size.infinite,
        painter: _SimpleChartPainter(
          prices: chartPrices,
          color: color,
          strokeWidth: strokeWidth,
          showArea: showArea,
          showGrid: showGrid,
          showLabels: showLabels,
        ),
      ),
    );
  }

  // Генерация базовых данных для графика
  List<double> _generateDefaultPrices() {
    final List<double> defaultPrices = [];
    for (int i = 0; i < 10; i++) {
      defaultPrices.add(100.0 + i * 5.0);
    }
    return defaultPrices;
  }
}

class _SimpleChartPainter extends CustomPainter {
  final List<double> prices;
  final Color color;
  final double strokeWidth;
  final bool showArea;
  final bool showGrid;
  final bool showLabels;

  _SimpleChartPainter({
    required this.prices,
    required this.color,
    required this.strokeWidth,
    required this.showArea,
    required this.showGrid,
    required this.showLabels,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (prices.isEmpty) return;

    final double width = size.width;
    final double height = size.height;

    // Find min and max values
    double minValue = prices.reduce((a, b) => a < b ? a : b);
    double maxValue = prices.reduce((a, b) => a > b ? a : b);

    // Увеличиваем разницу между min и max для более выраженного графика
    final double range = maxValue - minValue;
    final double padding = range * 0.2; // Увеличиваем отступ для более выраженного графика
    minValue -= padding;
    maxValue += padding;

    // Ensure there's a range to work with
    if (maxValue - minValue < 0.000001) {
      maxValue = minValue + 1;
    }

    // Calculate scaling factors
    final double xScale = width / (prices.length - 1);
    final double yScale = height / (maxValue - minValue);

    // Draw grid if needed
    if (showGrid) {
      _drawGrid(canvas, size, minValue, maxValue);
    }

    // Create path for the line
    final Path linePath = Path();

    // Move to the first point
    final double firstY = height - (prices[0] - minValue) * yScale;
    linePath.moveTo(0, firstY);

    // Используем более плавную кривую для соединения точек
    if (prices.length > 2) {
      // Создаем точки для кривой
      final List<Offset> points = [];
      for (int i = 0; i < prices.length; i++) {
        final double x = i * xScale;
        final double y = height - (prices[i] - minValue) * yScale;
        points.add(Offset(x, y));
      }

      // Используем кривую с натяжением для более плавного и естественного вида
      for (int i = 0; i < points.length - 1; i++) {
        final Offset p0 = i > 0 ? points[i - 1] : points[i];
        final Offset p1 = points[i];
        final Offset p2 = points[i + 1];
        final Offset p3 = i < points.length - 2 ? points[i + 2] : p2;

        // Вычисляем контрольные точки для кривой Безье
        // Используем метод Catmull-Rom для более плавной кривой
        final double tension = 0.5; // Параметр натяжения (0.0-1.0)

        final double x1 = p1.dx;
        final double y1 = p1.dy;
        final double x2 = p2.dx;
        final double y2 = p2.dy;

        final double controlX1 = x1 + (p2.dx - p0.dx) * tension / 3;
        final double controlY1 = y1 + (p2.dy - p0.dy) * tension / 3;
        final double controlX2 = x2 - (p3.dx - p1.dx) * tension / 3;
        final double controlY2 = y2 - (p3.dy - p1.dy) * tension / 3;

        // Добавляем кубическую кривую Безье
        linePath.cubicTo(controlX1, controlY1, controlX2, controlY2, x2, y2);
      }
    } else {
      // Если у нас мало точек, просто соединяем их линиями
      for (int i = 1; i < prices.length; i++) {
        final double x = i * xScale;
        final double y = height - (prices[i] - minValue) * yScale;
        linePath.lineTo(x, y);
      }
    }

    // Create paint for the line with improved appearance
    final Paint linePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true; // Сглаживание для более качественного отображения

    // Draw the line
    canvas.drawPath(linePath, linePaint);

    // Create and draw the area if needed
    if (showArea) {
      final Path areaPath = Path.from(linePath);

      // Complete the path to form a closed shape
      areaPath.lineTo(width, height);
      areaPath.lineTo(0, height);
      areaPath.close();

      // Create paint for the area with improved gradient
      final Paint areaPaint = Paint()
        ..shader = LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            color.withAlpha(120), // Более заметный верхний цвет
            color.withAlpha(10),  // Почти прозрачный нижний цвет
          ],
          stops: const [0.0, 0.9], // Более плавный градиент
        ).createShader(Rect.fromLTWH(0, 0, width, height))
        ..style = PaintingStyle.fill;

      // Draw the area
      canvas.drawPath(areaPath, areaPaint);
    }

    // Draw labels if needed
    if (showLabels) {
      _drawLabels(canvas, size, minValue, maxValue);
    }
  }

  void _drawGrid(Canvas canvas, Size size, double minValue, double maxValue) {
    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(51) // 0.2 * 255 = 51
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Draw horizontal grid lines
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // Draw vertical grid lines
    for (int i = 0; i <= 6; i++) {
      final x = size.width * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }
  }

  void _drawLabels(Canvas canvas, Size size, double minValue, double maxValue) {
    final textPaint = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Draw price labels
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxValue - (i / 4) * (maxValue - minValue);

      textPaint.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );

      textPaint.layout();
      textPaint.paint(canvas, Offset(size.width - textPaint.width - 4, y - textPaint.height / 2));
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  @override
  bool shouldRepaint(_SimpleChartPainter oldDelegate) {
    return oldDelegate.prices != prices ||
        oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.showArea != showArea ||
        oldDelegate.showGrid != showGrid ||
        oldDelegate.showLabels != showLabels;
  }
}
