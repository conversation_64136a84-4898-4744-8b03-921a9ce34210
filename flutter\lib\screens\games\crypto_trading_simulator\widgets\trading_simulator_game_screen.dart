import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../providers/trading_simulator_provider.dart';
import 'candlestick_chart.dart';
import 'trade_panel.dart';
import 'infinite_patterns_stats_panel.dart';
import 'game_over_dialog.dart';

class TradingSimulatorGameScreen extends StatefulWidget {
  const TradingSimulatorGameScreen({Key? key}) : super(key: key);

  @override
  State<TradingSimulatorGameScreen> createState() => _TradingSimulatorGameScreenState();
}

class _TradingSimulatorGameScreenState extends State<TradingSimulatorGameScreen> {
  @override
  void initState() {
    super.initState();
    // Check for game over state when balance changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<TradingSimulatorProvider>(context, listen: false);
      if (provider.balance <= 0) {
        _showGameOverDialog();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<TradingSimulatorProvider>(context);

    // Check for game over
    if (provider.balance <= 0 && provider.isTradeActive) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showGameOverDialog();
      });
    }

    return Container(
      color: Colors.black,
      child: SafeArea(
        child: Column(
          children: [
            _buildHeader(provider),

            if (provider.selectedMode == GameMode.infinitePatterns)
              InfinitePatternsStatsPanel(
                roundsPlayed: provider.roundsPlayed,
                winPercentage: provider.roundsPlayed > 0
                    ? (provider.wins / provider.roundsPlayed * 100).toInt()
                    : 0,
                deaths: provider.deaths,
                currentWinStreak: provider.currentWinStreak,
                bestWinStreak: provider.bestWinStreak,
              ).animate().fadeIn(duration: 300.ms),

            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                decoration: BoxDecoration(
                  color: const Color(0xFF121212),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey.shade800,
                    width: 1,
                  ),
                ),
                child: provider.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : provider.candles.isEmpty
                        ? const Center(
                            child: Text(
                              'No data available',
                              style: TextStyle(color: Colors.white),
                            ),
                          )
                        : ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: CandlestickChart(
                              candles: provider.candles,
                              entryCandle: provider.entryCandle,
                              resultCandle: provider.resultCandle,
                              action: provider.currentAction,
                            ),
                          ),
              ),
            ),

            Container(
              margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: const Color(0xFF0D2840),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.blue.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Balance:',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '\$${provider.balance.toStringAsFixed(2)}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (provider.isTradeActive && provider.lastProfit != 0)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Row(
                        children: [
                          const Text(
                            'Last Trade:',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            provider.lastProfit > 0 ? 'Correct!' : 'Wrong!',
                            style: TextStyle(
                              color: provider.lastProfit > 0 ? Colors.green : Colors.red,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${provider.lastProfit > 0 ? '+' : ''}\$${provider.lastProfit.toStringAsFixed(2)}',
                            style: TextStyle(
                              color: provider.lastProfit > 0 ? Colors.green : Colors.red,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),

            Container(
              margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
              child: provider.isTradeActive
                ? ElevatedButton(
                    onPressed: () => provider.nextRound(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0066CC),
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 56),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: const Text(
                      'NEXT LEVEL',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1.2,
                      ),
                    ),
                  )
                : Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => provider.executeTrade(TradeAction.buy),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            minimumSize: const Size(0, 56),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: const [
                              Icon(Icons.arrow_upward, size: 20),
                              SizedBox(width: 8),
                              Text(
                                'BUY',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => provider.executeTrade(TradeAction.sell),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            minimumSize: const Size(0, 56),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: const [
                              Icon(Icons.arrow_downward, size: 20),
                              SizedBox(width: 8),
                              Text(
                                'SELL',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(TradingSimulatorProvider provider) {
    final String modeTitle = provider.selectedMode == GameMode.custom
        ? 'Custom Mode'
        : 'Infinite Patterns';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Fake back button to match the design
              Container(
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.grey.shade800, width: 1),
                ),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => provider.navigateBack(),
                ),
              ),
              const Text(
                'Back to Menu',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              // Fake status icons to match the design
              const Icon(Icons.airplanemode_active, color: Colors.white, size: 18),
              const SizedBox(width: 8),
              const Icon(Icons.wifi, color: Colors.white, size: 18),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Text(
                  '18',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            modeTitle,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _showGameOverDialog() {
    final provider = Provider.of<TradingSimulatorProvider>(context, listen: false);

    if (provider.selectedMode == GameMode.infinitePatterns) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => GameOverDialog(
          onTopUp: () {
            provider.topUpBalance();
            Navigator.of(context).pop();
          },
          onRestart: () {
            provider.resetGame();
            Navigator.of(context).pop();
          },
          onBackToMenu: () {
            provider.navigateBack();
            Navigator.of(context).pop();
          },
        ),
      );
    }
  }
}
