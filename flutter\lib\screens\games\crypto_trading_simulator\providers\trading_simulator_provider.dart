import 'package:flutter/material.dart';
import 'dart:math';
import '../models/candle_data.dart';
import '../services/binance_api_service.dart';

enum SimulatorScreen {
  modeSelection,
  customModeSettings,
  infinitePatternsLeverage,
  gameScreen,
}

enum GameMode {
  custom,
  infinitePatterns,
}

enum TradeAction {
  buy,
  sell,
  none,
}

class TradingSimulatorProvider extends ChangeNotifier {
  // Navigation state - сразу показываем экран выбора режима
  SimulatorScreen _currentScreen = SimulatorScreen.modeSelection;
  SimulatorScreen get currentScreen => _currentScreen;

  // Game configuration
  GameMode _selectedMode = GameMode.custom;
  GameMode get selectedMode => _selectedMode;

  String _selectedSymbol = 'BTCUSDT';
  String get selectedSymbol => _selectedSymbol;

  String _selectedTimeframe = '30m';
  String get selectedTimeframe => _selectedTimeframe;

  int _selectedLeverage = 1;
  int get selectedLeverage => _selectedLeverage;

  // Game state
  double _balance = 1000.0;
  double get balance => _balance;

  int _roundsPlayed = 0;
  int get roundsPlayed => _roundsPlayed;

  int _wins = 0;
  int get wins => _wins;

  int _deaths = 0;
  int get deaths => _deaths;

  int _currentWinStreak = 0;
  int get currentWinStreak => _currentWinStreak;

  int _bestWinStreak = 0;
  int get bestWinStreak => _bestWinStreak;

  // Chart data
  List<CandleData> _candles = [];
  List<CandleData> get candles => _candles;

  int _visibleCandlesCount = 300;
  int get visibleCandlesCount => _visibleCandlesCount;

  // Trade state
  TradeAction _currentAction = TradeAction.none;
  TradeAction get currentAction => _currentAction;

  CandleData? _entryCandle;
  CandleData? get entryCandle => _entryCandle;

  CandleData? _resultCandle;
  CandleData? get resultCandle => _resultCandle;

  // Список результирующих свечей для отображения тренда
  List<CandleData> _resultCandles = [];
  List<CandleData> get resultCandles => _resultCandles;

  double _lastProfit = 0.0;
  double get lastProfit => _lastProfit;

  bool _isTradeActive = false;
  bool get isTradeActive => _isTradeActive;

  // Zoom level for chart
  double _zoomLevel = 1.0;
  double get zoomLevel => _zoomLevel;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  final BinanceApiService _apiService = BinanceApiService();
  final Random _random = Random();

  // Available symbols and timeframes
  final List<String> availableSymbols = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOGEUSDT',
    'XRPUSDT', 'DOTUSDT', 'LTCUSDT', 'LINKUSDT', 'MATICUSDT',
    'SOLUSDT', 'AVAXUSDT', 'UNIUSDT', 'AAVEUSDT', 'SUSHIUSDT',
  ];

  final List<String> availableTimeframes = ['30m', '1h', '4h', '1d'];

  final List<int> availableLeverages = [1, 2, 3, 5, 10, 25, 50, 100, 200, 500, 1000];

  // Navigation methods
  void navigateToScreen(SimulatorScreen screen) {
    _currentScreen = screen;
    notifyListeners();
  }

  void navigateBack() {
    switch (_currentScreen) {
      case SimulatorScreen.customModeSettings:
      case SimulatorScreen.infinitePatternsLeverage:
        _currentScreen = SimulatorScreen.modeSelection;
        break;
      case SimulatorScreen.gameScreen:
        if (_selectedMode == GameMode.custom) {
          _currentScreen = SimulatorScreen.customModeSettings;
        } else {
          _currentScreen = SimulatorScreen.infinitePatternsLeverage;
        }
        break;
      default:
        _currentScreen = SimulatorScreen.modeSelection;
    }
    notifyListeners();
  }

  // Game setup methods
  void selectGameMode(GameMode mode) {
    _selectedMode = mode;
    if (mode == GameMode.custom) {
      navigateToScreen(SimulatorScreen.customModeSettings);
    } else {
      navigateToScreen(SimulatorScreen.infinitePatternsLeverage);
    }
  }

  void selectSymbol(String symbol) {
    _selectedSymbol = symbol;
    notifyListeners();
  }

  void selectRandomSymbol() {
    _selectedSymbol = availableSymbols[_random.nextInt(availableSymbols.length)];
    notifyListeners();
  }

  void selectTimeframe(String timeframe) {
    _selectedTimeframe = timeframe;
    notifyListeners();
  }

  void selectRandomTimeframe() {
    _selectedTimeframe = availableTimeframes[_random.nextInt(availableTimeframes.length)];
    notifyListeners();
  }

  void selectLeverage(int leverage) {
    _selectedLeverage = leverage;
    notifyListeners();
  }

  // Game initialization
  Future<void> startGame() async {
    _isLoading = true;
    notifyListeners();

    // Reset game state for new game
    if (_selectedMode == GameMode.infinitePatterns) {
      _balance = 1000.0;
      _roundsPlayed = 0;
      _wins = 0;
      _deaths = 0;
      _currentWinStreak = 0;
      _bestWinStreak = 0;
    }

    await loadCandles();

    _isLoading = false;
    navigateToScreen(SimulatorScreen.gameScreen);
  }

  // Data loading
  Future<void> loadCandles() async {
    try {
      final fetchedCandles = await _apiService.fetchCandles(
        _selectedSymbol,
        _selectedTimeframe,
        _visibleCandlesCount
      );

      if (fetchedCandles.isNotEmpty) {
        _candles = fetchedCandles;
      } else {
        // Fallback to generated candles if API fails
        _candles = _generateFakeCandles();
      }

      _isTradeActive = false;
      _currentAction = TradeAction.none;
      _entryCandle = null;

      notifyListeners();
    } catch (e) {
      print('Error loading candles: $e');
      // Fallback to generated candles
      _candles = _generateFakeCandles();
      notifyListeners();
    }
  }

  // Generate fake candles for fallback
  List<CandleData> _generateFakeCandles() {
    final List<CandleData> fakeCandles = [];
    final basePrice = 20000.0 + _random.nextDouble() * 10000.0;
    double currentPrice = basePrice;

    for (int i = 0; i < _visibleCandlesCount; i++) {
      final time = DateTime.now().subtract(Duration(minutes: (_visibleCandlesCount - i) * 30)).millisecondsSinceEpoch / 1000;
      final changePercent = (_random.nextDouble() * 0.8 - 0.4) / 100; // -0.4% to +0.4%
      final change = currentPrice * changePercent;

      final open = currentPrice;
      currentPrice += change;
      final close = currentPrice;

      final highExtra = open > close
          ? open * (_random.nextDouble() * 0.2 / 100)
          : close * (_random.nextDouble() * 0.2 / 100);

      final lowExtra = open < close
          ? open * (_random.nextDouble() * 0.2 / 100)
          : close * (_random.nextDouble() * 0.2 / 100);

      final high = max(open, close) + highExtra;
      final low = min(open, close) - lowExtra;

      fakeCandles.add(CandleData(
        time: time,
        open: open,
        high: high,
        low: low,
        close: close,
        volume: _random.nextDouble() * 1000,
      ));
    }

    return fakeCandles;
  }

  // Trading logic
  void executeTrade(TradeAction action) {
    if (_isTradeActive) return;

    _isTradeActive = true;
    _currentAction = action;
    _entryCandle = _candles.last;

    // Сначала обновляем UI, чтобы показать маркер входа
    notifyListeners();

    // Небольшая задержка перед симуляцией результата для лучшего UX
    Future.delayed(const Duration(milliseconds: 300), () {
      _simulateTradeResult();
    });
  }

  void _simulateTradeResult() {
    if (_entryCandle == null) return;

    // Simulate price movement
    final entryPrice = _entryCandle!.close;

    // Увеличим диапазон изменения цены для более наглядного отображения
    final priceChangePercent = (_random.nextDouble() * 4 - 1.0) / 100; // -1.0% to 3.0%
    final newPrice = entryPrice * (1 + priceChangePercent);

    // Determine if trade was successful
    final isSuccess = (_currentAction == TradeAction.buy && newPrice > entryPrice) ||
                     (_currentAction == TradeAction.sell && newPrice < entryPrice);

    // Calculate profit/loss
    final tradeAmount = _balance * 0.1; // 10% of balance per trade
    final priceChange = (newPrice - entryPrice).abs() / entryPrice;
    final profitPercent = priceChange * 100;

    _lastProfit = isSuccess
        ? tradeAmount * (profitPercent / 100) * _selectedLeverage
        : -tradeAmount * (profitPercent / 100) * _selectedLeverage;

    // Update balance and stats
    _balance += _lastProfit;
    _roundsPlayed++;

    if (isSuccess) {
      _wins++;
      _currentWinStreak++;
      _bestWinStreak = max(_bestWinStreak, _currentWinStreak);
    } else {
      _currentWinStreak = 0;
    }

    // Очищаем предыдущие результирующие свечи
    _resultCandles.clear();

    // Создаем несколько результирующих свечей для отображения тренда
    final int numResultCandles = 5; // Количество свечей для отображения тренда

    // Определяем направление движения цены в зависимости от успешности сделки
    final bool isPriceGoingUp = (_currentAction == TradeAction.buy && isSuccess) ||
                               (_currentAction == TradeAction.sell && !isSuccess);

    // Создаем тренд - постепенное изменение цены в нужном направлении
    double currentPrice = entryPrice;
    double targetPrice = newPrice;
    double priceStep = (targetPrice - currentPrice) / numResultCandles;

    for (int i = 0; i < numResultCandles; i++) {
      final resultTime = _entryCandle!.time + (60 * (i + 1)); // Добавляем минуты к времени

      // Для первой свечи открытие равно цене закрытия входной свечи
      double resultOpen = (i == 0) ? currentPrice : _resultCandles.last.close;

      // Для последней свечи закрытие равно целевой цене
      double resultClose = (i == numResultCandles - 1)
          ? targetPrice
          : currentPrice + priceStep * (i + 1) + (_random.nextDouble() * 0.05 - 0.025) / 100 * currentPrice;

      // Добавляем небольшие случайные колебания для реалистичности
      double resultHigh = max(resultOpen, resultClose) + (_random.nextDouble() * 0.1 / 100 * currentPrice);
      double resultLow = min(resultOpen, resultClose) - (_random.nextDouble() * 0.1 / 100 * currentPrice);

      // Создаем результирующую свечу
      final resultCandle = CandleData(
        time: resultTime,
        open: resultOpen,
        high: resultHigh,
        low: resultLow,
        close: resultClose,
        volume: _entryCandle!.volume * (0.6 + _random.nextDouble() * 0.8), // Случайный объем
      );

      // Добавляем свечу в список результирующих свечей
      _resultCandles.add(resultCandle);
    }

    // Сохраняем последнюю свечу как результирующую для отображения маркера
    _resultCandle = _resultCandles.last;

    // Принудительно обновляем UI
    notifyListeners();

    // Добавляем задержку перед добавлением результирующих свечей для лучшей визуализации
    Future.delayed(const Duration(milliseconds: 500), () {
      // Добавляем результирующие свечи в общий список свечей
      for (final candle in _resultCandles) {
        if (!_candles.contains(candle)) {
          _candles.add(candle);
        }
      }

      // Обновляем UI
      notifyListeners();

      // Еще одно обновление через небольшую задержку для гарантии отображения
      Future.delayed(const Duration(milliseconds: 200), () {
        notifyListeners();
      });
    });
  }

  void nextRound() {
    if (_balance <= 0) {
      // Game over
      _deaths++;
      notifyListeners();
      return;
    }

    _isTradeActive = false;
    _currentAction = TradeAction.none;
    _entryCandle = null;
    _resultCandle = null;
    _resultCandles.clear();

    if (_selectedMode == GameMode.infinitePatterns) {
      selectRandomSymbol();
      selectRandomTimeframe();
    }

    loadCandles();
  }

  void topUpBalance() {
    if (_balance <= 0) {
      _balance = 1000.0;
      _deaths++;
      _isTradeActive = false;
      _currentAction = TradeAction.none;
      _entryCandle = null;
      _resultCandle = null;
      _resultCandles.clear();
      notifyListeners();
    }
  }

  void resetGame() {
    _balance = 1000.0;
    _roundsPlayed = 0;
    _wins = 0;
    _deaths = 0;
    _currentWinStreak = 0;
    _bestWinStreak = 0;
    _isTradeActive = false;
    _currentAction = TradeAction.none;
    _entryCandle = null;
    _resultCandle = null;
    _resultCandles.clear();

    if (_selectedMode == GameMode.infinitePatterns) {
      selectRandomSymbol();
      selectRandomTimeframe();
    }

    loadCandles();
  }

  // Chart zoom control methods
  void zoomIn() {
    if (_zoomLevel < 2.0) {
      _zoomLevel += 0.25;
      notifyListeners();
    }
  }

  void zoomOut() {
    if (_zoomLevel > 0.5) {
      _zoomLevel -= 0.25;
      notifyListeners();
    }
  }

  void resetZoom() {
    _zoomLevel = 1.0;
    notifyListeners();
  }
}
