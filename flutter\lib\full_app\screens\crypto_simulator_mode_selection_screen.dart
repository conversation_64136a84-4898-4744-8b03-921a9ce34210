import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../models/trading_simulator_models.dart';
import '../widgets/gradient_background.dart';
import '../widgets/animated_button.dart';

class CryptoSimulatorModeSelectionScreen extends StatefulWidget {
  const CryptoSimulatorModeSelectionScreen({super.key});

  @override
  State<CryptoSimulatorModeSelectionScreen> createState() => _CryptoSimulatorModeSelectionScreenState();
}

class _CryptoSimulatorModeSelectionScreenState extends State<CryptoSimulatorModeSelectionScreen> {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonWidth = screenWidth * 0.8; // Ограничиваем ширину кнопок до 80% экрана
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(CupertinoIcons.back, color: Colors.white),
          onPressed: () {
            // Переходим на экран Courses с вкладкой Games
            debugPrint('Navigating back to Courses screen with Games tab');
            Navigator.of(context).pushNamedAndRemoveUntil(
              '/courses', 
              (route) => false,
              arguments: 2, // Индекс вкладки Games
            );
          },
        ),
      ),
      body: GradientBackground(
        gradientColors: [
          Colors.black,
          const Color(0xFF0D0D15),
          const Color(0xFF101020),
        ],
        child: SafeArea(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Иконка и заголовок
                  const SizedBox(height: 16),
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.candlestick_chart,
                      size: 22,
                      color: Colors.orange,
                    ),
                  ),
                  
                  const SizedBox(height: 10),
                  
                  // Заголовок
                  const Text(
                    'Trading Simulator',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: -0.5,
                    ),
                  ),

                  const SizedBox(height: 2),

                  // Подзаголовок
                  Text(
                    'Choose your mode',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.white.withOpacity(0.7),
                      letterSpacing: -0.5,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Кнопки выбора режима (компактные по ширине)
                  Container(
                    width: buttonWidth,
                    child: _buildModeButtonNew(
                      title: 'Infinite Mode',
                      description: 'Endless trading scenarios with random patterns',
                      icon: CupertinoIcons.infinite,
                      color: Colors.orange,
                      onTap: () => _selectMode(SimulatorMode.infinitePatterns),
                    ),
                  ),

                  const SizedBox(height: 10),

                  Container(
                    width: buttonWidth,
                    child: _buildModeButtonNew(
                      title: 'Custom Mode',
                      description: 'Configure your own trading parameters',
                      icon: CupertinoIcons.slider_horizontal_3,
                      color: Colors.blue,
                      onTap: () => _selectMode(SimulatorMode.custom),
                    ),
                  ),
                  
                  const SizedBox(height: 14),
                  
                  // Кнопка с информацией об игре
                  Container(
                    width: buttonWidth * 0.8, // Делаем кнопку "Как играть" еще уже
                    child: _buildHowToPlayButton(),
                  ),
                  
                  const Spacer(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Компактная по ширине кнопка выбора режима
  Widget _buildModeButtonNew({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Иконка
            Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                color: color.withOpacity(0.15),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: color,
                size: 14,
              ),
            ),
            const SizedBox(width: 8),
            // Текст и описание
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.white.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            // Стрелка
            Icon(
              CupertinoIcons.chevron_right,
              color: Colors.white.withOpacity(0.4),
              size: 12,
            ),
          ],
        ),
      ),
    );
  }
  
  // Компактная по ширине кнопка "Как играть"
  Widget _buildHowToPlayButton() {
    return GestureDetector(
      onTap: () => _showHowToPlayDialog(),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.info_circle,
              color: Colors.white.withOpacity(0.7),
              size: 12,
            ),
            const SizedBox(width: 4),
            Text(
              'How to Play',
              style: TextStyle(
                fontSize: 12,
                color: Colors.white.withOpacity(0.8),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Диалог с информацией об игре
  void _showHowToPlayDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: Colors.grey.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Заголовок
              const Text(
                'How to Play',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Описание игры
              Text(
                'Trading Simulator is a safe environment to practice cryptocurrency trading without risking real money.',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              
              // Шаги
              _buildHowToStep(
                number: '1',
                text: 'Choose either Infinite Mode for varied scenarios or Custom Mode to set specific parameters',
              ),
              _buildHowToStep(
                number: '2',
                text: 'Analyze price chart patterns and decide: Will price go UP or DOWN in the next period?',
              ),
              _buildHowToStep(
                number: '3',
                text: 'Make your trade and see immediate results based on actual historical patterns',
              ),
              _buildHowToStep(
                number: '4',
                text: 'Continue trading to build your virtual portfolio and improve your skills',
              ),
              
              const SizedBox(height: 20),
              
              // Кнопка закрытия
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Text(
                    'Got it',
                    style: TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildHowToStep({required String number, required String text}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 22,
            height: 22,
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.blue,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Handle mode selection
  void _selectMode(SimulatorMode mode) {
    debugPrint('Selected mode: $mode');
    
    // Check if we came from a game over situation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      bool cameFromGameOver = false;
      Navigator.of(context).popUntil((route) {
        debugPrint('Route in stack: ${route.settings.name}');
        // Check if simulator_menu is in the stack
        if (route.settings.name == '/simulator_menu') {
          cameFromGameOver = true;
        }
        return true; // Keep going through the stack for inspection
      });
      
      // Now navigate based on whether we came from game over
      if (mode == SimulatorMode.infinitePatterns) {
        Navigator.pushNamed(context, '/infinite_patterns_leverage');
      } else {
        Navigator.pushNamed(context, '/custom_mode_settings');
      }
    });
  }
}
