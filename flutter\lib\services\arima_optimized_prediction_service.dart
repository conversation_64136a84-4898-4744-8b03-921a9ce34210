import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentiment_history_model.dart';
import '../utils/arima.dart';
import 'optimized_prediction_service.dart';
import 'optimized_sentiment_service.dart';

/// Service for predicting future market sentiment using ARIMA model
/// This is an optimized version that uses ARIMA for predictions
class ArimaOptimizedPredictionService {
  // Singleton instance
  static final ArimaOptimizedPredictionService _instance = ArimaOptimizedPredictionService._internal();

  // Factory constructor
  factory ArimaOptimizedPredictionService() => _instance;

  // Original services for delegating methods
  final OptimizedSentimentService _sentimentService = OptimizedSentimentService();
  final OptimizedPredictionService _originalPredictionService = OptimizedPredictionService();

  // Private constructor
  ArimaOptimizedPredictionService._internal();
  // Keys for storing ARIMA model parameters
  static const String _arimaParamsKey = 'arima_model_params';
  static const String _arimaTimestampKey = 'arima_model_timestamp';
  static const String _arimaPredictionsKey = 'arima_optimized_predictions';

  // Default ARIMA parameters
  static const int _defaultP = 1; // AR order
  static const int _defaultD = 1; // Differencing order
  static const int _defaultQ = 1; // MA order

  // Cache duration for ARIMA model (1 hour)
  static const int _modelCacheDurationMs = 60 * 60 * 1000;

  // ARIMA model instance
  ArimaModel? _arimaModel;

  /// Get or create an ARIMA model
  Future<ArimaModel> _getArimaModel() async {
    // If we already have a model, return it
    if (_arimaModel != null) {
      return _arimaModel!;
    }

    // Try to load model parameters from cache
    final params = await _loadModelParameters();

    if (params != null) {
      // Create model from cached parameters
      _arimaModel = ArimaModel.withCoefficients(
        p: params['p'],
        d: params['d'],
        q: params['q'],
        arCoefficients: List<double>.from(params['ar_coefficients']),
        maCoefficients: List<double>.from(params['ma_coefficients']),
        intercept: params['intercept'],
      );
      debugPrint('Loaded ARIMA model from cache: (${_arimaModel!.p},${_arimaModel!.d},${_arimaModel!.q})');
    } else {
      // Create a new default model
      _arimaModel = ArimaModel(p: _defaultP, d: _defaultD, q: _defaultQ);
      debugPrint('Created new ARIMA model: (${_arimaModel!.p},${_arimaModel!.d},${_arimaModel!.q})');
    }

    return _arimaModel!;
  }

  /// Load ARIMA model parameters from cache
  Future<Map<String, dynamic>?> _loadModelParameters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final paramsJson = prefs.getString(_arimaParamsKey);
      final timestamp = prefs.getInt(_arimaTimestampKey);

      // Check if we have cached parameters and they're not expired
      if (paramsJson != null && timestamp != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - timestamp < _modelCacheDurationMs) {
          final params = jsonDecode(paramsJson);
          debugPrint('Found valid ARIMA model parameters in cache');
          return params;
        } else {
          debugPrint('Cached ARIMA model parameters expired');
        }
      }
    } catch (e) {
      debugPrint('Error loading ARIMA model parameters: $e');
    }

    return null;
  }

  /// Save ARIMA model parameters to cache
  Future<void> _saveModelParameters(ArimaModel model) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final params = model.getParameters();
      final paramsJson = jsonEncode(params);

      await prefs.setString(_arimaParamsKey, paramsJson);
      await prefs.setInt(_arimaTimestampKey, DateTime.now().millisecondsSinceEpoch);

      debugPrint('Saved ARIMA model parameters to cache');
    } catch (e) {
      debugPrint('Error saving ARIMA model parameters: $e');
    }
  }

  /// Train the ARIMA model on historical data
  Future<void> _trainArimaModel(List<double> historicalValues) async {
    try {
      final model = await _getArimaModel();

      // Train the model
      model.train(historicalValues);

      // Save the trained model parameters
      await _saveModelParameters(model);

      debugPrint('Trained ARIMA model on ${historicalValues.length} data points');
    } catch (e) {
      debugPrint('Error training ARIMA model: $e');
    }
  }

  /// Get cached ARIMA predictions
  Future<List<SentimentHistoryEntry>?> _getArimaCachedPredictions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_arimaPredictionsKey);

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        final predictions = jsonList
            .map((json) => SentimentHistoryEntry.fromJson(json as Map<String, dynamic>))
            .toList();

        debugPrint('Retrieved ${predictions.length} ARIMA predictions from cache');
        return predictions;
      }
    } catch (e) {
      debugPrint('Error getting cached ARIMA predictions: $e');
    }

    return null;
  }

  /// Save ARIMA predictions to cache
  Future<void> _saveArimaPredictions(List<SentimentHistoryEntry> predictions) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert predictions to JSON
      final jsonList = predictions.map((entry) => entry.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      // Save predictions
      await prefs.setString(_arimaPredictionsKey, jsonString);

      debugPrint('Saved ${predictions.length} ARIMA predictions to cache');
    } catch (e) {
      debugPrint('Error saving ARIMA predictions to cache: $e');
    }
  }

  /// Get historical data from the sentiment service
  Future<SentimentHistory> getHistoricalData() async {
    return await _sentimentService.getHistoricalData();
  }

  /// Predict future sentiment values using ARIMA
  Future<List<SentimentHistoryEntry>> predictFutureSentiment(int daysAhead) async {
    // Try to use cached predictions first
    final cachedPredictions = await _getArimaCachedPredictions();
    if (cachedPredictions != null && cachedPredictions.length >= daysAhead) {
      debugPrint('Using cached ARIMA predictions');
      return cachedPredictions.take(daysAhead).toList();
    }

    debugPrint('Generating new ARIMA predictions for the next $daysAhead days');

    // Get historical data
    final history = await getHistoricalData();
    final entries = history.entries;

    // Need at least 5 data points for ARIMA
    if (entries.length < 5) {
      debugPrint('Not enough historical data for ARIMA prediction, falling back to default method');
      return await _originalPredictionService.predictFutureSentiment(daysAhead);
    }

    try {
      // Sort entries by date (oldest first for ARIMA)
      entries.sort((a, b) => a.date.compareTo(b.date));

      // Extract values for ARIMA
      final values = entries.map((e) => e.value).toList();

      // Train the ARIMA model
      await _trainArimaModel(values);

      // Get the trained model
      final arimaModel = await _getArimaModel();

      // Generate forecasts
      final forecasts = arimaModel.forecast(daysAhead);

      // Create prediction entries
      final predictions = <SentimentHistoryEntry>[];
      final today = DateTime.now();

      // Calculate volatility for confidence estimation
      double volatility = 0;
      if (entries.length >= 5) {
        final recentValues = entries.take(5).map((e) => e.value).toList();
        final mean = recentValues.reduce((a, b) => a + b) / recentValues.length;
        volatility = recentValues.map((v) => (v - mean) * (v - mean)).reduce((a, b) => a + b) / recentValues.length;
        volatility = sqrt(volatility);
      }

      // Create prediction entries with confidence metrics
      for (int i = 0; i < daysAhead; i++) {
        final futureDate = today.add(Duration(days: i + 1));

        // Clamp the forecast to valid range
        double forecastValue = forecasts[i].clamp(0.0, 100.0);

        // Add small random variation for more realistic forecasts
        // Use a deterministic seed for stable predictions
        final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
        final seed = (dayOfYear * 1000 + i * 100 + forecastValue.round()).toInt();
        final random = Random(seed);

        // Scale noise based on prediction distance and volatility
        final noiseFactor = (i + 1) / daysAhead * 0.5; // More noise for further predictions
        final noise = (random.nextDouble() * 2 - 1) * volatility * noiseFactor;

        // Apply noise to forecast
        forecastValue = (forecastValue + noise).clamp(0.0, 100.0);

        // Calculate confidence (decreases with prediction distance)
        final distanceFactor = (i + 1) / daysAhead;
        final confidenceBase = 0.85;
        final confidenceDecay = pow(1 - distanceFactor, 1.5);
        final confidence = (confidenceBase * (0.7 + 0.3 * confidenceDecay)).clamp(0.3, 0.95);

        debugPrint('ARIMA prediction for day ${i+1} ($futureDate): $forecastValue (confidence: ${confidence * 100}%)');

        // Create metrics map with prediction metadata
        final metrics = <String, double>{
          'confidence': (confidence * 100).clamp(30.0, 90.0),
          'volatility': volatility,
        };

        predictions.add(SentimentHistoryEntry(
          date: futureDate,
          value: forecastValue,
          metrics: metrics,
        ));
      }

      // Cache the predictions
      await _saveArimaPredictions(predictions);

      return predictions;
    } catch (e) {
      debugPrint('Error in ARIMA prediction: $e');
      debugPrint('Falling back to default prediction method');

      // Fall back to the original prediction service
      return await _originalPredictionService.predictFutureSentiment(daysAhead);
    }
  }
}
