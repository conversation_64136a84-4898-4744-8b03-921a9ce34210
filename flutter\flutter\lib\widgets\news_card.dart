import 'package:flutter/material.dart';
import '../models/news_item.dart';
import '../utils/device_type.dart';
import '../../lib/styles/news_card_colors.dart';

class NewsCard extends StatelessWidget {
  final NewsItem newsItem;
  final VoidCallback onTap;

  const NewsCard({
    super.key,
    required this.newsItem,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = DeviceUtils.isDesktop(context);

    return Card(
      margin: EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: isDesktop ? 24.0 : 16.0,
      ),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
        side: BorderSide(
          color: NewsCardColors.cardBorder,
          width: 1.0,
        ),
      ),
      color: null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.0),
        child: Container(
          decoration: BoxDecoration(
            color: NewsCardColors.cardBackground,
            borderRadius: BorderRadius.circular(16.0),
            boxShadow: NewsCardColors.cardShadow,
          ),
          child: isDesktop
              ? _buildDesktopLayout(context)
              : _buildMobileLayout(context),
        ),
      ),
    );
  }

  // Desktop layout with image on the right
  Widget _buildDesktopLayout(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Content on the left
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Source, date and sentiment
                Row(
                  children: [
                    // Source
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                      decoration: BoxDecoration(
                        color: NewsCardColors.meta.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: Text(
                        newsItem.source,
                        style: TextStyle(
                          color: NewsCardColors.source,
                          fontSize: 12.0,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8.0),

                    // Date
                    Text(
                      _formatDate(_getDisplayTime(newsItem)),
                      style: TextStyle(
                        color: NewsCardColors.meta,
                        fontSize: 12.0,
                      ),
                    ),
                    const SizedBox(width: 12.0),

                    // Sentiment indicator
                    _buildSentimentIndicator(),
                  ],
                ),
                const SizedBox(height: 12.0),

                // Title
                Text(
                  newsItem.aiGeneratedTitle ?? newsItem.title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18.0,
                    color: NewsCardColors.title,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12.0),

                // Description
                Text(
                  newsItem.description,
                  style: TextStyle(
                    fontSize: 14.0,
                    color: NewsCardColors.description,
                  ),
                  maxLines: 4,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12.0),

                // Tags
                Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  children: newsItem.tags.map((tag) => _buildTag(tag)).toList(),
                ),

                const SizedBox(height: 12.0),

                // Action buttons
                Row(
                  children: [
                    _buildActionButton(
                      icon: Icons.bookmark_border,
                      label: 'Save',
                      onPressed: () {},
                    ),
                    const SizedBox(width: 16.0),
                    _buildActionButton(
                      icon: Icons.share,
                      label: 'Share',
                      onPressed: () {},
                    ),
                    const SizedBox(width: 16.0),
                    _buildActionButton(
                      icon: Icons.open_in_new,
                      label: 'Read More',
                      onPressed: onTap,
                      isPrimary: true,
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(width: 24.0),

          // Image on the right
          Expanded(
            flex: 1,
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Image.network(
                    newsItem.imageUrl,
                    height: 180,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 180,
                        width: double.infinity,
                        color: Colors.grey[300],
                        child: const Icon(Icons.image_not_supported, size: 50),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Mobile layout with image on top
  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // News image with sentiment indicator
        Stack(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12.0),
                topRight: Radius.circular(12.0),
              ),
              child: Image.network(
                newsItem.imageUrl,
                height: 150,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 150,
                    width: double.infinity,
                    color: Colors.grey[300],
                    child: const Icon(Icons.image_not_supported, size: 50),
                  );
                },
              ),
            ),
            Positioned(
              top: 10,
              right: 10,
              child: _buildSentimentIndicator(),
            ),
          ],
        ),

        // News content
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Source and date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    newsItem.source,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12.0,
                    ),
                  ),
                  Text(
                    _formatDate(_getDisplayTime(newsItem)),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12.0,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8.0),

              // Title
              Text(
                newsItem.aiGeneratedTitle ?? newsItem.title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16.0,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8.0),

              // Description
              Text(
                newsItem.description,
                style: const TextStyle(fontSize: 14.0),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8.0),

              // Tags
              Wrap(
                spacing: 6.0,
                runSpacing: 6.0,
                children: newsItem.tags.map((tag) => _buildTag(tag)).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isPrimary = false,
  }) {
    return TextButton.icon(
      onPressed: onPressed,
      icon: Icon(
        icon,
        color: isPrimary ? NewsCardColors.title : NewsCardColors.meta,
        size: 18,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isPrimary ? NewsCardColors.title : NewsCardColors.meta,
          fontWeight: isPrimary ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: isPrimary
              ? BorderSide(color: NewsCardColors.meta, width: 1)
              : BorderSide.none,
        ),
        backgroundColor: Colors.transparent,
      ),
    );
  }

  Widget _buildSentimentIndicator() {
    IconData icon;
    Color color;
    switch (newsItem.sentiment) {
      case SentimentType.positive:
        icon = Icons.trending_up;
        color = NewsCardColors.trendUp;
        break;
      case SentimentType.negative:
        icon = Icons.trending_down;
        color = NewsCardColors.trendDown;
        break;
      case SentimentType.neutral:
      default:
        icon = Icons.trending_flat;
        color = NewsCardColors.trendNeutral;
        break;
    }
    return Icon(icon, color: color.withOpacity(0.8), size: 16);
  }

  Widget _buildTag(String tag) {
    Color bg;
    Color border;
    Color text;
    Gradient? gradient;
    switch (tag.toUpperCase()) {
      case 'BTC':
        bg = NewsCardColors.tagBTCBg;
        border = NewsCardColors.tagBTCBorder;
        text = NewsCardColors.tagBTCText;
        break;
      case 'ETH':
        bg = NewsCardColors.tagETHBg;
        border = NewsCardColors.tagETHBorder;
        text = NewsCardColors.tagETHText;
        break;
      case 'DEFI':
        bg = NewsCardColors.tagDeFiBg;
        border = NewsCardColors.tagDeFiBorder;
        text = NewsCardColors.tagDeFiText;
        break;
      case 'AI':
        bg = NewsCardColors.tagAIBg;
        border = NewsCardColors.tagAIBorder;
        text = NewsCardColors.tagAIText;
        break;
      case 'STOCKS':
      case 'DOW':
        bg = NewsCardColors.tagStocksBg;
        border = NewsCardColors.tagStocksBorder;
        text = NewsCardColors.tagStocksText;
        break;
      case 'SP500':
        bg = NewsCardColors.tagSP500Bg;
        border = NewsCardColors.tagSP500Border;
        text = NewsCardColors.tagSP500Text;
        break;
      case 'MACRO':
        bg = NewsCardColors.tagMacroBg;
        border = NewsCardColors.tagMacroBorder;
        text = NewsCardColors.tagMacroText;
        break;
      case 'CRYPTO':
        gradient = NewsCardColors.tagCryptoBg;
        border = NewsCardColors.tagCryptoBorder;
        text = NewsCardColors.tagCryptoText;
        break;
      case 'NASDAQ':
        bg = NewsCardColors.tagNasdaqBg;
        border = NewsCardColors.tagNasdaqBorder;
        text = NewsCardColors.tagNasdaqText;
        break;
      case 'CONGRESS':
        bg = NewsCardColors.tagCongressBg;
        border = NewsCardColors.tagCongressBorder;
        text = NewsCardColors.tagCongressText;
        break;
      default:
        bg = NewsCardColors.tagDefaultBg;
        border = NewsCardColors.tagDefaultBorder;
        text = NewsCardColors.tagDefaultText;
        break;
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
      decoration: BoxDecoration(
        color: gradient == null ? bg : null,
        gradient: gradient,
        border: Border.all(color: border, width: 1),
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Text(
        '#$tag',
        style: NewsCardColors.tagStyle.copyWith(color: text),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Возвращает время для отображения - приоритет publishedAt над fetchedAt
  DateTime _getDisplayTime(NewsItem newsItem) {
    // Всегда используем publishedAt для отображения времени публикации
    return newsItem.publishedAt;
  }
}
