import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../providers/trading_simulator_provider.dart';

class CustomModeSettingsScreen extends StatelessWidget {
  const CustomModeSettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<TradingSimulatorProvider>(context);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black,
            Colors.blueGrey.shade900,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Custom Mode Settings',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ).animate().fadeIn(duration: 500.ms),

              const SizedBox(height: 8),

              const Text(
                'Configure your trading experience',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ).animate().fadeIn(duration: 500.ms, delay: 200.ms),

              const SizedBox(height: 32),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSectionTitle('Select Cryptocurrency')
                          .animate()
                          .fadeIn(duration: 500.ms, delay: 300.ms),

                      const SizedBox(height: 16),

                      _buildSymbolSelector(context, provider)
                          .animate()
                          .fadeIn(duration: 500.ms, delay: 400.ms),

                      const SizedBox(height: 24),

                      _buildSectionTitle('Select Timeframe')
                          .animate()
                          .fadeIn(duration: 500.ms, delay: 500.ms),

                      const SizedBox(height: 16),

                      _buildTimeframeSelector(context, provider)
                          .animate()
                          .fadeIn(duration: 500.ms, delay: 600.ms),

                      const SizedBox(height: 24),

                      _buildSectionTitle('Select Leverage')
                          .animate()
                          .fadeIn(duration: 500.ms, delay: 700.ms),

                      const SizedBox(height: 16),

                      _buildLeverageSelector(context, provider)
                          .animate()
                          .fadeIn(duration: 500.ms, delay: 800.ms),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: () => provider.startGame(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 4,
                  ),
                  child: provider.isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                          'Start Trading',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ).animate().fadeIn(duration: 500.ms, delay: 900.ms),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSymbolSelector(BuildContext context, TradingSimulatorProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 60,
          decoration: BoxDecoration(
            color: Colors.grey.shade800,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: DropdownButton<String>(
                    value: provider.selectedSymbol,
                    onChanged: (value) {
                      if (value != null) {
                        provider.selectSymbol(value);
                      }
                    },
                    items: provider.availableSymbols.map((symbol) {
                      return DropdownMenuItem<String>(
                        value: symbol,
                        child: Text(symbol),
                      );
                    }).toList(),
                    isExpanded: true,
                    dropdownColor: Colors.grey.shade800,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                    underline: Container(),
                    icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                  ),
                ),
              ),
              Container(
                width: 1,
                height: 30,
                color: Colors.grey.shade700,
              ),
              IconButton(
                onPressed: () => provider.selectRandomSymbol(),
                icon: const Icon(Icons.shuffle, color: Colors.white),
                tooltip: 'Random Symbol',
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Select a cryptocurrency pair or use the shuffle button for a random selection.',
          style: TextStyle(
            color: Colors.white.withOpacity(0.6),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeframeSelector(BuildContext context, TradingSimulatorProvider provider) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: provider.availableTimeframes.map((timeframe) {
        final isSelected = timeframe == provider.selectedTimeframe;
        return InkWell(
          onTap: () => provider.selectTimeframe(timeframe),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue : Colors.grey.shade800,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? Colors.blue.shade300 : Colors.transparent,
                width: 1,
              ),
            ),
            child: Text(
              timeframe,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLeverageSelector(BuildContext context, TradingSimulatorProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.grey.shade800,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              const Text(
                'Leverage: ',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              Expanded(
                child: Text(
                  '${provider.selectedLeverage}x',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                provider.selectedLeverage > 10 ? 'High Risk' : 'Low Risk',
                style: TextStyle(
                  color: provider.selectedLeverage > 10 ? Colors.red : Colors.green,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Slider(
          value: provider.selectedLeverage.toDouble(),
          min: 1,
          max: 1000,
          divisions: 10,
          label: '${provider.selectedLeverage}x',
          onChanged: (value) => provider.selectLeverage(value.toInt()),
          activeColor: Colors.blue,
          inactiveColor: Colors.grey.shade700,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '1x',
              style: TextStyle(color: Colors.white.withOpacity(0.7)),
            ),
            Text(
              '1000x',
              style: TextStyle(color: Colors.white.withOpacity(0.7)),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'Higher leverage means higher potential profits, but also higher risk of losses.',
          style: TextStyle(
            color: Colors.white.withOpacity(0.6),
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
