import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/test_news_provider.dart';
import '../models/news_item.dart';
import '../models/sentiment_types.dart';
import '../widgets/news_card.dart';
import '../widgets/filter_chip.dart';

class TestNewsScreen extends StatefulWidget {
  const TestNewsScreen({Key? key}) : super(key: key);

  @override
  State<TestNewsScreen> createState() => _TestNewsScreenState();
}

class _TestNewsScreenState extends State<TestNewsScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Загружаем тестовые новости при инициализации
    Future.microtask(() => 
      context.read<TestNewsProvider>().loadTestNews()
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Тестовые новости'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
        ],
      ),
      body: Consumer<TestNewsProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Ошибка: ${provider.error}',
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.loadTestNews(),
                    child: const Text('Повторить'),
                  ),
                ],
              ),
            );
          }

          final news = provider.filteredNews;
          if (news.isEmpty) {
            return const Center(
              child: Text('Новости не найдены'),
            );
          }

          return Column(
            children: [
              _buildFilters(provider),
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(8),
                  itemCount: news.length,
                  itemBuilder: (context, index) {
                    return NewsCard(
                      news: news[index],
                      onTap: () => _showNewsDetails(context, news[index]),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showGenerateDialog(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildFilters(TestNewsProvider provider) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Row(
        children: [
          // Фильтр по категориям
          Wrap(
            spacing: 8,
            children: [
              CustomFilterChip(
                label: 'Все',
                selected: provider.selectedCategories.isEmpty,
                onSelected: (selected) {
                  if (selected) provider.setCategoryFilter([]);
                },
              ),
              CustomFilterChip(
                label: 'Крипто',
                selected: provider.selectedCategories.contains('crypto'),
                onSelected: (selected) {
                  if (selected) {
                    provider.setCategoryFilter(['crypto']);
                  } else {
                    provider.setCategoryFilter([]);
                  }
                },
              ),
              CustomFilterChip(
                label: 'Акции',
                selected: provider.selectedCategories.contains('stocks'),
                onSelected: (selected) {
                  if (selected) {
                    provider.setCategoryFilter(['stocks']);
                  } else {
                    provider.setCategoryFilter([]);
                  }
                },
              ),
            ],
          ),
          const SizedBox(width: 16),
          // Фильтр по сентименту
          Wrap(
            spacing: 8,
            children: [
              CustomFilterChip(
                label: 'Все',
                selected: provider.selectedSentiments.isEmpty,
                onSelected: (selected) {
                  if (selected) provider.setSentimentFilter([]);
                },
              ),
              CustomFilterChip(
                label: 'Позитивные',
                selected: provider.selectedSentiments.contains(SentimentType.positive),
                onSelected: (selected) {
                  if (selected) {
                    provider.setSentimentFilter([SentimentType.positive]);
                  } else {
                    provider.setSentimentFilter([]);
                  }
                },
              ),
              CustomFilterChip(
                label: 'Негативные',
                selected: provider.selectedSentiments.contains(SentimentType.negative),
                onSelected: (selected) {
                  if (selected) {
                    provider.setSentimentFilter([SentimentType.negative]);
                  } else {
                    provider.setSentimentFilter([]);
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Поиск новостей'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Введите поисковый запрос',
          ),
          onSubmitted: (value) {
            if (value.isNotEmpty) {
              context.read<TestNewsProvider>().searchNews(value);
            }
            Navigator.pop(context);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Отмена'),
          ),
          TextButton(
            onPressed: () {
              if (_searchController.text.isNotEmpty) {
                context.read<TestNewsProvider>().searchNews(_searchController.text);
              }
              Navigator.pop(context);
            },
            child: const Text('Поиск'),
          ),
        ],
      ),
    );
  }

  void _showNewsDetails(BuildContext context, NewsItem news) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (news.imageUrl.isNotEmpty)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      news.imageUrl,
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                const SizedBox(height: 16),
                Text(
                  news.title,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Источник: ${news.source}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Опубликовано: ${_formatDate(news.publishedAt)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(height: 16),
                Text(
                  news.description,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                if (news.content?.isNotEmpty ?? false) ...[
                  const SizedBox(height: 16),
                  Text(
                    news.content ?? '',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
                const SizedBox(height: 16),
                Wrap(
                  spacing: 8,
                  children: news.tags.map((tag) => Chip(
                    label: Text(tag),
                    backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                  )).toList(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showGenerateDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Сгенерировать тестовые новости'),
        content: const Text('Сгенерировать новые тестовые новости?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Отмена'),
          ),
          TextButton(
            onPressed: () {
              context.read<TestNewsProvider>().generateTestNews();
              Navigator.pop(context);
            },
            child: const Text('Сгенерировать'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year} ${date.hour}:${date.minute}';
  }
} 