import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class CryptoCompareService {
  static const String _baseUrl = 'https://min-api.cryptocompare.com';
  // Используем бесплатный API ключ для CryptoCompare
  static const String _apiKey = '****************************************************************';

  // Кэш для хранения URL логотипов
  final Map<String, String> _logoUrlCache = {};

  // Ключ для SharedPreferences
  static const String _cacheKey = 'crypto_logos_cache';

  // Конструктор, который загружает кэш из SharedPreferences
  CryptoCompareService() {
    _loadCache();
  }

  // Загрузка кэша из SharedPreferences
  Future<void> _loadCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_cacheKey);

      if (cachedData != null) {
        final Map<String, dynamic> decoded = jsonDecode(cachedData);
        decoded.forEach((key, value) {
          _logoUrlCache[key] = value.toString();
        });
        print('Loaded ${_logoUrlCache.length} logo URLs from cache');
      }
    } catch (e) {
      print('Error loading logo cache: $e');
    }
  }

  // Сохранение кэша в SharedPreferences
  Future<void> _saveCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_cacheKey, jsonEncode(_logoUrlCache));
      print('Saved ${_logoUrlCache.length} logo URLs to cache');
    } catch (e) {
      print('Error saving logo cache: $e');
    }
  }

  // Получение URL логотипа для одной монеты (с использованием кэша)
  Future<String> getLogoUrl(String symbol) async {
    // Проверяем кэш
    if (_logoUrlCache.containsKey(symbol)) {
      return _logoUrlCache[symbol]!;
    }

    // Если в кэше нет, запрашиваем данные
    try {
      final logoUrl = await _fetchLogoUrl(symbol);
      // Сохраняем в кэш
      _logoUrlCache[symbol] = logoUrl;
      // Обновляем кэш в SharedPreferences
      _saveCache();
      return logoUrl;
    } catch (e) {
      print('Error fetching logo for $symbol: $e');
      // Возвращаем заглушку в случае ошибки
      return 'https://via.placeholder.com/50?text=$symbol';
    }
  }

  // Получение URL логотипов для нескольких монет за один запрос
  Future<Map<String, String>> getLogoUrls(List<String> symbols) async {
    // Фильтруем символы, которые уже есть в кэше
    final List<String> symbolsToFetch = symbols
        .where((symbol) => !_logoUrlCache.containsKey(symbol))
        .toList();

    // Если все символы уже в кэше, возвращаем их
    if (symbolsToFetch.isEmpty) {
      return Map.fromEntries(
        symbols.map((symbol) => MapEntry(symbol, _logoUrlCache[symbol]!)),
      );
    }

    // Иначе запрашиваем данные для недостающих символов
    try {
      final newLogoUrls = await _fetchLogoUrls(symbolsToFetch);

      // Добавляем новые URL в кэш
      _logoUrlCache.addAll(newLogoUrls);

      // Обновляем кэш в SharedPreferences
      _saveCache();

      // Возвращаем все запрошенные URL (из кэша + новые)
      return Map.fromEntries(
        symbols.map((symbol) => MapEntry(
          symbol,
          _logoUrlCache.containsKey(symbol)
              ? _logoUrlCache[symbol]!
              : 'https://via.placeholder.com/50?text=$symbol'
        )),
      );
    } catch (e) {
      print('Error fetching logos: $e');
      // Возвращаем заглушки для символов, которых нет в кэше
      return Map.fromEntries(
        symbols.map((symbol) => MapEntry(
          symbol,
          _logoUrlCache.containsKey(symbol)
              ? _logoUrlCache[symbol]!
              : 'https://via.placeholder.com/50?text=$symbol'
        )),
      );
    }
  }

  // Приватный метод для запроса URL логотипа одной монеты
  Future<String> _fetchLogoUrl(String symbol) async {
    return _fetchLogoUrls([symbol]).then((map) => map[symbol]!);
  }

  // Приватный метод для запроса URL логотипов нескольких монет
  Future<Map<String, String>> _fetchLogoUrls(List<String> symbols) async {
    if (symbols.isEmpty) return {};

    final Map<String, String> result = {};

    try {
      // Формируем строку с символами для запроса
      final symbolsStr = symbols.join(',');

      print('Fetching logo URLs for symbols: $symbolsStr');

      // Формируем URL для запроса
      final url = Uri.parse(
        '$_baseUrl/data/coin/generalinfo?fsyms=$symbolsStr&tsym=USD${_apiKey.isNotEmpty ? '&api_key=$_apiKey' : ''}'
      );

      print('Request URL: $url');

      // Выполняем запрос
      final response = await http.get(
        url,
        headers: {'Accept-Encoding': 'gzip, deflate'},
      );

      // Проверяем статус ответа
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('Response status: ${data['Response']}');

        // Проверяем, что ответ содержит данные
        if (data.containsKey('Data')) {
          // Извлекаем URL логотипов
          final List<dynamic> dataList = data['Data'];
          print('Found ${dataList.length} coins in response');

          for (var item in dataList) {
            final coinInfo = item['CoinInfo'];
            final symbol = coinInfo['Name'];

            // Проверяем, есть ли URL изображения
            if (coinInfo.containsKey('ImageUrl') && coinInfo['ImageUrl'] != null && coinInfo['ImageUrl'].toString().isNotEmpty) {
              final imageUrl = 'https://www.cryptocompare.com${coinInfo['ImageUrl']}';
              print('Found logo for $symbol: $imageUrl');
              result[symbol] = imageUrl;
            } else {
              print('No logo found for $symbol, using placeholder');
              result[symbol] = 'https://via.placeholder.com/50?text=$symbol';
            }
          }
        } else {
          print('No Data field in response: ${response.body.substring(0, 200)}...');
          // В случае отсутствия данных создаем заглушки
          for (var symbol in symbols) {
            result[symbol] = 'https://via.placeholder.com/50?text=$symbol';
          }
        }
      } else {
        print('Failed to load coin info: ${response.statusCode}, body: ${response.body.substring(0, 200)}...');
        throw Exception('Failed to load coin info: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in _fetchLogoUrls: $e');
      // В случае ошибки создаем заглушки для всех символов
      for (var symbol in symbols) {
        result[symbol] = 'https://via.placeholder.com/50?text=$symbol';
      }
    }

    return result;
  }
}
