import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import '../models/anti_fomo_simulator_models.dart';
import '../models/trading_simulator_models.dart';
import '../widgets/gradient_background.dart';
import '../widgets/animated_button.dart';
import '../widgets/simulator_candlestick_chart.dart';
import '../widgets/trading_controls.dart';
import '../widgets/trade_result.dart';
import '../widgets/social_hype_meter.dart';
import '../widgets/fake_chat.dart';
import '../widgets/news_ticker.dart';
import '../services/anti_fomo_simulator_service.dart';
import 'fomo_analysis_results_screen.dart';
import '../models/fomo_psychological_model.dart' as psych;

import '../screens/courses_screen.dart';

class AntiFOMOSimulatorScreen extends StatelessWidget {
  const AntiFOMOSimulatorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Anti-FOMO Simulator'),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: GradientBackground(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 20),

              // Logo or icon
              const Icon(
                Icons.psychology,
                size: 80,
                color: Colors.white,
              ),

              const SizedBox(height: 20),

              // Title
              const Text(
                'Anti-FOMO Simulator',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 10),

              // Description
              const Text(
                'Train your emotional control and decision-making under pressure',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),

              const SizedBox(height: 40),

              // Start button
              Center(
                child: SizedBox(
                  width: 260,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.play_arrow, color: Colors.white, size: 18),
                    label: const Padding(
                      padding: EdgeInsets.symmetric(vertical: 6),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Start Simulation',
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(height: 1),
                          Text(
                            'Choose your trader role and difficulty',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange.shade700,
                      foregroundColor: Colors.white,
                      elevation: 6,
                      shadowColor: Colors.orange.shade200,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: EdgeInsets.zero,
                      minimumSize: const Size(0, 36),
                    ).copyWith(
                      overlayColor: MaterialStateProperty.all(Colors.orange.shade900.withOpacity(0.15)),
                    ),
                    onPressed: () => _navigateToRoleSelection(context),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // How to play button
              Center(
                child: SizedBox(
                  width: 260,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.help_outline, color: Colors.white, size: 18),
                    label: const Padding(
                      padding: EdgeInsets.symmetric(vertical: 6),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'How to Play',
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(height: 1),
                          Text(
                            'Learn the rules and strategies',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[800],
                      foregroundColor: Colors.white,
                      elevation: 3,
                      shadowColor: Colors.black45,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: EdgeInsets.zero,
                      minimumSize: const Size(0, 36),
                    ).copyWith(
                      overlayColor: MaterialStateProperty.all(Colors.grey.shade900.withOpacity(0.12)),
                    ),
                    onPressed: () => _showHowToPlay(context),
                  ),
                ),
              ),

              const SizedBox(height: 12),
              Center(
                child: SizedBox(
                  width: 260,
                  child: TextButton(
                    onPressed: () => Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(builder: (context) => const CoursesScreen(initialTabIndex: 2)),
                      (route) => false,
                    ),
                    child: const Text(
                      'Back to Menu',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),

              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToRoleSelection(BuildContext context) {
    // Navigate to role selection screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const RoleSelectionScreen(),
      ),
    );
  }

  void _showHowToPlay(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'How to Play',
          style: TextStyle(color: Colors.white),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHowToPlaySection(
                'Goal',
                'Make profitable trading decisions while resisting social pressure and FOMO (Fear Of Missing Out).',
              ),
              _buildHowToPlaySection(
                'Gameplay',
                '1. Choose your trader role and difficulty level\n'
                '2. Watch the chart and social media messages\n'
                '3. Make decisions: BUY, SELL, or HOLD\n'
                '4. See the results and learn from your decisions',
              ),
              _buildHowToPlaySection(
                'Social Hype Meter',
                'Shows the current level of market hype. Higher hype means more potential profit but also more risk of making emotional decisions.',
              ),
              _buildHowToPlaySection(
                'Trader Roles',
                '• Novice: Small capital, high leverage, high risk\n'
                '• Whale: Large capital, low leverage, market impact\n'
                '• Darkpool: Medium capital, access to insider info (which may be misleading)',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }

  Widget _buildHowToPlaySection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }
}

/// Screen for selecting trader role
class RoleSelectionScreen extends StatelessWidget {
  const RoleSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Trader Role'),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: GradientBackground(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Choose Your Trader Role',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 8),

              const Text(
                'Each role has different starting capital, leverage, and special abilities',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),

              const SizedBox(height: 30),

              // Role cards
              Expanded(
                child: ListView(
                  children: TraderRole.values.map((role) =>
                    _buildRoleCard(context, role)
                  ).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleCard(BuildContext context, TraderRole role) {
    return AnimatedButton(
      onTap: () => _selectRole(context, role),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: role.color.withOpacity(0.2),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: role.color,
            width: 2,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  role.icon,
                  color: role.color,
                  size: 22,
                ),
                const SizedBox(width: 8),
                Text(
                  role.displayName,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: role.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6),
            Text(
              role.description,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 6),
            Row(
              children: [
                _buildRoleStat('Starting Capital', role.initialBalance.toStringAsFixed(0)),
                const SizedBox(width: 8),
                _buildRoleStat('Leverage', '${role.leverageMultiplier.toStringAsFixed(0)}x'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleStat(String label, String value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 9,
              color: Colors.white54,
            ),
          ),
          const SizedBox(height: 1),
          Text(
            value.startsWith('\$') ? value : '\$$value',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _selectRole(BuildContext context, TraderRole role) {
    // Navigate to difficulty selection
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DifficultySelectionScreen(selectedRole: role),
      ),
    );
  }
}

/// Screen for selecting difficulty level
class DifficultySelectionScreen extends StatelessWidget {
  final TraderRole selectedRole;

  const DifficultySelectionScreen({
    super.key,
    required this.selectedRole,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Difficulty'),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: GradientBackground(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Selected role indicator
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: selectedRole.color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: selectedRole.color,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      selectedRole.icon,
                      color: selectedRole.color,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Selected Role: ${selectedRole.displayName}',
                      style: TextStyle(
                        color: selectedRole.color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              const Text(
                'Choose Difficulty Level',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 8),

              const Text(
                'Higher difficulty means less time to decide and higher stakes',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),

              const SizedBox(height: 30),

              // Difficulty cards
              Expanded(
                child: ListView(
                  children: DifficultyLevel.values.map((difficulty) =>
                    _buildDifficultyCard(context, difficulty)
                  ).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDifficultyCard(BuildContext context, DifficultyLevel difficulty) {
    final isPro = difficulty == DifficultyLevel.pro;
    return AnimatedButton(
      onTap: () => _selectDifficulty(context, difficulty),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: difficulty.color.withOpacity(0.2),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: isPro ? Colors.grey[500]! : difficulty.color,
            width: isPro ? 3 : 2,
          ),
          boxShadow: isPro
              ? [
                  BoxShadow(
                    color: Colors.grey[500]!.withOpacity(0.2),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ]
              : [],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              difficulty.displayName,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: isPro ? Colors.white : difficulty.color,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              difficulty.description,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 6),
            Row(
              children: [
                _buildDifficultyStat('Decision Time', '${difficulty.timerDuration} sec'),
                const SizedBox(width: 8),
                _buildDifficultyStat('Risk Multiplier', '${difficulty.riskMultiplier}x'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDifficultyStat(String label, String value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 9,
              color: Colors.white54,
            ),
          ),
          const SizedBox(height: 1),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _selectDifficulty(BuildContext context, DifficultyLevel difficulty) {
    // Navigate to game screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AntiFomoGameScreen(
          selectedRole: selectedRole,
          selectedDifficulty: difficulty,
        ),
      ),
    );
  }
}

/// The main game screen for the Anti FOMO Simulator
class AntiFomoGameScreen extends StatefulWidget {
  final TraderRole selectedRole;
  final DifficultyLevel selectedDifficulty;

  const AntiFomoGameScreen({
    super.key,
    required this.selectedRole,
    required this.selectedDifficulty,
  });

  @override
  State<AntiFomoGameScreen> createState() => _AntiFomoGameScreenState();
}

class _AntiFomoGameScreenState extends State<AntiFomoGameScreen> with TickerProviderStateMixin {
  final AntiFomoSimulatorService _service = AntiFomoSimulatorService();
  final psych.FomoPsychologicalModel _psychologicalModel = psych.FomoPsychologicalModel();

  // Game state
  late String _currentSymbol;
  late TimeFrame _currentTimeFrame;
  late double _balance;
  late List<CandleData> _candles = [];
  late SocialHype _socialHype;
  late List<ChatMessage> _chatMessages = [];
  late List<TickerNewsItem> _newsItems = [];

  // Track previous trading pairs and timeframes to avoid repetition
  final List<String> _recentlyUsedSymbols = [];
  final List<TimeFrame> _recentlyUsedTimeFrames = [];
  final int _maxHistoryTrack = 3; // Keep track of last 3 pairs/timeframes

  // Timer state
  late Timer _gameTimer;
  late Timer _chatTimer;
  late Timer _newsTimer;
  int _timeLeft = 0;
  int _decisionTimeUsed = 0;

  // UI state
  bool _isLoading = true;
  bool _showingResult = false;
  bool _gameOver = false;
  FomoTradeAction? _selectedAction;
  FomoTradeAction? _optimalAction;
  double _lastProfitAmount = 0;
  double _lastProfitPercentage = 0;
  int _roundsPlayed = 0;
  int _maxRounds = 10; // Limit the number of rounds in a session

  @override
  void initState() {
    super.initState();
    _initializeGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _chatTimer.cancel();
    _newsTimer.cancel();
    super.dispose();
  }

  void _initializeGame() async {
    setState(() {
      _isLoading = true;
      _showingResult = false;
      _gameOver = false;
      _selectedAction = null;
      _optimalAction = null;
      _lastProfitAmount = 0;
      _lastProfitPercentage = 0;
      _roundsPlayed = 0;

      // Clear history of used pairs/timeframes when starting a new game
      _recentlyUsedSymbols.clear();
      _recentlyUsedTimeFrames.clear();

      // Initialize game state with random non-repeating trading pair and timeframe
      _currentSymbol = _getRandomNonRepeatingSymbol();
      _currentTimeFrame = _getRandomNonRepeatingTimeFrame();
      _balance = widget.selectedRole.initialBalance;
      _socialHype = SocialHype(level: 50); // Start with medium hype
      _chatMessages = [];
      _newsItems = [];

      // Initialize timer
      _timeLeft = widget.selectedDifficulty.timerDuration;
      _decisionTimeUsed = 0;
    });

    // Reset psychological model
    _psychologicalModel.reset();

    // Load candles
    await _loadCandles();

    // Start timers
    _startGameTimer();
    _startChatTimer();
    _startNewsTimer();
  }

  // Get a random symbol that hasn't been used recently
  String _getRandomNonRepeatingSymbol() {
    String newSymbol;
    int attempts = 0;
    final maxAttempts = 10; // Avoid infinite loop

    do {
      newSymbol = _service.getRandomTradingPair();
      attempts++;
    } while (_recentlyUsedSymbols.contains(newSymbol) && attempts < maxAttempts);

    // Track this symbol
    _recentlyUsedSymbols.add(newSymbol);

    // Keep only recent history
    if (_recentlyUsedSymbols.length > _maxHistoryTrack) {
      _recentlyUsedSymbols.removeAt(0);
    }

    return newSymbol;
  }

  // Get a random timeframe that hasn't been used recently
  TimeFrame _getRandomNonRepeatingTimeFrame() {
    TimeFrame newTimeFrame;
    int attempts = 0;
    final maxAttempts = 5; // Avoid infinite loop

    do {
      newTimeFrame = _service.getRandomTimeFrame();
      attempts++;
    } while (_recentlyUsedTimeFrames.contains(newTimeFrame) && attempts < maxAttempts);

    // Track this timeframe
    _recentlyUsedTimeFrames.add(newTimeFrame);

    // Keep only recent history
    if (_recentlyUsedTimeFrames.length > _maxHistoryTrack) {
      _recentlyUsedTimeFrames.removeAt(0);
    }

    return newTimeFrame;
  }

  Future<void> _loadCandles() async {
    try {
      final candles = await _service.fetchCandles(
        symbol: _currentSymbol,
        timeFrame: _currentTimeFrame,
      );

      if (mounted) {
        setState(() {
          _candles = candles;
          _isLoading = false;

          // Visible candles (exclude future candles)
          final visibleCandles = candles.sublist(0, candles.length - 20);
          final futureCandles = candles.sublist(candles.length - 20);

          // Get entry point (last visible candle)
          final entryPoint = visibleCandles.last.close;

          // Get 9 candles from future for results and use last one for optimal action determination
          final candles9Future = futureCandles.sublist(0, 9);
          final lastVisibleFutureCandle = candles9Future.last;

          // Calculate optimal action based on last visible future candle relative to entry point
          if (lastVisibleFutureCandle.close > entryPoint) {
            // If the 9th future candle is higher than entry point, BUY would have been optimal
            _optimalAction = FomoTradeAction.buy;
          } else {
            // If the 9th future candle is lower than entry point, SELL would have been optimal
            _optimalAction = FomoTradeAction.sell;
          }

          // Generate initial chat messages - 10x more (50 instead of 5)
          _chatMessages = _service.generateChatMessages(_socialHype.level, 50);

          // Generate initial news items - increased from 3 to 10
          _newsItems = [
            _service.generateNewsItem(),
            _service.generateNewsItem(),
            _service.generateNewsItem(),
            _service.generateNewsItem(),
            _service.generateNewsItem(),
            _service.generateNewsItem(),
            _service.generateNewsItem(),
            _service.generateNewsItem(),
            _service.generateNewsItem(),
            _service.generateNewsItem(),
          ];
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_showingResult || _gameOver) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_timeLeft > 0) {
          _timeLeft--;
          _decisionTimeUsed++;
        } else {
          // Time's up - execute HOLD action automatically
          _handleTradeAction(FomoTradeAction.hold);
        }
      });
    });
  }

  void _startChatTimer() {
    // Generate new chat messages based on hype level
    // Higher hype = more frequent messages
    final messageInterval = _socialHype.level > 80 ? 1 : (_socialHype.level > 50 ? 2 : 3);
    // ^ Increased frequency (was 2/4/6 seconds)

    _chatTimer = Timer.periodic(Duration(seconds: messageInterval), (timer) {
      if (_showingResult || _gameOver) {
        return;
      }

      setState(() {
        // Add multiple new messages at once (10x more)
        final newMessages = _service.generateChatMessages(_socialHype.level, 10);
        _chatMessages.insertAll(0, newMessages);

        // Keep only the last 50 messages (was 5)
        if (_chatMessages.length > 50) {
          _chatMessages.removeRange(50, _chatMessages.length);
        }
      });
    });
  }

  void _startNewsTimer() {
    // Generate new news items periodically
    _newsTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      if (_showingResult || _gameOver) {
        return;
      }

      setState(() {
        // Add a new news item
        final isSpecial = _socialHype.level > 70 && math.Random().nextDouble() < 0.3;
        final newItem = _service.generateNewsItem(forceSpecial: isSpecial);
        _newsItems.add(newItem);

        // Keep only the last 10 news items
        if (_newsItems.length > 10) {
          _newsItems.removeAt(0);
        }

        // Special news items affect social hype more
        if (isSpecial) {
          _socialHype.update(_socialHype.level + 20);
        } else {
          _socialHype.update(_socialHype.level);
        }
      });
    });
  }

  void _handleTradeAction(FomoTradeAction action) {
    if (_showingResult || _candles.isEmpty) return;

    // Get visible and future candles
    final visibleCandles = _candles.sublist(0, _candles.length - 20);
    final futureCandles = _candles.sublist(_candles.length - 20);
    final entryPoint = visibleCandles.last.close;

    // Show only first 9 candles from future for results
    final candles9Future = futureCandles.sublist(0, 9);
    final lastVisibleFutureCandle = candles9Future.last;

    // Determine optimal action based on the last visible future candle vs entry point
    if (lastVisibleFutureCandle.close > entryPoint) {
      _optimalAction = FomoTradeAction.buy;
    } else {
      _optimalAction = FomoTradeAction.sell;
    }

    // Calculate trade outcome
    final profitPercentage = _service.calculateTradeOutcome(
      userAction: action,
      optimalAction: _optimalAction!,
      socialHypeLevel: _socialHype.level,
      role: widget.selectedRole,
      difficulty: widget.selectedDifficulty,
    );

    // Calculate profit amount based on role's initial balance
    final profitAmount = widget.selectedRole.initialBalance * (profitPercentage / 100);

    // Record the action for psychological analysis
    final actionRecord = psych.UserActionRecord(
      timestamp: DateTime.now(),
      action: action,
      optimalAction: _optimalAction!,
      socialHypeLevel: _socialHype.level,
      profitPercentage: profitPercentage,
      profitAmount: profitAmount,
      decisionTimeSeconds: _decisionTimeUsed,
      availableTimeSeconds: widget.selectedDifficulty.timerDuration,
    );
    _psychologicalModel.recordAction(actionRecord);

    // Update game state
    setState(() {
      _showingResult = true;
      _selectedAction = action;
      _lastProfitAmount = profitAmount;
      _lastProfitPercentage = profitPercentage;
      _balance += profitAmount;
      _roundsPlayed++;
      if (_balance <= 0 || _roundsPlayed >= _maxRounds) {
        _gameOver = true;
      }
    });
    _gameTimer.cancel();
  }

  // Start a new round with a new random trading pair and timeframe
  void _nextRound() {
    setState(() {
      _isLoading = true;
      _showingResult = false;
      _selectedAction = null;
      _optimalAction = null;
      _lastProfitAmount = 0;
      _lastProfitPercentage = 0;
      _decisionTimeUsed = 0;

      // Get new random non-repeating pair and timeframe
      _currentSymbol = _getRandomNonRepeatingSymbol();
      _currentTimeFrame = _getRandomNonRepeatingTimeFrame();

      // Reset timer
      _timeLeft = widget.selectedDifficulty.timerDuration;
    });

    // Load new candles
    _loadCandles();

    // Restart game timer
    _startGameTimer();
  }

  void _restartGame() {
    _initializeGame();
  }

  void _showAnalysisResults() {
    final analysisResult = _psychologicalModel.getAnalysisResult();

    Navigator.push(
      context,
      PageRouteBuilder(
        transitionDuration: Duration(milliseconds: 1800),
        pageBuilder: (context, animation, secondaryAnimation) => FomoAnalysisResultsScreen(
          result: analysisResult,
          role: widget.selectedRole,
          finalBalance: _balance,
          initialBalance: widget.selectedRole.initialBalance,
          chartWidget: SimulatorCandlestickChart.forResults(
            candles: _candles.sublist(0, _candles.length - 20 + 9),
            visibleCandleCount: 100,
            showEntryMarkers: true,
            entryPrice: _selectedAction != null ? _getLastVisibleCandle().close : null,
            highlightedCandle: _getLastVisibleCandle(),
            height: 420,
          ),
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.selectedRole.displayName} - ${widget.selectedDifficulty.displayName}'),
        backgroundColor: Colors.black,
        elevation: 0,
        actions: [
          // Balance display
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Center(
              child: Text(
                _service.formatCurrency(_balance),
                style: TextStyle(
                  color: _balance >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ),
      body: GradientBackground(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildGameContent(),
      ),
    );
  }

  Widget _buildGameContent() {
    return Column(
      children: [
        // News ticker
        NewsTickerWidget(newsItems: _newsItems),

        // Social hype meter
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Social Hype',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 4),
              SocialHypeMeter(hype: _socialHype),
            ],
          ),
        ),

        // Main content area - Chart and Chat side by side
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Chart on the left - always large and fully visible
              Expanded(
                flex: 3,
                child: Column(
                  children: [
                    // Chart
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: _showingResult
                          ? SimulatorCandlestickChart.forResults(
                              candles: _candles.sublist(0, _candles.length - 20 + 9),
                              visibleCandleCount: 100,
                              showEntryMarkers: true,
                              entryPrice: _selectedAction != null ? _getLastVisibleCandle().close : null,
                              highlightedCandle: _getLastVisibleCandle(),
                            )
                          : SimulatorCandlestickChart(
                              candles: _candles.sublist(0, _candles.length - 20),
                              showFutureCandles: false,
                              visibleCandleCount: 100,
                              showEntryCrosshair: false,
                              zoomFactor: 0.5,
                              verticalZoomFactor: 1.0,
                              showEntryMarkers: false,
                            ),
                      ),
                    ),
                    // Trading info and controls (unchanged)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '$_currentSymbol/USDT',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            _currentTimeFrame.displayName,
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (_gameOver)
                      _buildGameOverPanel()
                    else if (_showingResult && _selectedAction != null)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
                        child: SizedBox(
                          height: 140,
                          child: FomoTradeResultWidget(
                            action: _selectedAction!.label,
                            optimalAction: _optimalAction!.label,
                            profitAmount: _lastProfitAmount,
                            profitPercentage: _lastProfitPercentage,
                            onNextRound: _nextRound,
                            isSpecialScenario: _lastProfitAmount < 0 && _selectedAction == _optimalAction &&
                                             widget.selectedRole == TraderRole.darkpool && _socialHype.level > 70,
                          ),
                        ),
                      )
                    else
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: FomoTradingControls(
                          onActionSelected: _handleTradeAction,
                          isEnabled: !_showingResult,
                          timeLeft: _timeLeft,
                          totalTime: widget.selectedDifficulty.timerDuration,
                        ),
                      ),
                  ],
                ),
              ),
              // Chat on the right - fixed width, always fully visible
              Container(
                width: 360,
                margin: const EdgeInsets.symmetric(vertical: 0.0, horizontal: 8.0),
                child: FakeChat(
                  messages: _chatMessages,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGameOverPanel() {
    final reason = _balance <= 0 ? 'OUT OF FUNDS' : 'SESSION COMPLETE';
    final isProfit = _balance > widget.selectedRole.initialBalance;
    final profitAmount = _balance - widget.selectedRole.initialBalance;
    final profitPercentage = (profitAmount / widget.selectedRole.initialBalance) * 100;

    return Container(
      margin: const EdgeInsets.all(8.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: _balance <= 0 ? Colors.red.withOpacity(0.2) : Colors.blue.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _balance <= 0 ? Colors.red : Colors.blue,
          width: 2,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _balance <= 0 ? Icons.warning_amber_rounded : Icons.check_circle_outline,
            color: _balance <= 0 ? Colors.red : Colors.blue,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            reason,
            style: TextStyle(
              color: _balance <= 0 ? Colors.red : Colors.blue,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Final balance: ',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              Text(
                _service.formatCurrency(_balance),
                style: TextStyle(
                  color: isProfit ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Profit/Loss: ',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              Text(
                '${isProfit ? '+' : ''}${_service.formatCurrency(profitAmount)} (${isProfit ? '+' : ''}${profitPercentage.toStringAsFixed(2)}%)',
                style: TextStyle(
                  color: isProfit ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: _showAnalysisResults,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                child: const Text('View Analysis'),
              ),
              ElevatedButton(
                onPressed: _restartGame,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                child: const Text('Play Again'),
              ),
              TextButton(
                onPressed: () => Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(builder: (context) => const AntiFOMOSimulatorScreen()),
                  (route) => false,
                ),
                child: const Text('Exit'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  CandleData _getLastVisibleCandle() {
    if (_candles.isEmpty) {
      throw Exception("Candles list is empty");
    }

    // Get the last visible candle (the one before the hidden future candles)
    int lastVisibleIndex = _candles.length - 21; // 20 hidden future candles

    // Make sure the index is valid
    if (lastVisibleIndex < 0) {
      lastVisibleIndex = 0;
    }

    return _candles[lastVisibleIndex];
  }
}

