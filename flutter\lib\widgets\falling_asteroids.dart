import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Класс для представления астероида
class Asteroid {
  /// Текущая позиция астероида
  Offset position;
  
  /// Скорость движения астероида
  double speed;
  
  /// Угол движения астероида (в радианах)
  double angle;
  
  /// Размер астероида
  double size;
  
  /// Яркость астероида (от 0.0 до 1.0)
  double brightness;
  
  /// Вращение астероида (в радианах)
  double rotation;
  
  /// Скорость вращения астероида
  double rotationSpeed;
  
  /// Цвет астероида
  Color color;
  
  /// Форма астероида (0 - круг, 1 - многоугольник)
  int shape;
  
  /// Количество вершин многоугольника (если shape == 1)
  int vertices;
  
  Asteroid({
    required this.position,
    required this.speed,
    required this.angle,
    required this.size,
    this.brightness = 1.0,
    this.rotation = 0.0,
    this.rotationSpeed = 0.0,
    this.color = Colors.white,
    this.shape = 0,
    this.vertices = 5,
  });
  
  /// Обновление позиции астероида
  void update(Size screenSize) {
    // Обновляем позицию на основе скорости и угла
    position = Offset(
      position.dx + math.cos(angle) * speed,
      position.dy + math.sin(angle) * speed,
    );
    
    // Обновляем вращение
    rotation += rotationSpeed;
    
    // Если астероид вышел за пределы экрана, возвращаем его в случайную позицию сверху
    if (position.dy > screenSize.height + size || 
        position.dx < -size || 
        position.dx > screenSize.width + size) {
      resetPosition(screenSize);
    }
  }
  
  /// Сброс позиции астероида на верхнюю часть экрана
  void resetPosition(Size screenSize) {
    final random = math.Random();
    position = Offset(
      random.nextDouble() * screenSize.width,
      -size - random.nextDouble() * 100, // Немного выше экрана
    );
    
    // Обновляем угол для движения вниз с небольшим отклонением
    angle = math.pi / 2 + (random.nextDouble() - 0.5) * 0.5;
    
    // Случайная скорость
    speed = 1.0 + random.nextDouble() * 3.0;
  }
}

/// Виджет для отображения падающих астероидов
class FallingAsteroids extends StatefulWidget {
  /// Количество астероидов
  final int count;
  
  /// Минимальный размер астероида
  final double minSize;
  
  /// Максимальный размер астероида
  final double maxSize;
  
  /// Минимальная скорость астероида
  final double minSpeed;
  
  /// Максимальная скорость астероида
  final double maxSpeed;
  
  const FallingAsteroids({
    super.key,
    this.count = 10,
    this.minSize = 2.0,
    this.maxSize = 6.0,
    this.minSpeed = 1.0,
    this.maxSpeed = 3.0,
  });

  @override
  State<FallingAsteroids> createState() => _FallingAsteroidsState();
}

class _FallingAsteroidsState extends State<FallingAsteroids> with SingleTickerProviderStateMixin {
  /// Список астероидов
  late List<Asteroid> _asteroids;
  
  /// Контроллер анимации
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    
    // Инициализируем контроллер анимации
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 16), // ~60 FPS
    )..repeat();
    
    // Инициализируем астероиды
    _initAsteroids();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  /// Инициализация астероидов
  void _initAsteroids() {
    final random = math.Random();
    _asteroids = List.generate(widget.count, (_) {
      // Случайный размер
      final size = widget.minSize + random.nextDouble() * (widget.maxSize - widget.minSize);
      
      // Случайная скорость
      final speed = widget.minSpeed + random.nextDouble() * (widget.maxSpeed - widget.minSpeed);
      
      // Случайная форма (0 - круг, 1 - многоугольник)
      final shape = random.nextInt(2);
      
      // Случайное количество вершин (для многоугольника)
      final vertices = 4 + random.nextInt(4); // от 4 до 7 вершин
      
      // Случайный цвет (оттенки серого)
      final brightness = 0.6 + random.nextDouble() * 0.4; // от 0.6 до 1.0
      final color = Color.fromRGBO(
        (255 * brightness).round(),
        (255 * brightness).round(),
        (255 * brightness).round(),
        1.0,
      );
      
      return Asteroid(
        position: Offset(
          random.nextDouble() * 1000, // Начальная позиция X
          random.nextDouble() * -500, // Начальная позиция Y (выше экрана)
        ),
        speed: speed,
        angle: math.pi / 2 + (random.nextDouble() - 0.5) * 0.5, // Движение вниз с небольшим отклонением
        size: size,
        brightness: brightness,
        rotation: random.nextDouble() * 2 * math.pi, // Случайное начальное вращение
        rotationSpeed: (random.nextDouble() - 0.5) * 0.1, // Случайная скорость вращения
        color: color,
        shape: shape,
        vertices: vertices,
      );
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final size = MediaQuery.of(context).size;
        
        // Обновляем позиции астероидов
        for (var asteroid in _asteroids) {
          asteroid.update(size);
        }
        
        return CustomPaint(
          size: size,
          painter: AsteroidsPainter(_asteroids),
        );
      },
    );
  }
}

/// Painter для отрисовки астероидов
class AsteroidsPainter extends CustomPainter {
  final List<Asteroid> asteroids;
  
  AsteroidsPainter(this.asteroids);
  
  @override
  void paint(Canvas canvas, Size size) {
    for (var asteroid in asteroids) {
      final paint = Paint()
        ..color = asteroid.color
        ..style = PaintingStyle.fill;
      
      // Сохраняем текущее состояние холста
      canvas.save();
      
      // Перемещаем холст в позицию астероида
      canvas.translate(asteroid.position.dx, asteroid.position.dy);
      
      // Вращаем холст
      canvas.rotate(asteroid.rotation);
      
      // Рисуем астероид в зависимости от его формы
      if (asteroid.shape == 0) {
        // Круг
        canvas.drawCircle(Offset.zero, asteroid.size, paint);
      } else {
        // Многоугольник
        final path = Path();
        final vertices = asteroid.vertices;
        
        for (int i = 0; i < vertices; i++) {
          final angle = 2 * math.pi * i / vertices;
          final x = math.cos(angle) * asteroid.size;
          final y = math.sin(angle) * asteroid.size;
          
          if (i == 0) {
            path.moveTo(x, y);
          } else {
            path.lineTo(x, y);
          }
        }
        
        path.close();
        canvas.drawPath(path, paint);
      }
      
      // Восстанавливаем состояние холста
      canvas.restore();
    }
  }
  
  @override
  bool shouldRepaint(covariant AsteroidsPainter oldDelegate) => true;
}
