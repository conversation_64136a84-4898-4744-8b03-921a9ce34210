# TMM Flutter App Makefile

.PHONY: help full lite clean build-full build-lite test

# Default target
help:
	@echo "TMM Flutter App - Команды для управления"
	@echo ""
	@echo "Запуск приложений:"
	@echo "  make switcher      - Запустить с тумблером (Рекомендуется)"
	@echo "  make full          - Запустить полную версию"
	@echo "  make lite          - Запустить лайт версию"
	@echo "  make full-web      - Запустить полную версию в браузере"
	@echo "  make lite-mobile   - Запустить лайт версию на мобильном"
	@echo ""
	@echo "Сборка:"
	@echo "  make build-full    - Собрать полную версию"
	@echo "  make build-lite    - Собрать лайт версию"
	@echo "  make build-web     - Собрать для веб"
	@echo "  make build-apk     - Собрать APK (лайт версия)"
	@echo ""
	@echo "Разработка:"
	@echo "  make clean         - Очистить проект"
	@echo "  make test          - Запустить тесты"
	@echo "  make format        - Форматировать код"
	@echo "  make analyze       - Анализ кода"

# Запуск приложений
full:
	@echo "🚀 Запуск полной версии TMM..."
	flutter run lib/main.dart

lite:
	@echo "📱 Запуск лайт версии TMM..."
	flutter run lib/main_lite.dart

switcher:
	@echo "🎛️ Запуск TMM с тумблером (Рекомендуется)..."
	flutter run lib/main_with_switcher.dart

toggle:
	@echo "🎛️ Запуск TMM с тумблером..."
	flutter run lib/main_with_switcher.dart

full-web:
	@echo "🌐 Запуск полной версии в браузере..."
	flutter run -d chrome lib/main.dart

lite-mobile:
	@echo "📱 Запуск лайт версии на мобильном..."
	flutter run -d mobile lib/main_lite.dart

# Сборка
build-full:
	@echo "🔨 Сборка полной версии..."
	flutter build apk lib/main.dart --release

build-lite:
	@echo "🔨 Сборка лайт версии..."
	flutter build apk lib/main_lite.dart --release

build-web:
	@echo "🌐 Сборка для веб..."
	flutter build web lib/main.dart

build-apk:
	@echo "📦 Сборка APK (лайт версия)..."
	flutter build apk lib/main_lite.dart --release

# Разработка
clean:
	@echo "🧹 Очистка проекта..."
	flutter clean
	flutter pub get

test:
	@echo "🧪 Запуск тестов..."
	flutter test

format:
	@echo "✨ Форматирование кода..."
	dart format .

analyze:
	@echo "🔍 Анализ кода..."
	flutter analyze

# Установка зависимостей
deps:
	@echo "📦 Установка зависимостей..."
	flutter pub get

# Обновление зависимостей
upgrade:
	@echo "⬆️ Обновление зависимостей..."
	flutter pub upgrade
