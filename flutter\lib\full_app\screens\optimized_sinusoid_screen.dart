import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import '../widgets/app_bottom_navigation.dart';
import '../models/sentiment_history_model.dart';
import '../services/optimized_sentiment_service.dart';
import '../services/stable_prediction_service.dart';

/// An optimized version of the Sinusoid screen with better performance and data display
class OptimizedSinusoidScreen extends StatefulWidget {
  const OptimizedSinusoidScreen({Key? key}) : super(key: key);

  @override
  State<OptimizedSinusoidScreen> createState() => _OptimizedSinusoidScreenState();
}

class _OptimizedSinusoidScreenState extends State<OptimizedSinusoidScreen> {
  // Services
  final OptimizedSentimentService _sentimentService = OptimizedSentimentService();
  final StablePredictionService _predictionService = StablePredictionService();

  // State variables
  bool _isLoading = true;
  double _indicatorValue = 50.0;
  Map<String, double> _metrics = {};

  // Prediction data
  bool _isLoadingPredictions = true;
  List<SentimentHistoryEntry> _predictions = [];

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  /// Load all data in parallel
  Future<void> _loadAllData() async {
    debugPrint('Loading all data...');

    setState(() {
      _isLoading = true;
      _isLoadingPredictions = true;
    });

    try {
      // Load current sentiment and predictions in parallel
      await Future.wait([
        _loadCurrentSentiment(),
        _loadPredictions(),
      ]);

      debugPrint('All data loaded successfully');
    } catch (e) {
      debugPrint('Error loading data: $e');

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Load current sentiment data
  Future<void> _loadCurrentSentiment() async {
    try {
      debugPrint('Loading current sentiment...');

      // Get sentiment data
      final data = await _sentimentService.getMarketSentiment();

      // Update state if still mounted
      if (mounted) {
        setState(() {
          _indicatorValue = data['indicator'];
          _metrics = Map<String, double>.from(data['metrics']);
          _isLoading = false;
        });
      }

      debugPrint('Current sentiment loaded: $_indicatorValue');
    } catch (e) {
      debugPrint('Error loading current sentiment: $e');

      // Update state to show error
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      // Re-throw to be caught by _loadAllData
      rethrow;
    }
  }



  /// Load predictions
  Future<void> _loadPredictions() async {
    try {
      debugPrint('Loading predictions...');

      // Get predictions for the next 2 days
      final predictions = await _predictionService.predictFutureSentiment(2);

      // Update state if still mounted
      if (mounted) {
        setState(() {
          _predictions = predictions;
          _isLoadingPredictions = false;
        });
      }

      debugPrint('Predictions loaded: ${predictions.length}');
    } catch (e) {
      debugPrint('Error loading predictions: $e');

      // Update state to show error
      if (mounted) {
        setState(() {
          _isLoadingPredictions = false;
        });
      }

      // Re-throw to be caught by _loadAllData
      rethrow;
    }
  }

  /// Reset all data (for testing)
  Future<void> _resetData() async {
    debugPrint('Resetting all data...');

    // Show loading indicators
    setState(() {
      _isLoading = true;
      _isLoadingPredictions = true;
    });

    try {
      // Clear all data
      await _sentimentService.clearAllData();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All data has been reset'),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // Reload all data
      await _loadAllData();
    } catch (e) {
      debugPrint('Error resetting data: $e');

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error resetting data: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      // Ensure loading indicators are hidden
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isLoadingPredictions = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Market Sentiment'),
        backgroundColor: Colors.black,
        elevation: 0,
        actions: [
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAllData,
            tooltip: 'Refresh data',
          ),
          // Reset button (for testing)
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: _resetData,
            tooltip: 'Reset data (for testing)',
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 2,
        onTap: (index) {
          if (index != 2) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 1:
                Navigator.pushReplacementNamed(context, '/charts');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }

  /// Build the main body of the screen
  Widget _buildBody() {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Top section with indicator and predictions
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Title and current value
                      Text(
                        'Current Market Sentiment: ${_indicatorValue.toStringAsFixed(1)}',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: _getProgressColor(_indicatorValue),
                        ),
                      ),
                      Text(
                        _getCurrentLevel(_indicatorValue),
                        style: TextStyle(
                          fontSize: 16,
                          color: _getProgressColor(_indicatorValue),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Main content row with indicator and predictions
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left column - Main indicator
                          Expanded(
                            flex: 3,
                            child: _buildIndicator(),
                          ),

                          // Right column - Predictions
                          Expanded(
                            flex: 2,
                            child: _buildPredictionsColumn(),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 10),

                // Metrics section (full width)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildMetricsSection(),
                ),
              ],
            ),
          );
  }

  /// Build the indicator gauge
  Widget _buildIndicator() {
    return SizedBox(
      height: 300,
      child: SfLinearGauge(
        orientation: LinearGaugeOrientation.vertical,
        minimum: 0,
        maximum: 100,
        showLabels: false,
        showTicks: false,
        axisTrackStyle: const LinearAxisTrackStyle(
          thickness: 20,
          edgeStyle: LinearEdgeStyle.bothCurve,
          color: Colors.grey,
        ),
        ranges: const [
          LinearGaugeRange(startValue: 0, endValue: 20, color: Colors.red),
          LinearGaugeRange(startValue: 20, endValue: 40, color: Colors.orange),
          LinearGaugeRange(startValue: 40, endValue: 60, color: Colors.yellow),
          LinearGaugeRange(startValue: 60, endValue: 80, color: Colors.lime),
          LinearGaugeRange(startValue: 80, endValue: 100, color: Colors.green),
        ],
        markerPointers: [
          LinearShapePointer(
            value: _indicatorValue,
            shapeType: LinearShapePointerType.triangle,
            position: LinearElementPosition.outside,
            color: Colors.white,
          ),
        ],
      ),
    );
  }



  /// Build the predictions column
  Widget _buildPredictionsColumn() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Text(
          'Future Prognosis',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        if (_isLoadingPredictions)
          const SizedBox(
            width: 30,
            height: 30,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        else if (_predictions.isEmpty)
          const Text(
            'No predictions available',
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: Colors.grey,
            ),
          )
        else
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade900,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.grey.shade800,
                width: 1,
              ),
            ),
            child: Column(
              children: _predictions.asMap().entries.map((entry) {
                final index = entry.key;
                final prediction = entry.value;
                final day = index + 1;

                // Calculate change from current value
                final changeFromCurrent = prediction.value - _indicatorValue;
                final changePercent = (changeFromCurrent / _indicatorValue * 100).abs();
                final isPositive = changeFromCurrent > 0;
                final changeColor = isPositive ? Colors.green : Colors.red;
                final changeIcon = isPositive ? Icons.arrow_upward : Icons.arrow_downward;
                final changeText = isPositive ? '+' : '-';

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Column(
                    children: [
                      Text(
                        'Day $day (${_formatDate(prediction.date)})',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        prediction.value.toStringAsFixed(1),
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: _getProgressColor(prediction.value),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getCurrentLevel(prediction.value),
                        style: TextStyle(
                          fontSize: 12,
                          color: _getProgressColor(prediction.value),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Show expected change
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(changeIcon, color: changeColor, size: 14),
                          const SizedBox(width: 4),
                          Text(
                            '$changeText${changePercent.toStringAsFixed(1)}%',
                            style: TextStyle(
                              color: changeColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  /// Build the metrics section
  Widget _buildMetricsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        const Padding(
          padding: EdgeInsets.only(left: 8.0, bottom: 16.0),
          child: Text(
            'Market Sentiment Metrics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),

        // Traditional metrics
        const Padding(
          padding: EdgeInsets.only(left: 8.0, bottom: 8.0),
          child: Text(
            'Traditional Metrics',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white70,
            ),
          ),
        ),

        _buildMetricItem(
          'Fear & Greed Index (20%)',
          _metrics['Fear & Greed Index'] ?? 50.0,
          'Alternative.me - Market sentiment indicator'
        ),
        const SizedBox(height: 16),

        _buildMetricItem(
          'News Sentiment (15%)',
          _metrics['News Sentiment'] ?? 50.0,
          'CryptoPanic - Aggregated news sentiment'
        ),
        const SizedBox(height: 16),

        _buildMetricItem(
          'Bitcoin Dominance (15%)',
          _metrics['Bitcoin Dominance'] ?? 50.0,
          'CoinGecko - BTC market share of total crypto market'
        ),
        const SizedBox(height: 16),

        // Market structure metrics
        const Padding(
          padding: EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
          child: Text(
            'Market Structure Metrics',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white70,
            ),
          ),
        ),

        _buildMetricItem(
          'Altcoin Share (15%)',
          _metrics['Altcoin Share'] ?? 50.0,
          'CoinGecko - Non-BTC market share'
        ),
        const SizedBox(height: 16),

        _buildMetricItem(
          'Stablecoin Liquidity (15%)',
          _metrics['Stablecoin Liquidity'] ?? 50.0,
          'CoinGecko - Total stablecoin market cap'
        ),
        const SizedBox(height: 16),

        // Trading metrics
        const Padding(
          padding: EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
          child: Text(
            'Trading Metrics',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white70,
            ),
          ),
        ),

        _buildMetricItem(
          'Put/Call Ratio (10%)',
          _metrics['Put/Call Ratio'] ?? 50.0,
          'Binance - Bid/ask volume ratio'
        ),
        const SizedBox(height: 16),

        _buildMetricItem(
          'Order Book Depth (10%)',
          _metrics['Order Book Depth'] ?? 50.0,
          'Binance - Total bid volume'
        ),
        const SizedBox(height: 16),

        _buildMetricItem(
          'Volume Change Velocity (10%)',
          _metrics['Volume Change Velocity'] ?? 50.0,
          'Binance - 24h volume change'
        ),
        const SizedBox(height: 16),

        _buildMetricItem(
          'Volatility Index (10%)',
          _metrics['Volatility Index'] ?? 50.0,
          'Binance - Price volatility coefficient'
        ),
        const SizedBox(height: 16),

        // Social metrics
        const Padding(
          padding: EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
          child: Text(
            'Social Metrics',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white70,
            ),
          ),
        ),

        _buildMetricItem(
          'Social Engagement (10%)',
          _metrics['Social Engagement'] ?? 50.0,
          'CoinGecko - Twitter followers and Reddit activity'
        ),
        const SizedBox(height: 16),

        _buildMetricItem(
          'Holders Score (15%)',
          _metrics['Holders Score'] ?? 50.0,
          'CoinGecko - Market cap and trading volume'
        ),
        const SizedBox(height: 16),

        _buildMetricItem(
          'Volume Score (15%)',
          _metrics['Volume Score'] ?? 50.0,
          'Binance / CoinGecko - 24h trading volume'
        ),
        const SizedBox(height: 16),

        _buildMetricItem(
          'Price Volatility (10%)',
          _metrics['Price Volatility'] ?? 50.0,
          'CoinGecko - 7-day price volatility'
        ),

        // Add explanation of the indicator
        const Padding(
          padding: EdgeInsets.only(top: 24.0, left: 8.0, right: 8.0),
          child: Text(
            'The Market Sentiment Indicator combines these metrics using a weighted average to provide a comprehensive view of the current market conditions. All data is sourced from free public APIs.',
            style: TextStyle(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  /// Build a metric item with progress bar
  Widget _buildMetricItem(String name, double value, String source) {
    final progressColor = _getProgressColor(value);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade900,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Metric name and source
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      source,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),

              // Value
              Text(
                value.toStringAsFixed(1),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: progressColor,
                ),
              ),
            ],
          ),

          const SizedBox(height: 10),

          // Progress bar
          Stack(
            children: [
              // Background
              Container(
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.grey.shade800,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),

              // Progress
              FractionallySizedBox(
                widthFactor: value / 100,
                child: Container(
                  height: 8,
                  decoration: BoxDecoration(
                    color: progressColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Get the color for a progress value
  Color _getProgressColor(double value) {
    if (value <= 20) {
      return Colors.red;
    } else if (value <= 40) {
      return Colors.orange;
    } else if (value <= 60) {
      return Colors.yellow;
    } else if (value <= 80) {
      return Colors.lime;
    } else {
      return Colors.green;
    }
  }

  /// Get the current level description
  String _getCurrentLevel(double value) {
    if (value <= 20) {
      return "Extreme Fear (Crash)";
    } else if (value <= 40) {
      return "Fear (Anxiety)";
    } else if (value <= 60) {
      return "Neutral (Stasis)";
    } else if (value <= 80) {
      return "Greed (Lift)";
    } else {
      return "Extreme Greed (Surge)";
    }
  }

  /// Format date as day/month
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}';
  }
}
