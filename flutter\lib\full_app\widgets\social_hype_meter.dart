import 'package:flutter/material.dart';
import '../models/anti_fomo_simulator_models.dart';

/// A widget that displays the social hype meter with animation
class SocialHypeMeter extends StatefulWidget {
  final SocialHype hype;
  final double width;
  final double height;

  const SocialHypeMeter({
    super.key,
    required this.hype,
    this.width = double.infinity,
    this.height = 40,
  });

  @override
  State<SocialHypeMeter> createState() => _SocialHypeMeterState();
}

class _SocialHypeMeterState extends State<SocialHypeMeter> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _levelAnimation;
  double _previousLevel = 50;

  @override
  void initState() {
    super.initState();
    _previousLevel = widget.hype.level.toDouble();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _levelAnimation = Tween<double>(
      begin: _previousLevel,
      end: widget.hype.level.toDouble(),
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward();
  }

  @override
  void didUpdateWidget(SocialHypeMeter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.hype.level != widget.hype.level) {
      _previousLevel = oldWidget.hype.level.toDouble();
      _levelAnimation = Tween<double>(
        begin: _previousLevel,
        end: widget.hype.level.toDouble(),
      ).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeInOut,
        ),
      );
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final actualWidth = constraints.maxWidth;
        final currentLevel = _levelAnimation.value;
        final fillWidth = (currentLevel / 100) * actualWidth;
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(widget.height / 2),
            border: Border.all(
              color: Colors.white,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.07),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Градиентная заливка только до текущего уровня
              ClipRRect(
                borderRadius: BorderRadius.circular(widget.height / 2 - 2),
                child: Align(
                  alignment: Alignment.centerLeft,
                  widthFactor: currentLevel / 100,
                  child: Container(
                    width: actualWidth,
                    height: widget.height,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFF34C759), // green
                          Color(0xFFFFFF00), // yellow
                          Color(0xFFFF9500), // orange
                          Color(0xFFFF3B30), // red
                        ],
                        stops: [0.0, 0.33, 0.66, 1.0],
                      ),
                    ),
                  ),
                ),
              ),
              // Белый фон поверх незаполненной части
              if (currentLevel < 100)
                Positioned(
                  left: fillWidth,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.horizontal(
                        right: Radius.circular(widget.height / 2 - 2),
                      ),
                    ),
                  ),
                ),
              // Индикатор
              Positioned(
                left: (actualWidth - 16) * (currentLevel / 100),
                top: (widget.height - 16) / 2,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${currentLevel.toInt()}',
                      style: const TextStyle(
                        color: Colors.black87,
                        fontWeight: FontWeight.w700,
                        fontSize: 12,
                        letterSpacing: 0.1,
                      ),
                    ),
                  ),
                ),
              ),
              // Labels
              Positioned.fill(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Low',
                        style: TextStyle(
                          color: const Color(0xFF34C759),
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                          letterSpacing: 0.1,
                          shadows: [
                            Shadow(
                              color: Colors.white.withOpacity(0.7),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                      Text(
                        'High',
                        style: TextStyle(
                          color: const Color(0xFFFF3B30),
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                          letterSpacing: 0.1,
                          shadows: [
                            Shadow(
                              color: Colors.white.withOpacity(0.7),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  // Helper function to get color based on level
  Color _getIndicatorColor(double level) {
    if (level < 50) {
      return const Color(0xFF34C759); // iOS green
    } else {
      return const Color(0xFFFF3B30); // iOS red
    }
  }
}
