import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

// Import both app versions
import 'main.dart' as full_app;
import 'main_lite.dart' as lite_app;

void main() {
  // Проверяем переменную окружения или используем дефолтное значение
  const String appMode = String.fromEnvironment('APP_MODE', defaultValue: 'full');
  
  if (kDebugMode) {
    print('🚀 Запуск приложения в режиме: $appMode');
  }
  
  switch (appMode.toLowerCase()) {
    case 'lite':
    case 'mobile':
      lite_app.main();
      break;
    case 'full':
    case 'desktop':
    default:
      full_app.main();
      break;
  }
}
