import 'package:flutter/material.dart';

class ReactStyleHoverButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;

  const ReactStyleHoverButton({
    Key? key,
    this.text = 'Hover me',
    this.onPressed,
  }) : super(key: key);

  @override
  State<ReactStyleHoverButton> createState() => _ReactStyleHoverButtonState();
}

class _ReactStyleHoverButtonState extends State<ReactStyleHoverButton> 
    with SingleTickerProviderStateMixin {
  bool isHovered = false;
  late AnimationController _controller;
  late Animation<double> _widthAnimation;
  late Animation<double> _heightAnimation;
  late Animation<Color?> _textColorAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Анимация линии снизу (width: 0 -> 100%)
    _widthAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
      ),
    );

    // Анимация заполнения фона (height: 0 -> 100%)
    _heightAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 1.0, curve: Curves.easeInOut),
      ),
    );

    // Анимация изменения цвета текста
    _textColorAnimation = ColorTween(
      begin: const Color(0xFFFFEDD3), // #ffedd3
      end: const Color(0xFF1E1E2B),   // #1e1e2b
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 1.0, curve: Curves.easeInOut),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          isHovered = true;
        });
        _controller.forward();
      },
      onExit: (_) {
        setState(() {
          isHovered = false;
        });
        _controller.reverse();
      },
      child: GestureDetector(
        onTap: widget.onPressed,
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Stack(
                clipBehavior: Clip.none,
                alignment: Alignment.center,
                children: [
                  // Базовый контейнер для позиционирования
                  Container(
                    color: Colors.transparent,
                    child: Text(
                      widget.text.toUpperCase(),
                      style: TextStyle(
                        color: _textColorAnimation.value ?? const Color(0xFFFFEDD3),
                        fontSize: 17,
                        letterSpacing: 1.0,
                      ),
                    ),
                  ),
                  
                  // Горизонтальная линия снизу (аналог ::before)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 2,
                      width: MediaQuery.of(context).size.width * 0.2 * _widthAnimation.value,
                      color: const Color(0xFFFFC506), // #ffc506
                    ),
                  ),
                  
                  // Фоновое заполнение (аналог ::after)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 50 * _heightAnimation.value,
                      width: MediaQuery.of(context).size.width * 0.2,
                      color: const Color(0xFFFFC506), // #ffc506
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
