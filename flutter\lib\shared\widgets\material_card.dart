import 'package:flutter/material.dart';
import '../models/material_item.dart';

class MaterialCard extends StatelessWidget {
  final MaterialItem materialItem;
  final VoidCallback onTap;

  const MaterialCard({
    super.key,
    required this.materialItem,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and save button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      materialItem.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16.0,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      materialItem.isSaved ? Icons.bookmark : Icons.bookmark_border,
                      color: materialItem.isSaved ? Colors.amber : Colors.grey,
                    ),
                    onPressed: () {
                      // Toggle saved status (would be implemented with state management)
                    },
                  ),
                ],
              ),
              
              // Level indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                decoration: BoxDecoration(
                  color: _getLevelColor(materialItem.level).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Text(
                  _getLevelText(materialItem.level),
                  style: TextStyle(
                    color: _getLevelColor(materialItem.level),
                    fontSize: 12.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 8.0),
              
              // Description
              Text(
                materialItem.description,
                style: const TextStyle(fontSize: 14.0),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8.0),
              
              // Tags
              Wrap(
                spacing: 6.0,
                runSpacing: 6.0,
                children: materialItem.tags.map((tag) => _buildTag(tag)).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Text(
        tag,
        style: TextStyle(
          color: Colors.blue[800],
          fontSize: 12.0,
        ),
      ),
    );
  }

  Color _getLevelColor(MaterialLevel level) {
    switch (level) {
      case MaterialLevel.beginner:
        return Colors.green;
      case MaterialLevel.intermediate:
        return Colors.orange;
      case MaterialLevel.advanced:
        return Colors.red;
    }
  }

  String _getLevelText(MaterialLevel level) {
    switch (level) {
      case MaterialLevel.beginner:
        return 'Beginner';
      case MaterialLevel.intermediate:
        return 'Intermediate';
      case MaterialLevel.advanced:
        return 'Advanced';
    }
  }
}
