import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../services/quiz_service.dart';

class QuizStatisticsScreen extends StatefulWidget {
  const QuizStatisticsScreen({Key? key}) : super(key: key);

  @override
  State<QuizStatisticsScreen> createState() => _QuizStatisticsScreenState();
}

class _QuizStatisticsScreenState extends State<QuizStatisticsScreen> 
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  Map<String, dynamic> _stats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadStatistics();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.elasticOut),
    ));
  }

  Future<void> _loadStatistics() async {
    _stats = await QuizService.instance.getOverallStats();
    
    setState(() {
      _isLoading = false;
    });

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1A1A1A),
              Color(0xFF0A0A0A),
              Color(0xFF2A2A2A),
            ],
          ),
        ),
        child: SafeArea(
          child: _isLoading ? _buildLoadingScreen() : _buildContent(),
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return const Center(
      child: CupertinoActivityIndicator(
        color: Color(0xFF007AFF),
        radius: 20,
      ),
    );
  }

  Widget _buildContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            _buildHeader(),
            _buildOverallStatsSection(),
            _buildDetailedStatsSection(),
            _buildAchievementsSection(),
            const SliverToBoxAdapter(child: SizedBox(height: 40)),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Row(
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () => Navigator.pop(context),
                  child: const Icon(
                    CupertinoIcons.back,
                    color: Color(0xFF007AFF),
                    size: 28,
                  ),
                ),
                const Spacer(),
                const Text(
                  'Статистика',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 28,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const Spacer(),
                const SizedBox(width: 44), // Балансировка для центрирования
              ],
            ),
            const SizedBox(height: 20),
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF007AFF), Color(0xFF5856D6)],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF007AFF).withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Icon(
                CupertinoIcons.chart_bar,
                color: Colors.white,
                size: 32,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallStatsSection() {
    final totalQuestions = _stats['total_questions_answered'] ?? 0;
    final totalCorrect = _stats['total_correct_answers'] ?? 0;
    final sessionsPlayed = _stats['sessions_played'] ?? 0;
    final bestAccuracy = _stats['best_accuracy'] ?? 0.0;
    final overallAccuracy = totalQuestions > 0 ? (totalCorrect / totalQuestions) * 100 : 0.0;

    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Общая статистика',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: _buildMainStatCard(
                      '🎯',
                      'Лучший результат',
                      '${bestAccuracy.toStringAsFixed(1)}%',
                      const Color(0xFFFFD700),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildMainStatCard(
                      '📊',
                      'Общая точность',
                      '${overallAccuracy.toStringAsFixed(1)}%',
                      const Color(0xFF007AFF),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildMainStatCard(
                      '🎮',
                      'Игр сыграно',
                      '$sessionsPlayed',
                      const Color(0xFF34C759),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildMainStatCard(
                      '❓',
                      'Всего вопросов',
                      '$totalQuestions',
                      const Color(0xFF5856D6),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainStatCard(String emoji, String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 32),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 24,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedStatsSection() {
    final totalQuestions = _stats['total_questions_answered'] ?? 0;
    final totalCorrect = _stats['total_correct_answers'] ?? 0;
    final totalWrong = totalQuestions - totalCorrect;
    final sessionsPlayed = _stats['sessions_played'] ?? 0;
    final avgQuestionsPerSession = sessionsPlayed > 0 ? totalQuestions / sessionsPlayed : 0.0;

    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Детальная статистика',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 20),
              _buildStatRow('✅', 'Правильных ответов', '$totalCorrect', Colors.green),
              const SizedBox(height: 12),
              _buildStatRow('❌', 'Неправильных ответов', '$totalWrong', Colors.red),
              const SizedBox(height: 12),
              _buildStatRow('📈', 'Среднее вопросов за игру', avgQuestionsPerSession.toStringAsFixed(1), const Color(0xFF007AFF)),
              const SizedBox(height: 20),
              _buildProgressBar(
                'Прогресс обучения',
                totalCorrect / (totalQuestions > 0 ? totalQuestions : 1),
                Colors.green,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String emoji, String title, String value, Color color) {
    return Row(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: color.withOpacity(0.15),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Center(
            child: Text(
              emoji,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar(String title, double progress, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${(progress * 100).toStringAsFixed(1)}%',
              style: TextStyle(
                color: color,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withOpacity(0.1),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8,
          ),
        ),
      ],
    );
  }

  Widget _buildAchievementsSection() {
    final totalQuestions = _stats['total_questions_answered'] ?? 0;
    final sessionsPlayed = _stats['sessions_played'] ?? 0;
    final bestAccuracy = _stats['best_accuracy'] ?? 0.0;

    final achievements = _calculateAchievements(totalQuestions, sessionsPlayed, bestAccuracy);

    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Достижения',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 20),
              ...achievements.map((achievement) => Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: _buildAchievementCard(
                  achievement['emoji'],
                  achievement['title'],
                  achievement['description'],
                  achievement['isUnlocked'],
                  achievement['color'],
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAchievementCard(String emoji, String title, String description, bool isUnlocked, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isUnlocked 
            ? color.withOpacity(0.1)
            : Colors.white.withOpacity(0.02),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isUnlocked 
              ? color.withOpacity(0.3)
              : Colors.white.withOpacity(0.05),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isUnlocked 
                  ? color.withOpacity(0.2)
                  : Colors.white.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                emoji,
                style: TextStyle(
                  fontSize: 24,
                  color: isUnlocked ? null : Colors.white.withOpacity(0.3),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: isUnlocked 
                        ? Colors.white
                        : Colors.white.withOpacity(0.5),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    color: isUnlocked 
                        ? Colors.white.withOpacity(0.7)
                        : Colors.white.withOpacity(0.3),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          if (isUnlocked)
            Icon(
              CupertinoIcons.checkmark_circle_fill,
              color: color,
              size: 24,
            ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _calculateAchievements(int totalQuestions, int sessionsPlayed, double bestAccuracy) {
    return [
      {
        'emoji': '🎯',
        'title': 'Первые шаги',
        'description': 'Ответить на 10 вопросов',
        'isUnlocked': totalQuestions >= 10,
        'color': const Color(0xFF34C759),
      },
      {
        'emoji': '📚',
        'title': 'Студент',
        'description': 'Ответить на 100 вопросов',
        'isUnlocked': totalQuestions >= 100,
        'color': const Color(0xFF007AFF),
      },
      {
        'emoji': '🎓',
        'title': 'Эксперт',
        'description': 'Ответить на 500 вопросов',
        'isUnlocked': totalQuestions >= 500,
        'color': const Color(0xFF5856D6),
      },
      {
        'emoji': '🏆',
        'title': 'Мастер',
        'description': 'Достичь 90% точности',
        'isUnlocked': bestAccuracy >= 90,
        'color': const Color(0xFFFFD700),
      },
      {
        'emoji': '🔥',
        'title': 'Любитель викторин',
        'description': 'Сыграть 10 игр',
        'isUnlocked': sessionsPlayed >= 10,
        'color': const Color(0xFFFF9500),
      },
      {
        'emoji': '💎',
        'title': 'Постоянство',
        'description': 'Сыграть 50 игр',
        'isUnlocked': sessionsPlayed >= 50,
        'color': const Color(0xFF00C7BE),
      },
    ];
  }
} 