import 'package:finance_ai/models/sentiment_types.dart';

class SentimentData {
  final SentimentType sentiment;
  final NewsImpact impact;
  final double score;
  final double confidence;
  final String reasoning;
  final String marketImpact;
  final Map<String, dynamic> metadata;

  const SentimentData({
    required this.sentiment,
    required this.impact,
    required this.score,
    this.confidence = 0.5,
    this.reasoning = '',
    this.marketImpact = '',
    this.metadata = const {},
  });

  factory SentimentData.fromJson(Map<String, dynamic> json) {
    // Поддерживаем как новый формат (sentimentData), так и старый (прямые поля)
    final sentimentData = json['sentimentData'] ?? json;

    return SentimentData(
      sentiment: _parseSentimentType(sentimentData['sentiment']),
      impact: _parseNewsImpact(sentimentData['impact']),
      score: (sentimentData['score'] ?? 0.5).toDouble(),
      confidence: (sentimentData['confidence'] ?? 0.5).toDouble(),
      reasoning: sentimentData['reasoning'] ?? '',
      marketImpact: sentimentData['marketImpact'] ?? '',
      metadata: json['metadata'] ?? {},
    );
  }

  static SentimentType _parseSentimentType(dynamic sentiment) {
    if (sentiment == null) return SentimentType.neutral;

    final sentimentStr = sentiment.toString().toLowerCase();
    switch (sentimentStr) {
      case 'positive':
        return SentimentType.positive;
      case 'negative':
        return SentimentType.negative;
      default:
        return SentimentType.neutral;
    }
  }

  static NewsImpact _parseNewsImpact(dynamic impact) {
    if (impact == null) return NewsImpact.medium;

    final impactStr = impact.toString().toLowerCase();
    switch (impactStr) {
      case 'high':
        return NewsImpact.high;
      case 'low':
        return NewsImpact.low;
      default:
        return NewsImpact.medium;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'sentiment': sentiment.toString().split('.').last,
      'impact': impact.toString().split('.').last,
      'score': score,
      'confidence': confidence,
      'reasoning': reasoning,
      'marketImpact': marketImpact,
      'metadata': metadata,
    };
  }

  // Factory constructors for default values
  factory SentimentData.neutral({
    double confidence = 0.5,
    String reasoning = 'Neutral market sentiment',
    String marketImpact = 'Minimal expected impact'
  }) => SentimentData(
    sentiment: SentimentType.neutral,
    impact: NewsImpact.medium,
    score: 0.5,
    confidence: confidence,
    reasoning: reasoning,
    marketImpact: marketImpact,
  );

  factory SentimentData.positive({
    NewsImpact impact = NewsImpact.medium,
    double confidence = 0.8,
    String reasoning = 'Positive market sentiment',
    String marketImpact = 'Potentially bullish impact'
  }) => SentimentData(
    sentiment: SentimentType.positive,
    impact: impact,
    score: 0.8,
    confidence: confidence,
    reasoning: reasoning,
    marketImpact: marketImpact,
  );

  factory SentimentData.negative({
    NewsImpact impact = NewsImpact.medium,
    double confidence = 0.8,
    String reasoning = 'Negative market sentiment',
    String marketImpact = 'Potentially bearish impact'
  }) => SentimentData(
    sentiment: SentimentType.negative,
    impact: impact,
    score: 0.8,
    confidence: confidence,
    reasoning: reasoning,
    marketImpact: marketImpact,
  );
} 