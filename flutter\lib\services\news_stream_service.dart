import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/news_item.dart';
import '../models/sentiment_data.dart';
import '../models/sentiment_types.dart';

/// Типы событий из потока новостей
enum NewsStreamEventType {
  newNews,
  connected,
  disconnected,
  error,
}

class NewsStreamService {
  static const String _backendBaseUrl = kDebugMode
    ? 'http://localhost:4000'
    : 'https://your-domain.com/api'; // Замените на ваш продакшн URL

  StreamController<NewsStreamEvent>? _streamController;
  http.Client? _httpClient;
  bool _isConnected = false;
  bool _isConnecting = false;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 3);

  Stream<NewsStreamEvent> get newsStream {
    _streamController ??= StreamController<NewsStreamEvent>.broadcast();
    return _streamController!.stream;
  }

  bool get isConnected => _isConnected;
  bool get isConnecting => _isConnecting;
  
  /// Подключение к потоку новостей
  Future<void> connect() async {
    if (_isConnected || _isConnecting) {
      debugPrint('[NewsStream] Уже подключен или подключается к потоку');
      return;
    }

    _isConnecting = true;
    _cancelReconnectTimer();

    try {
      debugPrint('[NewsStream] Попытка подключения к $_backendBaseUrl/news/stream');

      // Закрываем предыдущий клиент если есть
      _httpClient?.close();
      _httpClient = http.Client();

      final request = http.Request('GET', Uri.parse('$_backendBaseUrl/news/stream'));

      // Добавляем заголовки для SSE
      request.headers.addAll({
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      });

      final response = await _httpClient!.send(request);

      if (response.statusCode == 200) {
        _isConnected = true;
        _isConnecting = false;
        _reconnectAttempts = 0; // Сбрасываем счетчик попыток
        debugPrint('[NewsStream] Успешно подключен к потоку новостей');

        _streamController ??= StreamController<NewsStreamEvent>.broadcast();

        // Слушаем поток данных
        response.stream
            .transform(utf8.decoder)
            .transform(const LineSplitter())
            .listen(
          _handleStreamData,
          onError: _handleStreamError,
          onDone: _handleStreamDone,
          cancelOnError: false, // Не отменяем подписку при ошибке
        );
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      debugPrint('[NewsStream] Ошибка подключения: $e');
      _isConnected = false;
      _isConnecting = false;
      _streamController?.addError(e);

      // Планируем переподключение
      _scheduleReconnect();
    }
  }
  
  /// Отключение от потока
  void disconnect() {
    debugPrint('[NewsStream] Отключение от потока новостей');

    _cancelReconnectTimer();
    _httpClient?.close();
    _httpClient = null;
    _isConnected = false;
    _isConnecting = false;
  }

  /// Отмена таймера переподключения
  void _cancelReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Планирование переподключения
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint('[NewsStream] Превышено максимальное количество попыток переподключения');
      return;
    }

    _reconnectAttempts++;
    final delay = Duration(seconds: _reconnectDelay.inSeconds * _reconnectAttempts);

    debugPrint('[NewsStream] Планируем переподключение через ${delay.inSeconds} секунд (попытка $_reconnectAttempts/$_maxReconnectAttempts)');

    _reconnectTimer = Timer(delay, () {
      if (!_isConnected && !_isConnecting) {
        debugPrint('[NewsStream] Автоматическое переподключение...');
        connect();
      }
    });
  }
  
  /// Обработка данных из потока
  void _handleStreamData(String line) {
    if (line.startsWith('data: ')) {
      try {
        final jsonData = line.substring(6); // Убираем "data: "
        final data = json.decode(jsonData);
        
        final event = NewsStreamEvent.fromJson(data);
        _streamController?.add(event);
        
        debugPrint('[NewsStream] Получено событие: ${event.type}');
      } catch (e) {
        debugPrint('[NewsStream] Ошибка парсинга данных: $e');
      }
    }
  }
  
  /// Обработка ошибок потока
  void _handleStreamError(Object error) {
    debugPrint('[NewsStream] Ошибка потока: $error');
    _isConnected = false;
    _isConnecting = false;
    _streamController?.addError(error);

    // Планируем переподключение при ошибке
    _scheduleReconnect();
  }

  /// Обработка завершения потока
  void _handleStreamDone() {
    debugPrint('[NewsStream] Поток завершен');
    _isConnected = false;
    _isConnecting = false;

    // Планируем переподключение при завершении потока
    _scheduleReconnect();
  }
  
  /// Закрытие сервиса
  void dispose() {
    debugPrint('[NewsStream] Закрытие сервиса');
    disconnect();
    _streamController?.close();
    _streamController = null;
  }

  /// Принудительное переподключение
  Future<void> reconnect() async {
    debugPrint('[NewsStream] Принудительное переподключение');
    disconnect();
    await Future.delayed(const Duration(milliseconds: 500)); // Небольшая задержка
    await connect();
  }
}

/// Событие из потока новостей
class NewsStreamEvent {
  final NewsStreamEventType type;
  final NewsItem? news;
  final String? message;
  final DateTime timestamp;
  final int? totalCount;
  
  NewsStreamEvent({
    required this.type,
    this.news,
    this.message,
    required this.timestamp,
    this.totalCount,
  });
  
  factory NewsStreamEvent.fromJson(Map<String, dynamic> json) {
    return NewsStreamEvent(
      type: _parseEventType(json['type']),
      news: json['news'] != null ? _parseNewsItem(json['news']) : null,
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      totalCount: json['totalCount'],
    );
  }

  static NewsStreamEventType _parseEventType(String type) {
    switch (type.toLowerCase()) {
      case 'news-added':
      case 'new_news':
        return NewsStreamEventType.newNews;
      case 'connected':
        return NewsStreamEventType.connected;
      case 'disconnected':
        return NewsStreamEventType.disconnected;
      case 'error':
        return NewsStreamEventType.error;
      default:
        debugPrint('[NewsStream] Неизвестный тип события: $type');
        return NewsStreamEventType.newNews;
    }
  }
  
  static NewsItem _parseNewsItem(Map<String, dynamic> json) {
    return NewsItem(
      id: json['id'] ?? '',
      title: json['aiGeneratedTitle'] ?? json['title'] ?? '',
      description: json['originalDescription'] ?? json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? json['urlToImage'] ?? 'https://via.placeholder.com/300x200?text=No+Image',
      publishedAt: DateTime.parse(json['publishedAt'] ?? DateTime.now().toIso8601String()),
      source: json['source'] ?? '',
      url: json['url'] ?? '',
      sentiment: _parseSentimentType(json['sentiment']),
      tags: List<String>.from(json['tags'] ?? []),
      category: _parseNewsCategory(json['tags'] ?? []),
      content: json['rewrittenContent'] ?? json['content'] ?? json['summary'] ?? '',
      summary: json['summary'],
      sentimentData: json['sentimentData'] != null
          ? SentimentData.fromJson(json['sentimentData'])
          : null,
      fetchedAt: DateTime.now(),
    );
  }
  
  static SentimentType _parseSentimentType(dynamic sentiment) {
    if (sentiment == null) return SentimentType.neutral;
    
    final sentimentStr = sentiment.toString().toLowerCase();
    switch (sentimentStr) {
      case 'positive':
        return SentimentType.positive;
      case 'negative':
        return SentimentType.negative;
      default:
        return SentimentType.neutral;
    }
  }
  
  static NewsCategory _parseNewsCategory(List<dynamic> tags) {
    final tagsLower = tags.map((tag) => tag.toString().toLowerCase()).toList();
    
    if (tagsLower.any((tag) => ['bitcoin', 'ethereum', 'crypto', 'blockchain', 'btc', 'eth'].contains(tag))) {
      return NewsCategory.crypto;
    } else if (tagsLower.any((tag) => ['stocks', 'market', 'nasdaq', 'dow', 'finance'].contains(tag))) {
      return NewsCategory.stocks;
    } else if (tagsLower.any((tag) => ['ai', 'artificial intelligence', 'machine learning'].contains(tag))) {
      return NewsCategory.ai;
    } else if (tagsLower.any((tag) => ['politics', 'government', 'regulation'].contains(tag))) {
      return NewsCategory.politics;
    } else if (tagsLower.any((tag) => ['whale', 'institutional'].contains(tag))) {
      return NewsCategory.whales;
    }
    
    return NewsCategory.all;
  }
}
