import 'package:flutter/material.dart';
import '../models/trading_simulator_models.dart';
import '../models/anti_fomo_simulator_models.dart';

/// A widget that displays trading controls (UP/DOWN buttons)
class TradingControls extends StatelessWidget {
  final Function(TradeAction) onActionSelected;
  final bool isEnabled;
  final double buttonSize;

  const TradingControls({
    super.key,
    required this.onActionSelected,
    this.isEnabled = true,
    this.buttonSize = 60.0,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(TradeAction.buy),
        _buildActionButton(TradeAction.sell),
      ],
    );
  }

  Widget _buildActionButton(TradeAction action) {
    return GestureDetector(
      onTap: isEnabled ? () => onActionSelected(action) : null,
      child: Opacity(
        opacity: isEnabled ? 1.0 : 0.5,
        child: Container(
          width: buttonSize,
          height: buttonSize,
          decoration: BoxDecoration(
            color: action.color,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: action.color.withOpacity(0.5),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                action.label,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: buttonSize * 0.2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// A widget that displays FOMO trading controls (BUY/SELL/HOLD buttons)
class FomoTradingControls extends StatelessWidget {
  final Function(FomoTradeAction) onActionSelected;
  final bool isEnabled;
  final int timeLeft;
  final int totalTime;

  const FomoTradingControls({
    super.key,
    required this.onActionSelected,
    this.isEnabled = true,
    required this.timeLeft,
    required this.totalTime,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Timer indicator
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          width: double.infinity,
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey[800],
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: timeLeft / totalTime,
            child: Container(
              decoration: BoxDecoration(
                color: _getTimerColor(),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),

        // Action buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(FomoTradeAction.buy),
            _buildActionButton(FomoTradeAction.hold),
            _buildActionButton(FomoTradeAction.sell),
          ],
        ),
      ],
    );
  }

  Color _getTimerColor() {
    final timeRatio = timeLeft / totalTime;
    if (timeRatio > 0.6) {
      return Colors.green;
    } else if (timeRatio > 0.3) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  Widget _buildActionButton(FomoTradeAction action) {
    return GestureDetector(
      onTap: isEnabled ? () => onActionSelected(action) : null,
      child: Opacity(
        opacity: isEnabled ? 1.0 : 0.5,
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: action.color,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: action.color.withOpacity(0.5),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                action.label,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
