import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/news_provider.dart';

class SSEDebugPanel extends StatelessWidget {
  const SSEDebugPanel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<NewsProvider>(
      builder: (context, newsProvider, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.8),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'SSE Debug Panel',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              
              // Статус подключения
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: newsProvider.isStreamConnected 
                          ? Colors.green 
                          : Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    newsProvider.isStreamConnected 
                        ? 'Подключен к SSE' 
                        : 'Не подключен к SSE',
                    style: TextStyle(
                      color: newsProvider.isStreamConnected 
                          ? Colors.green 
                          : Colors.red,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Количество новостей
              Text(
                'Новостей: ${newsProvider.news.length}',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
              
              // Ошибки
              if (newsProvider.error.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  'Ошибка: ${newsProvider.error}',
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                  ),
                ),
              ],
              
              const SizedBox(height: 12),
              
              // Кнопки управления
              Row(
                children: [
                  ElevatedButton(
                    onPressed: () async {
                      await newsProvider.connectToNewsStream();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Подключить'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      newsProvider.disconnectFromNewsStream();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Отключить'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () async {
                      await newsProvider.reconnectToNewsStream();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Переподключить'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
