import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../models/candle.dart';
import '../models/chart_state.dart';

// Условный импорт для веб-платформы
import 'improved_tradingview_chart.dart';
// Для веб-платформы используем отдельную реализацию
import 'web_improved_tradingview_chart.dart' if (dart.library.io) 'improved_tradingview_chart.dart';

/// Фабрика для создания правильной реализации TradingView в зависимости от платформы
class PlatformImprovedTradingViewChart extends StatefulWidget {
  final List<Candle> allCandles; // Все 250 свечей
  final Function(double price, int time)? onEntryPointSet;
  final Function(bool isUp, double percentChange, double finalPrice)? onTradeResult;

  const PlatformImprovedTradingViewChart({
    super.key,
    required this.allCandles,
    this.onEntryPointSet,
    this.onTradeResult,
  });

  // Статический метод для доступа к методу showAllCandles через GlobalKey
  static void showAllCandles(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartState?;
    state?.showAllCandles();
  }

  // Статический метод для доступа к методу showInitialCandles через GlobalKey
  static void showInitialCandles(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartState?;
    state?.showInitialCandles();
  }

  // Статический метод для доступа к методу setEntryPoint через GlobalKey
  static void setEntryPoint(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartState?;
    state?.setEntryPoint();
  }

  // Статический метод для доступа к методу clearChartElements через GlobalKey
  static void clearChartElements(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartState?;
    state?.clearChartElements();
  }

  // Статический метод для доступа к методу resetChartPosition через GlobalKey
  static void resetChartPosition(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartState?;
    state?.resetChartPosition();
  }

  // Статический метод для сохранения состояния графика
  static void saveChartState(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartState?;
    state?.saveChartState();
  }

  // Статический метод для восстановления состояния графика
  static void restoreChartState(GlobalKey key, ChartState chartState) {
    final state = key.currentState as _PlatformImprovedTradingViewChartState?;
    state?.restoreChartState(chartState);
  }

  @override
  State<PlatformImprovedTradingViewChart> createState() => _PlatformImprovedTradingViewChartState();
}

class _PlatformImprovedTradingViewChartState extends State<PlatformImprovedTradingViewChart> {
  // Ключи для доступа к методам виджетов
  final GlobalKey<WebImprovedTradingViewChartState> _webChartKey = GlobalKey<WebImprovedTradingViewChartState>();
  final GlobalKey<ImprovedTradingViewChartState> _mobileChartKey = GlobalKey<ImprovedTradingViewChartState>();

  // Метод для отображения всех свечей
  void showAllCandles() {
    if (kIsWeb) {
      _webChartKey.currentState?.showAllCandles();
    } else {
      _mobileChartKey.currentState?.showAllCandles();
    }
  }

  // Метод для отображения только первых 243 свечей
  void showInitialCandles() {
    if (kIsWeb) {
      _webChartKey.currentState?.showInitialCandles();
    } else {
      _mobileChartKey.currentState?.showInitialCandles();
    }
  }

  // Метод для установки точки входа
  void setEntryPoint() {
    if (kIsWeb) {
      _webChartKey.currentState?.setEntryPoint();
    } else {
      _mobileChartKey.currentState?.setEntryPoint();
    }
  }

  // Метод для определения результата
  void determineResult() {
    if (kIsWeb) {
      _webChartKey.currentState?.determineResult();
    } else {
      _mobileChartKey.currentState?.determineResult();
    }
  }

  // Метод для очистки элементов графика
  void clearChartElements() {
    if (kIsWeb) {
      _webChartKey.currentState?.clearChartElements();
    } else {
      _mobileChartKey.currentState?.clearChartElements();
    }
  }

  // Метод для сброса позиции графика
  void resetChartPosition() {
    if (kIsWeb) {
      _webChartKey.currentState?.resetChartPosition();
    } else {
      _mobileChartKey.currentState?.resetChartPosition();
    }
  }

  // Метод для сохранения состояния графика
  void saveChartState() {
    if (kIsWeb) {
      _webChartKey.currentState?.saveChartState();
    } else {
      _mobileChartKey.currentState?.saveChartState();
    }
  }

  // Метод для восстановления состояния графика
  void restoreChartState(ChartState chartState) {
    if (kIsWeb) {
      _webChartKey.currentState?.restoreChartState(chartState);
    } else {
      _mobileChartKey.currentState?.restoreChartState(chartState);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      // Для веб-платформы
      return WebImprovedTradingViewChart(
        key: _webChartKey,
        allCandles: widget.allCandles,
        onEntryPointSet: widget.onEntryPointSet,
        onTradeResult: widget.onTradeResult,
      );
    } else {
      // Для мобильных платформ
      return ImprovedTradingViewChart(
        key: _mobileChartKey,
        allCandles: widget.allCandles,
        onEntryPointSet: widget.onEntryPointSet,
        onTradeResult: widget.onTradeResult,
      );
    }
  }
}
