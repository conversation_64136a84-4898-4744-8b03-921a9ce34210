import 'package:flutter/material.dart';
import '../widgets/auth_decorative_pane.dart';
import '../widgets/modern_filled_button.dart';
// no SVG needed
// ignore: unused_import
import 'package:flutter_svg/flutter_svg.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF22242B),
      body: Center(
        child: Container(
          width: 720,
          constraints: const BoxConstraints(maxHeight: 650),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF2A2C32).withOpacity(0.95),
                const Color(0xFF23252B).withOpacity(0.9),
                const Color(0xFF1B1D22).withOpacity(0.85),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
            border: Border.all(
              color: Colors.white.withOpacity(0.15),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.4),
                blurRadius: 30,
                offset: const Offset(0, 15),
                spreadRadius: -5,
              ),
              BoxShadow(
                color: const Color(0xFF0F3460).withOpacity(0.3),
                blurRadius: 60,
                offset: const Offset(0, 30),
                spreadRadius: -10,
              ),
            ],
          ),
          child: Row(
            children: [
              // Decorative pane left (fixed width)
              const SizedBox(
                width: 300,
                child: AuthDecorativePane(),
              ),
              const SizedBox(width: 32),
              // Buttons & content right
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // PNG логотип без фона
                    SizedBox(
                      width: 200,
                      height: 200,
                      child: Image.asset(
                        'logo/TMM/TMM.png',
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) => const SizedBox.shrink(),
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'TMM',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 2.0,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'Ваш финансовый помощник',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 48),
                    ModernFilledButton(
                      text: 'Login',
                      onPressed: () {
                        Navigator.pushNamed(context, '/login');
                      },
                    ),
                    const SizedBox(height: 16),
                    ModernFilledButton(
                      text: 'Sign Up',
                      onPressed: () {
                        Navigator.pushNamed(context, '/register');
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

