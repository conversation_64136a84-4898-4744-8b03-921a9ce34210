import 'package:flutter/material.dart';

class NewsDropdownMenu extends StatefulWidget {
  final int selectedIndex;
  final ValueChanged<int> onCategorySelected;

  const NewsDropdownMenu({
    Key? key,
    required this.selectedIndex,
    required this.onCategorySelected,
  }) : super(key: key);

  @override
  State<NewsDropdownMenu> createState() => _NewsDropdownMenuState();
}

class _NewsDropdownMenuState extends State<NewsDropdownMenu> with SingleTickerProviderStateMixin {
  bool isMenuOpen = false;
  late AnimationController _controller;
  late Animation<double> _fade;
  late Animation<Offset> _slide;
  late Animation<double> _scale;

  final _categories = [
    _MenuCategory('All', Icons.public),
    _MenuCategory('Crypto', Icons.currency_bitcoin),
    _MenuCategory('Stock', Icons.trending_up),
    _MenuCategory('Whales', Icons.waves),
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fade = CurvedAnimation(parent: _controller, curve: Curves.easeOut);
    _slide = Tween<Offset>(begin: const Offset(0, -0.1), end: Offset.zero)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutBack));
    _scale = Tween<double>(begin: 0.95, end: 1.0)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutBack));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleMenu() {
    setState(() {
      isMenuOpen = !isMenuOpen;
      if (isMenuOpen) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  void _selectCategory(int index) {
    widget.onCategorySelected(index);
    _toggleMenu();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Кнопка-триггер
        Positioned(
          top: 24,
          right: 24,
          child: GestureDetector(
            onTap: _toggleMenu,
            child: AnimatedRotation(
              turns: isMenuOpen ? 0.25 : 0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: isMenuOpen
                    ? Icon(Icons.close, key: const ValueKey('close'), size: 32, color: Colors.white)
                    : Icon(Icons.menu, key: const ValueKey('menu'), size: 32, color: Colors.white),
              ),
            ),
          ),
        ),
        // Выпадающее меню
        if (isMenuOpen)
          Positioned.fill(
            child: GestureDetector(
              onTap: _toggleMenu,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
        if (isMenuOpen)
          Positioned(
            top: 70,
            right: 24,
            child: FadeTransition(
              opacity: _fade,
              child: SlideTransition(
                position: _slide,
                child: ScaleTransition(
                  scale: _scale,
                  child: Material(
                    color: Colors.transparent,
                    child: Container(
                      width: 200,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.92),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.25),
                            blurRadius: 18,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(_categories.length, (i) {
                          final isActive = widget.selectedIndex == i;
                          return InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: () => _selectCategory(i),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14),
                              decoration: BoxDecoration(
                                color: isActive ? Colors.white.withOpacity(0.08) : Colors.transparent,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    _categories[i].icon,
                                    color: isActive ? Colors.white : Colors.white70,
                                    size: 22,
                                  ),
                                  const SizedBox(width: 14),
                                  Text(
                                    _categories[i].label,
                                    style: TextStyle(
                                      color: isActive ? Colors.white : Colors.white70,
                                      fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class _MenuCategory {
  final String label;
  final IconData icon;
  const _MenuCategory(this.label, this.icon);
} 