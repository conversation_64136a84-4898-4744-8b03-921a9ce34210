import 'package:flutter/material.dart';

/// Класс с текстовыми стилями для приложения, имитирующими стиль шрифтов Apple
class AppTextStyles {
  static const String _primaryFontFamily = '.SF Pro Text';
  static const String _displayFontFamily = '.SF Pro Display';
  
  // Базовые цвета текста
  static const Color _primaryColor = Colors.white;
  static const Color _secondaryColor = Color(0xBBFFFFFF); // 73% белый
  static const Color _tertiaryColor = Color(0x99FFFFFF); // 60% белый
  static const Color _quaternaryColor = Color(0x66FFFFFF); // 40% белый
  
  // Цвет акцента (бледно-голубой в стиле северного сияния)
  static const Color _accentColor = Color(0xFF81D4FA);
  
  // Заголовок новости
  static TextStyle headline1({Color? color}) => TextStyle(
    fontFamily: _displayFontFamily,
    fontSize: 28,
    fontWeight: FontWeight.w700, // Bold
    letterSpacing: -0.4,
    height: 1.2,
    color: color ?? _primaryColor,
  );
  
  // Подзаголовок
  static TextStyle headline2({Color? color}) => TextStyle(
    fontFamily: _displayFontFamily,
    fontSize: 22,
    fontWeight: FontWeight.w600, // Semibold
    letterSpacing: -0.2,
    height: 1.3,
    color: color ?? _primaryColor,
  );
  
  // Заголовок раздела
  static TextStyle headline3({Color? color}) => TextStyle(
    fontFamily: _displayFontFamily,
    fontSize: 20,
    fontWeight: FontWeight.w600, // Semibold
    letterSpacing: 0.0,
    height: 1.3,
    color: color ?? _primaryColor,
  );
  
  // Подзаголовок раздела
  static TextStyle subtitle1({Color? color}) => TextStyle(
    fontFamily: _primaryFontFamily,
    fontSize: 17,
    fontWeight: FontWeight.w600, // Semibold
    letterSpacing: -0.2,
    height: 1.3,
    color: color ?? _secondaryColor,
  );
  
  // Основной текст
  static TextStyle bodyText1({Color? color}) => TextStyle(
    fontFamily: _primaryFontFamily,
    fontSize: 17,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: -0.3,
    height: 1.5,
    color: color ?? _secondaryColor,
  );
  
  // Дополнительный текст
  static TextStyle bodyText2({Color? color}) => TextStyle(
    fontFamily: _primaryFontFamily,
    fontSize: 15,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: -0.2,
    height: 1.4,
    color: color ?? _tertiaryColor,
  );
  
  // Метки и теги
  static TextStyle caption({Color? color}) => TextStyle(
    fontFamily: _primaryFontFamily,
    fontSize: 13,
    fontWeight: FontWeight.w500, // Medium
    letterSpacing: 0.2,
    height: 1.3,
    color: color ?? _quaternaryColor,
  );
  
  // Кнопки
  static TextStyle button({Color? color}) => TextStyle(
    fontFamily: _primaryFontFamily,
    fontSize: 15,
    fontWeight: FontWeight.w600, // Semibold
    letterSpacing: 0.4,
    height: 1.0,
    color: color ?? _accentColor,
  );
  
  // Метки времени и дат
  static TextStyle overline({Color? color}) => TextStyle(
    fontFamily: _primaryFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400, // Regular
    letterSpacing: 0.5,
    height: 1.0,
    color: color ?? _quaternaryColor,
  );
  
  // Получение SF шрифта с учетом платформы
  static String getSFFont(BuildContext context) {
    // На iOS будет использоваться системный SF Font
    // Для других платформ можно будет добавить пакеты SF Pro
    return '.SF Pro Text';
  }
}
