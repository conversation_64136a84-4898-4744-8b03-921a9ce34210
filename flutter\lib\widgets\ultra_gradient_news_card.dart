import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:finance_ai/models/news_item.dart';
import 'package:finance_ai/models/sentiment_types.dart';
import 'package:finance_ai/utils/sentiment_analyzer.dart';
import 'animated_upward_chart_indicator.dart';
import 'animated_downward_chart_indicator.dart';
import '../models/news_detail.dart';
import '../widgets/news_detail_modal.dart';
import '../utils/modal_utils.dart';
// 🚫 Убрали import 'news_freshness_indicator.dart' - больше не используется

enum CardSize {
  compact,  // 280x150
  standard, // 360x200
  large,    // 480x280
  tall      // 360x420
}

class CardDimensions {
  static const Map<CardSize, Size> sizes = {
    CardSize.compact: Size(360, 220),   // 1 колонка
    CardSize.standard: <PERSON>ze(540, 260),  // 1.5 колонки
    CardSize.large: <PERSON>ze(820, 320),     // 2.5–3 колонки
    CardSize.tall: <PERSON>ze(540, 400),      // 1.5 колонки, высокая
  };
  static const Map<CardSize, double> heights = {
    CardSize.compact: 220,
    CardSize.standard: 260,
    CardSize.large: 320,
    CardSize.tall: 400,
  };
  static const Map<CardSize, double> imageHeights = {
    CardSize.compact: 120,
    CardSize.standard: 160,
    CardSize.large: 220,
    CardSize.tall: 260,
  };
  static const double padding = 16.0;
  static const double spacing = 16.0;
}

class UltraGradientNewsCard extends StatefulWidget {
  final NewsItem newsItem;
  final CardSize size;
  final VoidCallback onTap;

  const UltraGradientNewsCard({
    Key? key,
    required this.newsItem,
    required this.size,
    required this.onTap,
  }) : super(key: key);

  @override
  State<UltraGradientNewsCard> createState() => _UltraGradientNewsCardState();
}

class _UltraGradientNewsCardState extends State<UltraGradientNewsCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final cardSize = CardDimensions.sizes[widget.size]!;
    final sentiment = widget.newsItem.sentiment;
    final isNeutral = sentiment == SentimentType.neutral;

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedScale(
          scale: _isHovered ? 1.025 : 1.0,
          duration: 300.ms,
          curve: Curves.easeOutExpo,
          child: Container(
            width: cardSize.width,
            height: cardSize.height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.18),
                  blurRadius: 32,
                  offset: const Offset(0, 12),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: Stack(
                children: [
                  // Apple glassmorphism: блюр + полупрозрачный белый слой
                  BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 24, sigmaY: 24),
                    child: Container(
                      decoration: BoxDecoration(
                        color: isNeutral
                            ? null
                            : _getSentimentColor(sentiment).withOpacity(0.15),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.13),
                          width: 1.5,
                        ),
                        gradient: isNeutral
                          ? LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                // Darker, near-black gradient for neutral cards
                                const Color(0xFF0A0A0D),
                                const Color(0xFF131316),
                                const Color(0xFF1A1B1E),
                              ],
                            )
                          : null,
                        // Fill color assigned above.
                      ),
                    ),
                  ),
                  // Градиентная подсветка по сентименту
                  if (!isNeutral)
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            _getSentimentColor(sentiment).withOpacity(0.15),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  // Контент
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTitle(),
                        const SizedBox(height: 16),
                        _buildDescription(),
                        const SizedBox(height: 8),
                        _buildFooter(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    final title = (widget.newsItem.aiGeneratedTitle?.isNotEmpty == true)
        ? widget.newsItem.aiGeneratedTitle!
        : (widget.newsItem.title?.isNotEmpty == true)
            ? widget.newsItem.title!
            : (widget.newsItem.description?.isNotEmpty == true)
                ? widget.newsItem.description!
                : 'Без заголовка';

    return Text(
      title,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: GoogleFonts.inter(
        color: Colors.white,
        fontSize: 18,
        fontWeight: FontWeight.bold,
        letterSpacing: -0.2,
      ),
    );
  }

  String _getFullDescription(NewsItem item, CardSize size) {
    if (item.summary?.isNotEmpty == true) return item.summary!.trim();
    if (item.rewrittenContent?.isNotEmpty == true) {
      final words = item.rewrittenContent!.split(' ');
      return words.take(60).join(' ') + (words.length > 60 ? '...' : '');
    }
    return item.description ?? 'Нет выжимки';
  }

  Widget _buildDescription() {
    final desc = _getFullDescription(widget.newsItem, widget.size);
    int maxLines = 2;
    if (widget.size == CardSize.large) maxLines = 8;
    if (widget.size == CardSize.tall) maxLines = 10;
    if (widget.size == CardSize.standard) maxLines = 4;

    if (desc.isEmpty) return const SizedBox.shrink();

    return Text(
      desc,
      maxLines: maxLines,
      overflow: TextOverflow.ellipsis,
      style: GoogleFonts.inter(
        color: Colors.white.withOpacity(0.85),
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  Widget _buildFooter() {
    // Используем cachedAt если доступно, иначе publishedAt
    final displayTime = widget.newsItem.cachedAt ?? widget.newsItem.publishedAt;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 🚫 Убрали NewsFreshnessIndicator для чистого интерфейса
        Text(
          widget.newsItem.source,
          style: TextStyle(
            color: Colors.white.withOpacity(0.6),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        buildFetchTime(displayTime),
      ],
    );
  }

  Color _getSentimentColor(SentimentType sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        // Extra muted green
        return const Color(0xFF3C8D64);
      case SentimentType.negative:
        // Extra muted red
        return const Color(0xFFC24E4E);
      default:
        return const Color(0xFF60a5fa);
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    if (difference.inDays > 0) {
      return '${difference.inDays}д назад';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}ч назад';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}м назад';
    } else {
      return 'Только что';
    }
  }

  Widget buildFetchTime(DateTime publishedAt) {
    final timeText = _formatTimeAgo(publishedAt);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.12),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.access_time_rounded, size: 16, color: Colors.white.withOpacity(0.7)),
          const SizedBox(width: 6),
          Text(
            timeText,
            style: TextStyle(
              color: Colors.white.withOpacity(0.85),
              fontSize: 14,
              fontWeight: FontWeight.w400,
              fontFamily: 'SF Pro Display',
              letterSpacing: -0.1,
            ),
          ),
        ],
      ),
    );
  }

  // 🕒 СОБСТВЕННАЯ ФУНКЦИЯ ФОРМАТИРОВАНИЯ ВРЕМЕНИ
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final diff = now.difference(dateTime);

    if (diff.inMinutes < 1) {
      return 'Just now';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}h ago';
    } else if (diff.inDays == 1) {
      return 'Yesterday';
    } else if (diff.inDays < 7) {
      return '${diff.inDays}d ago';
    } else {
      // Для старых новостей показываем дату
      return '${dateTime.day}.${dateTime.month.toString().padLeft(2, '0')}.${dateTime.year}';
    }
  }
}

// Новый премиальный виджет тега
class _AppleTag extends StatelessWidget {
  final String tag;
  const _AppleTag({required this.tag});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return AnimatedContainer(
      duration: 300.ms,
      curve: Curves.easeOutExpo,
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
      margin: const EdgeInsets.only(top: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        gradient: LinearGradient(
          colors: isDark
              ? [Color(0xFF23242a).withOpacity(0.7), Color(0xFF3a3b40).withOpacity(0.7)]
              : [Color(0xFFf5f6fa).withOpacity(0.7), Color(0xFFe3e4e8).withOpacity(0.7)],
        ),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black.withOpacity(0.10) : Colors.white.withOpacity(0.10),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: isDark ? Colors.white.withOpacity(0.10) : Colors.black.withOpacity(0.08),
          width: 1.2,
        ),
        // Apple glassmorphism
        backgroundBlendMode: BlendMode.overlay,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.tag, size: 16, color: isDark ? Colors.white70 : Colors.black54),
          const SizedBox(width: 4),
          Text(
            tag,
            style: GoogleFonts.inter(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white.withOpacity(0.92) : Colors.black.withOpacity(0.82),
              letterSpacing: -0.1,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 350.ms).slideY(begin: 0.2, end: 0);
  }
} 