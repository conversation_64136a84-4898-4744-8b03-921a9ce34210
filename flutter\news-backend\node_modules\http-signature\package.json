{"name": "http-signature", "description": "Reference implementation of Joyent's HTTP Signature scheme.", "version": "0.10.1", "license": "MIT", "author": "Joyent, Inc", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "git://github.com/joyent/node-http-signature.git"}, "homepage": "https://github.com/joyent/node-http-signature/", "bugs": "https://github.com/joyent/node-http-signature/issues", "keywords": ["https", "request"], "engines": {"node": ">=0.8"}, "main": "lib/index.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"assert-plus": "^0.1.5", "asn1": "0.1.11", "ctype": "0.5.3"}, "devDependencies": {"node-uuid": "^1.4.1", "tap": "0.4.2"}}