import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../models/trading_simulator_models.dart';
import '../widgets/gradient_background.dart';
import '../widgets/animated_button.dart';
import 'dart:math' as math;

class CustomModeSettingsScreen extends StatefulWidget {
  const CustomModeSettingsScreen({super.key});

  @override
  State<CustomModeSettingsScreen> createState() => _CustomModeSettingsScreenState();
}

class _CustomModeSettingsScreenState extends State<CustomModeSettingsScreen> {
  String _selectedSymbol = 'BTCUSDT';
  String _selectedTimeframe = '1h';
  double _leverage = 10.0;
  
  final List<Map<String, String>> _symbols = [
    {'symbol': 'RANDOM', 'name': 'Random Coin'},
    {'symbol': 'BTCUSDT', 'name': 'Bitcoin'},
    {'symbol': 'ETHUSDT', 'name': 'Ethereum'},
    {'symbol': 'BNBUSDT', 'name': 'Binance Coin'},
    {'symbol': 'SOLUSDT', 'name': 'Solana'},
    {'symbol': 'ADAUSDT', 'name': 'Cardano'},
  ];
  
  final List<Map<String, String>> _timeframes = [
    {'value': 'RANDOM', 'name': 'Random'},
    {'value': '5m', 'name': '5m'},
    {'value': '15m', 'name': '15m'},
    {'value': '30m', 'name': '30m'},
    {'value': '1h', 'name': '1h'},
    {'value': '4h', 'name': '4h'},
    {'value': '1d', 'name': '1d'},
  ];
  
  final List<double> _leverageOptions = [1, 2, 5, 10, 20, 50, 100, 250, 500, 1000];
  
  // Colors for coin icons
  final Map<String, Color> _coinColors = {
    'RANDOM': Colors.purpleAccent,
    'BTCUSDT': Colors.orange,
    'ETHUSDT': Colors.blue,
    'BNBUSDT': Colors.amber,
    'SOLUSDT': Colors.purple,
    'ADAUSDT': Colors.teal,
  };
  
  // List of top 25 Binance coins to use when Random is selected
  final List<String> _top25Coins = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'XRPUSDT', 
    'ADAUSDT', 'DOGEUSDT', 'TRXUSDT', 'LINKUSDT', 'MATICUSDT',
    'DOTUSDT', 'LTCUSDT', 'AVAXUSDT', 'UNIUSDT', 'ATOMUSDT',
    'ICPUSDT', 'ETCUSDT', 'FILUSDT', 'XLMUSDT', 'NEARUSDT',
    'ALGOUSDT', 'AAVEUSDT', 'AXSUSDT', 'SANDUSDT', 'MANAUSDT'
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(CupertinoIcons.back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Custom Mode',
          style: TextStyle(
            color: Colors.white,
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: GradientBackground(
        gradientColors: [
          Colors.black,
          const Color(0xFF191919),
          const Color(0xFF222222),
        ],
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                
                // Coin selection
                _buildSettingSectionTitle('Select Coin', CupertinoIcons.bitcoin_circle),
                const SizedBox(height: 6),
                _buildCoinSelector(),
                
                const SizedBox(height: 16),
                
                // Timeframe selection
                _buildSettingSectionTitle('Select Timeframe', CupertinoIcons.time),
                const SizedBox(height: 6),
                _buildTimeframeSelector(),
                
                const SizedBox(height: 16),
                
                // Leverage selection
                _buildSettingSectionTitle('Select Leverage', CupertinoIcons.chart_bar_alt_fill),
                const SizedBox(height: 6),
                _buildLeverageDisplay(),
                const SizedBox(height: 8),
                _buildLeverageSelector(),
                
                // Add slider for finer leverage adjustment
                const SizedBox(height: 12),
                _buildLeverageSlider(),
                
                const Spacer(),
                
                // Start Trading button
                AnimatedButton(
                  onTap: _startTrading,
                  child: Container(
                    height: 56,
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          _getCoinColor(_selectedSymbol),
                          _getCoinColor(_selectedSymbol).withOpacity(0.7),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: _getCoinColor(_selectedSymbol).withOpacity(0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.chart_bar_fill,
                          color: Colors.white,
                          size: 18,
                        ),
                        const SizedBox(width: 10),
                        const Text(
                          'Start Trading',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  // Section title with icon
  Widget _buildSettingSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.white70,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ],
    );
  }
  
  // Get color for specific coin
  Color _getCoinColor(String symbol) {
    return _coinColors[symbol] ?? Colors.blue;
  }
  
  // Coin selector with improved design
  Widget _buildCoinSelector() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: _symbols.map((coin) {
          final isSelected = coin['symbol'] == _selectedSymbol;
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedSymbol = coin['symbol']!;
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 150),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                color: isSelected 
                    ? _getCoinColor(coin['symbol']!).withOpacity(0.15) 
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: isSelected
                    ? Border.all(
                        color: _getCoinColor(coin['symbol']!),
                        width: 1,
                      )
                    : null,
              ),
              child: Row(
                children: [
                  Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? _getCoinColor(coin['symbol']!) 
                          : _getCoinColor(coin['symbol']!).withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        coin['symbol']!.substring(0, 1),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    coin['symbol']!.replaceAll('USDT', ''),
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected 
                          ? _getCoinColor(coin['symbol']!) 
                          : Colors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
  
  // Timeframe selector with improved design
  Widget _buildTimeframeSelector() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: _timeframes.map((timeframe) {
          final isSelected = timeframe['value'] == _selectedTimeframe;
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedTimeframe = timeframe['value']!;
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 150),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                color: isSelected 
                    ? const Color(0xFF4CAF50).withOpacity(0.15) 
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: isSelected
                    ? Border.all(
                        color: const Color(0xFF4CAF50),
                        width: 1,
                      )
                    : null,
              ),
              child: Text(
                timeframe['value']!,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected 
                      ? const Color(0xFF4CAF50) 
                      : Colors.white.withOpacity(0.8),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
  
  // Leverage display
  Widget _buildLeverageDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: _getLeverageColor(_leverage).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                '${_leverage.toInt()}x',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: _getLeverageColor(_leverage),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getLeverageColor(_leverage).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: _getLeverageColor(_leverage).withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Text(
                  _getLeverageDescription(_leverage),
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: _getLeverageColor(_leverage),
                  ),
                ),
              ),
            ],
          ),
          GestureDetector(
            onTap: () {
              _showLeverageInfo();
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.info_outline,
                size: 16,
                color: Colors.white.withOpacity(0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Leverage info popup
  void _showLeverageInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[850],
        title: const Text(
          'About Leverage',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Higher leverage multiplies both your potential profits and losses.',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '• 1-5x: Low risk, suitable for beginners',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
            Text(
              '• 10-20x: Medium risk, for experienced traders',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
            Text(
              '• 50-100x: High risk, for advanced traders only',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
  
  // Leverage selector with improved design
  Widget _buildLeverageSelector() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Column(
        children: [
          SizedBox(
            height: 36,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: _leverageOptions.map((leverage) {
                final isSelected = leverage == _leverage;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _leverage = leverage;
                    });
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 150),
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? _getLeverageColor(leverage)
                          : Colors.white.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(18),
                      border: Border.all(
                        color: isSelected 
                            ? Colors.white.withOpacity(0.1)
                            : _getLeverageColor(leverage).withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '${leverage.toInt()}x',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color: isSelected 
                              ? Colors.white 
                              : _getLeverageColor(leverage),
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
  
  // Leverage slider for finer adjustments
  Widget _buildLeverageSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: Text(
            'Fine-tune Leverage',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withOpacity(0.7),
            ),
          ),
        ),
        const SizedBox(height: 4),
        SliderTheme(
          data: SliderThemeData(
            trackHeight: 3,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
            trackShape: const RoundedRectSliderTrackShape(),
            thumbColor: _getLeverageColor(_leverage),
            overlayColor: _getLeverageColor(_leverage).withOpacity(0.2),
          ),
          child: Slider(
            value: _leverage,
            min: 1,
            max: 1000,
            divisions: 999,
            activeColor: _getLeverageColor(_leverage),
            inactiveColor: _getLeverageColor(_leverage).withOpacity(0.2),
            onChanged: (value) {
              setState(() {
                _leverage = value;
              });
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('1x', style: TextStyle(fontSize: 10, color: Colors.white.withOpacity(0.6))),
              Text('250x', style: TextStyle(fontSize: 10, color: Colors.white.withOpacity(0.6))),
              Text('500x', style: TextStyle(fontSize: 10, color: Colors.white.withOpacity(0.6))),
              Text('1000x', style: TextStyle(fontSize: 10, color: Colors.white.withOpacity(0.6))),
            ],
          ),
        ),
      ],
    );
  }
  
  // Get color based on leverage value
  Color _getLeverageColor(double leverage) {
    if (leverage <= 5) {
      return Colors.green;
    } else if (leverage <= 20) {
      return Colors.orange;
    } else if (leverage <= 100) {
      return Colors.deepOrange;
    } else if (leverage <= 500) {
      return Colors.redAccent;
    } else {
      return Colors.red;
    }
  }
  
  // Get description based on leverage value
  String _getLeverageDescription(double leverage) {
    if (leverage <= 5) {
      return 'Low risk';
    } else if (leverage <= 20) {
      return 'Medium risk';
    } else if (leverage <= 100) {
      return 'High risk';
    } else if (leverage <= 500) {
      return 'Very high risk';
    } else {
      return 'Extreme risk';
    }
  }
  
  // Start trading
  void _startTrading() {
    String symbol = _selectedSymbol;
    String timeframe = _selectedTimeframe;
    
    // If random coin is selected, choose one randomly
    if (_selectedSymbol == 'RANDOM') {
      final random = math.Random();
      symbol = _top25Coins[random.nextInt(_top25Coins.length)];
    }
    
    // If random timeframe is selected, choose one randomly
    if (_selectedTimeframe == 'RANDOM') {
      final random = math.Random();
      List<String> actualTimeframes = _timeframes
          .where((tf) => tf['value'] != 'RANDOM')
          .map((tf) => tf['value']!)
          .toList();
      timeframe = actualTimeframes[random.nextInt(actualTimeframes.length)];
    }
    
    Navigator.pushNamed(
      context,
      '/crypto_simulator',
      arguments: {
        'mode': SimulatorMode.custom,
        'leverage': _leverage,
        'symbol': symbol,
        'timeframe': timeframe,
        'initialBalance': 1000.0,
      },
    );
  }
}
