import 'package:flutter/material.dart';
import '../models/candle_data.dart';
import '../providers/trading_simulator_provider.dart';
import 'package:candlesticks/candlesticks.dart';

class Candlestick<PERSON>hart extends StatelessWidget {
  final List<CandleData> candles;
  final CandleData? entryCandle;
  final CandleData? resultCandle;
  final TradeAction action;

  const CandlestickChart({
    Key? key,
    required this.candles,
    this.entryCandle,
    this.resultCandle,
    required this.action,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Convert our CandleData to the format expected by the candlesticks package
    final candleData = candles.map((candle) {
      return Candle(
        date: DateTime.fromMillisecondsSinceEpoch((candle.time * 1000).toInt()),
        high: candle.high,
        low: candle.low,
        open: candle.open,
        close: candle.close,
        volume: candle.volume,
      );
    }).toList();

    return Container(
      padding: const EdgeInsets.all(8),
      child: Stack(
        children: [
          Candlesticks(
            candles: candleData,
            actions: [
              ToolBarAction(
                onPressed: () {},
                child: const Icon(Icons.show_chart, color: Colors.white),
              ),
            ],
          ),
          if (entryCandle != null) _buildEntryMarker(),
          if (resultCandle != null) _buildResultMarker(),
        ],
      ),
    );
  }

  Widget _buildEntryMarker() {
    // Отображаем маркер входа в стиле, соответствующем дизайну
    final color = Colors.white;

    return Positioned(
      bottom: 80,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey.shade900,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade800, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Entry',
              style: TextStyle(
                color: Colors.grey.shade400,
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${entryCandle!.close.toStringAsFixed(2)}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultMarker() {
    if (resultCandle == null || entryCandle == null) return const SizedBox();

    final isSuccess = (action == TradeAction.buy && resultCandle!.close > entryCandle!.close) ||
                     (action == TradeAction.sell && resultCandle!.close < entryCandle!.close);

    final color = isSuccess ? Colors.green : Colors.red;
    final resultText = isSuccess ? 'Correct!' : 'Wrong!';

    // Рассчитываем процентное изменение цены
    final priceChange = ((resultCandle!.close - entryCandle!.close) / entryCandle!.close * 100);
    final priceChangeText = priceChange >= 0
        ? '+${priceChange.toStringAsFixed(2)}%'
        : '${priceChange.toStringAsFixed(2)}%';

    return Positioned(
      bottom: 40,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSuccess ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              resultText,
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              priceChangeText,
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Метод для создания кнопок управления графиком удален, так как кнопки больше не используются
}
