import 'dart:convert';

/// Модель для хранения исторических данных о рыночном сентименте
class HistoricalEntry {
  final DateTime date;
  final double value;
  final Map<String, dynamic> metrics;

  HistoricalEntry(this.date, this.value, this.metrics);

  /// Преобразование в JSON для сохранения
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'value': value,
      'metrics': metrics,
    };
  }

  /// Создание из JSON
  factory HistoricalEntry.fromJson(Map<String, dynamic> json) {
    return HistoricalEntry(
      DateTime.parse(json['date']),
      json['value'],
      Map<String, dynamic>.from(json['metrics']),
    );
  }

  /// Создание копии с новыми значениями
  HistoricalEntry copyWith({
    DateTime? date,
    double? value,
    Map<String, dynamic>? metrics,
  }) {
    return HistoricalEntry(
      date ?? this.date,
      value ?? this.value,
      metrics ?? this.metrics,
    );
  }

  @override
  String toString() {
    return 'HistoricalEntry(date: $date, value: $value, metrics: ${jsonEncode(metrics)})';
  }
}
