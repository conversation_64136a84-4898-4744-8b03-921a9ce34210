enum MaterialLevel {
  beginner,
  intermediate,
  advanced,
}

class MaterialItem {
  final String id;
  final String title;
  final String description;
  final String content;
  final MaterialLevel level;
  final List<String> tags;
  final bool isSaved;

  MaterialItem({
    required this.id,
    required this.title,
    required this.description,
    required this.content,
    required this.level,
    required this.tags,
    this.isSaved = false,
  });

  // Mock data factory
  static List<MaterialItem> getMockItems() {
    return [
      MaterialItem(
        id: '1',
        title: 'Understanding Cryptocurrency Market Cycles',
        description: 'Learn about the different market cycles in cryptocurrency and how to identify them.',
        content: 'Detailed content about cryptocurrency market cycles...',
        level: MaterialLevel.beginner,
        tags: ['Market Analysis', 'Trading'],
      ),
      MaterialItem(
        id: '2',
        title: 'Technical Analysis Fundamentals',
        description: 'Master the basics of technical analysis for cryptocurrency trading.',
        content: 'Comprehensive guide to technical analysis...',
        level: MaterialLevel.beginner,
        tags: ['Technical Analysis', 'Trading', 'Charts'],
      ),
      MaterialItem(
        id: '3',
        title: 'DeFi Yield Farming Strategies',
        description: 'Explore different yield farming strategies in decentralized finance.',
        content: 'In-depth look at yield farming in DeFi...',
        level: MaterialLevel.intermediate,
        tags: ['DeFi', 'Yield Farming', 'Ethereum'],
      ),
      MaterialItem(
        id: '4',
        title: 'NFT Creation and Monetization',
        description: 'Learn how to create, mint, and monetize your own NFTs.',
        content: 'Step-by-step guide to NFT creation and monetization...',
        level: MaterialLevel.intermediate,
        tags: ['NFT', 'Digital Art', 'Monetization'],
      ),
      MaterialItem(
        id: '5',
        title: 'Advanced Smart Contract Development',
        description: 'Deep dive into smart contract development with Solidity.',
        content: 'Advanced techniques for smart contract development...',
        level: MaterialLevel.advanced,
        tags: ['Smart Contracts', 'Solidity', 'Development'],
      ),
      MaterialItem(
        id: '6',
        title: 'Crypto Tax Compliance Guide',
        description: 'Navigate the complex world of cryptocurrency taxation and compliance.',
        content: 'Comprehensive guide to cryptocurrency taxation...',
        level: MaterialLevel.beginner,
        tags: ['Taxes', 'Compliance', 'Regulation'],
      ),
      MaterialItem(
        id: '7',
        title: 'Layer 2 Scaling Solutions Explained',
        description: 'Understand different Layer 2 scaling solutions for blockchain networks.',
        content: 'Detailed explanation of Layer 2 scaling solutions...',
        level: MaterialLevel.advanced,
        tags: ['Scaling', 'Layer 2', 'Ethereum'],
      ),
      MaterialItem(
        id: '8',
        title: 'Crypto Security Best Practices',
        description: 'Essential security practices for protecting your cryptocurrency investments.',
        content: 'Comprehensive security guide for cryptocurrency holders...',
        level: MaterialLevel.beginner,
        tags: ['Security', 'Wallets', 'Protection'],
        isSaved: true,
      ),
    ];
  }
}
