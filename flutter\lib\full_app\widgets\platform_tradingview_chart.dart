import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../models/candle.dart';

// Условный импорт для веб-платформы
import 'tradingview_chart.dart';
// Для веб-платформы используем отдельную реализацию
import 'web_tradingview_chart.dart' if (dart.library.io) 'tradingview_chart.dart';

/// Фабрика для создания правильной реализации TradingView в зависимости от платформы
class PlatformTradingViewChart extends StatefulWidget {
  final List<Candle> initialCandles;
  final List<Candle> futureCandles;
  final bool showFutureCandles;
  final Function(double price, int time)? onEntryPointSet;
  final Function(bool isUp, double percentChange, double finalPrice)? onTradeResult;
  final GlobalKey? chartKey;

  const PlatformTradingViewChart({
    super.key,
    required this.initialCandles,
    this.futureCandles = const [],
    this.showFutureCandles = false,
    this.onEntryPointSet,
    this.onTradeResult,
    this.chartKey,
  });

  @override
  State<PlatformTradingViewChart> createState() => _PlatformTradingViewChartState();
}

class _PlatformTradingViewChartState extends State<PlatformTradingViewChart> {
  // Ключи для доступа к методам виджетов
  final GlobalKey<WebTradingViewChartState> _webChartKey = GlobalKey<WebTradingViewChartState>();
  final GlobalKey<TradingViewChartState> _mobileChartKey = GlobalKey<TradingViewChartState>();

  // Публичный метод для отображения всех свечей
  void showAllCandles() {
    if (kIsWeb) {
      _webChartKey.currentState?.showAllCandles();
    } else {
      _mobileChartKey.currentState?.showAllCandles();
    }
  }

  // Статический метод для доступа к методу showAllCandles через GlobalKey
  static void showAllCandlesStatic(GlobalKey key) {
    final state = key.currentState as _PlatformTradingViewChartState?;
    state?.showAllCandles();
  }

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      // Для веб-платформы
      return WebTradingViewChart(
        key: _webChartKey,
        initialCandles: widget.initialCandles,
        futureCandles: widget.futureCandles,
        showFutureCandles: widget.showFutureCandles,
        onEntryPointSet: widget.onEntryPointSet,
        onTradeResult: widget.onTradeResult,
      );
    } else {
      // Для мобильных платформ
      return TradingViewChart(
        key: _mobileChartKey,
        initialCandles: widget.initialCandles,
        futureCandles: widget.futureCandles,
        showFutureCandles: widget.showFutureCandles,
        onEntryPointSet: widget.onEntryPointSet,
        onTradeResult: widget.onTradeResult,
      );
    }
  }
}
