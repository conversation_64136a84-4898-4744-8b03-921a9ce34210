/// Перечисление для представления таймфреймов
enum TimeFrame {
  min30('30m'),
  hour1('1h'),
  hour2('2h'),
  hour4('4h'),
  hour6('6h'),
  hour8('8h'),
  hour12('12h'),
  day1('1d'),
  week1('1w');

  final String apiValue;
  
  const TimeFrame(this.apiValue);
  
  /// Получение TimeFrame из строкового значения
  static TimeFrame fromString(String value) {
    return TimeFrame.values.firstWhere(
      (tf) => tf.apiValue == value,
      orElse: () => TimeFrame.hour1,
    );
  }
}
