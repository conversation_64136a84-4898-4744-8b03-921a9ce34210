import '../models/news_item.dart';

List<NewsItem> filterAndEnrichNews(List<NewsItem> newsList) {
  final Set<String> seenTitles = {};
  final Set<String> seenDescriptions = {};
  final List<String> stopWords = [
    'реклама', 'sponsored', 'buy now', 'ad', 'breaking', '...', 'test', 'promo'
  ];
  final List<String> assetKeywords = [
    'BTC', 'ETH', 'SOL', 'QNT', 'TAO', 'HYPE', 'USDT', 'USDC', 'BNB', 'DOGE'
  ];

  return newsList.where((item) {
    final title = (item.title ?? '').trim();
    final desc = (item.description ?? '').trim();

    // 1. Минимальная длина
    if (title.length < 30 || desc.length < 40) return false;

    // 2. Дубли
    if (seenTitles.contains(title) || seenDescriptions.contains(desc)) return false;
    seenTitles.add(title);
    seenDescriptions.add(desc);

    // 3. Стоп-слова
    final lower = (title + desc).toLowerCase();
    if (stopWords.any((w) => lower.contains(w))) return false;

    // 4. Мусорные новости
    if (title == desc) return false;
    if (desc.isEmpty) return false;
    if (desc.replaceAll(RegExp(r'[^\w]'), '').isEmpty) return false;
    if (title == title.toUpperCase()) return false;

    // 5. Теги
    if (item.tags.isEmpty) {
      final foundAssets = assetKeywords.where((a) => title.contains(a) || desc.contains(a)).toList();
      if (foundAssets.isEmpty) return false;
      item.tags.addAll(foundAssets);
    }

    return true;
  }).toList();
} 