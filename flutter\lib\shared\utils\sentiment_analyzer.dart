import 'package:finance_ai/models/sentiment_types.dart';
import 'package:finance_ai/models/sentiment_data.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class SentimentAnalyzer {
  // Ключевые слова для анализа тональности (оставлены как запасной вариант)
  static const List<String> _positiveKeywords = [
    'bull', 'bullish', 'up', 'rise', 'gain', 'profit', 'moon', 'pump', 
    'surge', 'rally', 'breakout', 'green', 'high', 'increase', 'growth',
    'boom', 'rocket', 'soar', 'climb', 'jump', 'explode', 'breakthrough'
  ];

  static const List<String> _negativeKeywords = [
    'bear', 'bearish', 'down', 'fall', 'drop', 'crash', 'dump', 'plunge',
    'correction', 'sell-off', 'red', 'low', 'decline', 'loss', 'fear',
    'panic', 'collapse', 'sink', 'tumble', 'slide', 'dip', 'bleeding'
  ];

  static const List<String> _highImpactKeywords = [
    'bitcoin', 'btc', 'ethereum', 'eth', 'major', 'breaking', 'alert',
    'urgent', 'massive', 'huge', 'giant', 'record', 'historic', 'unprecedented'
  ];

  /// Анализирует тональность новости с помощью ИИ-агента (DeepSeek)
  static Future<SentimentData> analyzeSentiment(String title, String description) async {
    try {
      // Формируем текст для анализа (заголовок + описание)
      final text = '$title $description';

      // Вызываем API ИИ-сервиса (DeepSeek)
      final response = await http.post(
        Uri.parse('https://api.deepseek.com/v1/analyze'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${const String.fromEnvironment('DEEPSEEK_API_KEY')}',
        },
        body: jsonEncode({
          'text': text,
          'task': 'sentiment_analysis',
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final sentiment = data['sentiment'] as String;
        final score = data['score'] as double;

        // Преобразуем ответ API в SentimentData
        switch (sentiment.toLowerCase()) {
          case 'positive':
            return SentimentData.positive(impact: score > 0.7 ? NewsImpact.high : NewsImpact.medium);
          case 'negative':
            return SentimentData.negative(impact: score > 0.7 ? NewsImpact.high : NewsImpact.medium);
          default:
            return SentimentData.neutral();
        }
      } else {
        throw Exception('API Error: ${response.statusCode}');
      }
    } catch (e) {
      // В случае ошибки используем запасной вариант (ключевые слова)
      return _fallbackAnalyzeSentiment(title, description);
    }
  }

  /// Запасной метод анализа на основе ключевых слов
  static SentimentData _fallbackAnalyzeSentiment(String title, String description) {
    final text = (title + ' ' + description).toLowerCase();
    
    int positiveCount = _positiveKeywords.where((word) => text.contains(word)).length;
    int negativeCount = _negativeKeywords.where((word) => text.contains(word)).length;

    if (positiveCount > negativeCount) {
      final impact = positiveCount > negativeCount * 2 ? NewsImpact.high : NewsImpact.medium;
      return SentimentData.positive(impact: impact);
    } else if (negativeCount > positiveCount) {
      final impact = negativeCount > positiveCount * 2 ? NewsImpact.high : NewsImpact.medium;
      return SentimentData.negative(impact: impact);
    } else {
      return SentimentData.neutral();
    }
  }

  /// Анализ только по заголовку (для быстрого анализа)
  static SentimentData quickAnalyze(String title) {
    // Упрощенный анализ только по заголовку
    final text = title.toLowerCase();
    
    if (text.contains('growth') || text.contains('profit') || text.contains('gain')) {
      return SentimentData.positive();
    } else if (text.contains('loss') || text.contains('decline') || text.contains('drop')) {
      return SentimentData.negative();
    }
    
    return SentimentData.neutral();
  }

  /// Получение цвета для индикатора тональности
  static int getSentimentColor(SentimentType sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return 0xFF10B981; // Зеленый
      case SentimentType.negative:
        return 0xFFEF4444; // Красный
      case SentimentType.neutral:
        return 0xFF3B82F6; // Синий
    }
  }
} 