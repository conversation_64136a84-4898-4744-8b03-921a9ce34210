import 'package:flutter/material.dart';
import 'cosmic_background.dart';
import 'dart:math' as math;

/// A reusable decorative pane used on authentication screens.
/// It renders a subtle animated cosmic background that occupies
/// one side of the split-screen card.
class AuthDecorativePane extends StatefulWidget {
  const AuthDecorativePane({Key? key}) : super(key: key);

  @override
  State<AuthDecorativePane> createState() => _AuthDecorativePaneState();
}

class _AuthDecorativePaneState extends State<AuthDecorativePane>
    with SingleTickerProviderStateMixin {
  late final AnimationController _rotation;

  @override
  void initState() {
    super.initState();
    _rotation = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 120),
    )..repeat();
  }

  @override
  void dispose() {
    _rotation.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(24),
      child: AnimatedBuilder(
        animation: _rotation,
        builder: (context, child) => Transform.rotate(
          angle: _rotation.value * 2 * math.pi,
          child: Transform.scale(scale: 3.5, child: child),
        ),
        child: const CosmicBackground(
          starCount: 1000,
          minStarSize: 0.15,
          maxStarSize: 0.8,
          animationDuration: Duration(milliseconds: 14000),
          backgroundColor: Color(0xFF1B1C22),
          enableComet: true,
          enableParallax: true,
          parallaxIntensity: 25,
          enableAsteroids: false,
          enableSatellites: false,
        ),
      ),
    );
  }
} 