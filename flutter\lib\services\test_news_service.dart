import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/news_item.dart';
import '../models/sentiment_types.dart';

class TestNewsService {
  static const String _newsApiKey = 'YOUR_NEWS_API_KEY'; // Замените на ваш ключ
  static const String _cryptoCompareApiKey = 'YOUR_CRYPTOCOMPARE_API_KEY'; // Замените на ваш ключ
  
  // Получение новостей из NewsAPI
  static Future<List<NewsItem>> getNewsFromNewsAPI({
    String? category,
    String? query,
    int pageSize = 10,
  }) async {
    final url = Uri.parse('https://newsapi.org/v2/top-headlines?' +
        'country=us&' +
        'apiKey=$_newsApiKey&' +
        'pageSize=$pageSize' +
        (category != null ? '&category=$category' : '') +
        (query != null ? '&q=$query' : ''));

    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['articles'] as List).map((article) {
          return NewsItem(
            id: article['url'] ?? DateTime.now().toString(),
            title: article['title'] ?? '',
            description: article['description'] ?? '',
            imageUrl: article['urlToImage'] ?? '',
            publishedAt: DateTime.parse(article['publishedAt'] ?? DateTime.now().toString()),
            source: article['source']['name'] ?? '',
            url: article['url'] ?? '',
            sentiment: _determineSentiment(article['title'] ?? ''),
            tags: _extractTags(article['title'] ?? ''),
            category: _determineCategory(article['title'] ?? ''),
            importanceLevel: 2,
            content: article['content'] ?? '',
          );
        }).toList();
      }
      throw Exception('Failed to load news from NewsAPI');
    } catch (e) {
      throw Exception('Error fetching news from NewsAPI: $e');
    }
  }

  // Получение криптовалютных новостей из CryptoCompare
  static Future<List<NewsItem>> getCryptoNews({
    int limit = 10,
  }) async {
    final url = Uri.parse('https://min-api.cryptocompare.com/data/v2/news/?lang=EN');

    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['Data'] as List).take(limit).map((article) {
          return NewsItem(
            id: article['id']?.toString() ?? DateTime.now().toString(),
            title: article['title'] ?? '',
            description: article['body'] ?? '',
            imageUrl: article['imageurl'] ?? '',
            publishedAt: DateTime.fromMillisecondsSinceEpoch(article['published_on'] * 1000),
            source: article['source'] ?? '',
            url: article['url'] ?? '',
            sentiment: _determineSentiment(article['title'] ?? ''),
            tags: _extractTags(article['title'] ?? ''),
            category: NewsCategory.crypto,
            importanceLevel: 2,
            content: article['body'] ?? '',
          );
        }).toList();
      }
      throw Exception('Failed to load crypto news');
    } catch (e) {
      throw Exception('Error fetching crypto news: $e');
    }
  }

  // Получение новостей из CryptoPanic
  static Future<List<NewsItem>> getCryptoPanicNews({
    int limit = 10,
  }) async {
    final url = Uri.parse('https://cryptopanic.com/api/v1/posts/?auth_token=YOUR_CRYPTOPANIC_TOKEN&public=true');

    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['results'] as List).take(limit).map((article) {
          return NewsItem(
            id: article['id']?.toString() ?? DateTime.now().toString(),
            title: article['title'] ?? '',
            description: article['metadata']['description'] ?? '',
            imageUrl: article['metadata']['image']?['url'] ?? '',
            publishedAt: DateTime.parse(article['published_at'] ?? DateTime.now().toString()),
            source: article['source']['title'] ?? '',
            url: article['url'] ?? '',
            sentiment: _determineSentiment(article['title'] ?? ''),
            tags: _extractTags(article['title'] ?? ''),
            category: NewsCategory.crypto,
            importanceLevel: 2,
            content: article['metadata']['description'] ?? '',
          );
        }).toList();
      }
      throw Exception('Failed to load news from CryptoPanic');
    } catch (e) {
      throw Exception('Error fetching news from CryptoPanic: $e');
    }
  }

  // Комбинирование новостей из разных источников
  static Future<List<NewsItem>> getCombinedNews({
    int count = 20,
    NewsCategory? category,
    SentimentType? sentiment,
  }) async {
    try {
      final List<NewsItem> allNews = [];
      
      // Получаем новости из разных источников
      if (category == null || category == NewsCategory.crypto) {
        allNews.addAll(await getCryptoNews(limit: count ~/ 2));
        allNews.addAll(await getCryptoPanicNews(limit: count ~/ 2));
      }
      
      if (category == null || category != NewsCategory.crypto) {
        allNews.addAll(await getNewsFromNewsAPI(
          category: _mapCategoryToNewsAPI(category),
          pageSize: count,
        ));
      }

      // Сортируем по дате публикации
      allNews.sort((a, b) => b.publishedAt.compareTo(a.publishedAt));

      // Фильтруем по сентименту, если указан
      if (sentiment != null) {
        return allNews.where((news) => news.sentiment == sentiment).toList();
      }

      return allNews;
    } catch (e) {
      throw Exception('Error fetching combined news: $e');
    }
  }

  // Вспомогательные методы
  static SentimentType _determineSentiment(String text) {
    final lowerText = text.toLowerCase();
    if (lowerText.contains('surge') || lowerText.contains('rise') || lowerText.contains('gain')) {
      return SentimentType.positive;
    } else if (lowerText.contains('drop') || lowerText.contains('fall') || lowerText.contains('crash')) {
      return SentimentType.negative;
    }
    return SentimentType.neutral;
  }

  static List<String> _extractTags(String text) {
    final List<String> tags = [];
    final lowerText = text.toLowerCase();
    
    if (lowerText.contains('bitcoin') || lowerText.contains('btc')) tags.add('BTC');
    if (lowerText.contains('ethereum') || lowerText.contains('eth')) tags.add('ETH');
    if (lowerText.contains('crypto')) tags.add('Crypto');
    if (lowerText.contains('blockchain')) tags.add('Blockchain');
    if (lowerText.contains('defi')) tags.add('DeFi');
    if (lowerText.contains('nft')) tags.add('NFT');
    
    return tags;
  }

  static NewsCategory _determineCategory(String text) {
    final lowerText = text.toLowerCase();
    if (lowerText.contains('bitcoin') || lowerText.contains('crypto')) {
      return NewsCategory.crypto;
    } else if (lowerText.contains('stock') || lowerText.contains('market')) {
      return NewsCategory.stocks;
    } else if (lowerText.contains('ai') || lowerText.contains('artificial intelligence')) {
      return NewsCategory.ai;
    } else if (lowerText.contains('regulation') || lowerText.contains('policy')) {
      return NewsCategory.politics;
    }
    return NewsCategory.all;
  }

  static String? _mapCategoryToNewsAPI(NewsCategory? category) {
    switch (category) {
      case NewsCategory.crypto:
        return 'technology';
      case NewsCategory.stocks:
        return 'business';
      case NewsCategory.politics:
        return 'general';
      default:
        return null;
    }
  }
} 