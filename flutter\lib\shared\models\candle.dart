import 'package:flutter/material.dart';

/// Data model for a candle in the trading simulator
class Candle {
  final double open;
  final double high;
  final double low;
  final double close;
  final DateTime timestamp;

  Candle({
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.timestamp,
  });

  /// Create a Candle from Binance API data
  factory Candle.fromList(List<dynamic> data) {
    return Candle(
      open: double.parse(data[1]),
      high: double.parse(data[2]),
      low: double.parse(data[3]),
      close: double.parse(data[4]),
      timestamp: DateTime.fromMillisecondsSinceEpoch(data[0]),
    );
  }

  /// Check if candle is green (close >= open)
  bool get isGreen => close >= open;
}
