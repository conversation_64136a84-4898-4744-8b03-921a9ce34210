import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/crypto_currency.dart';
import '../providers/crypto_provider.dart';
import '../widgets/app_bottom_navigation.dart';
import 'crypto_detail_screen.dart';
import 'dart:math' as math;
import '../services/local_crypto_icons_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class CryptoListScreen extends StatefulWidget {
  const CryptoListScreen({super.key});

  @override
  State<CryptoListScreen> createState() => _CryptoListScreenState();
}

class _CryptoListScreenState extends State<CryptoListScreen> {
  @override
  void initState() {
    super.initState();
    // Загружаем данные о криптовалютах при инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
      if (cryptoProvider.allCryptos.isEmpty && !cryptoProvider.isLoading) {
        cryptoProvider.loadAllCryptos();
      }
    });
  }

  // Метод для генерации истории цен, если она отсутствует
  List<PricePoint> _generateDefaultPriceHistory(double basePrice) {
    final List<PricePoint> pricePoints = [];
    final now = DateTime.now();
    final random = math.Random();

    // Генерируем 24 точки для часового графика
    for (int i = 0; i < 24; i++) {
      final time = now.subtract(Duration(hours: 24 - i));
      // Добавляем случайные колебания к базовой цене
      final randomFactor = 0.95 + (i / 24.0) * 0.1 + (random.nextDouble() - 0.5) * 0.02;
      final price = basePrice * randomFactor;
      pricePoints.add(PricePoint(time, price));
    }

    return pricePoints;
  }

  // Метод для открытия детальной страницы криптовалюты
  void _openCryptoDetailScreen(BuildContext context, CryptoCurrency crypto) {
    // Проверка на валидность криптовалюты
    if (crypto.symbol.isEmpty) {
      debugPrint('Invalid cryptocurrency: empty symbol');
      return;
    }

    // Создаем копию криптовалюты с гарантированными данными
    final safeCrypto = CryptoCurrency(
      id: crypto.id.isNotEmpty ? crypto.id : 'unknown',
      name: crypto.name.isNotEmpty ? crypto.name : 'Unknown',
      symbol: crypto.symbol.isNotEmpty ? crypto.symbol : 'XXX',
      price: crypto.price,
      priceChangePercentage24h: crypto.priceChangePercentage24h,
      marketCap: crypto.marketCap,
      volume24h: crypto.volume24h,
      imageUrl: crypto.imageUrl,
      isFavorite: crypto.isFavorite,
      priceHistory: crypto.priceHistory.isNotEmpty ? crypto.priceHistory : _generateDefaultPriceHistory(crypto.price),
    );

    // Переходим на детальную страницу
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CryptoDetailScreen(crypto: safeCrypto),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CryptoProvider>(
      builder: (context, cryptoProvider, _) {
        return Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            title: const Text('Cryptocurrencies'),
            backgroundColor: Colors.black,
            elevation: 0,
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh, color: Colors.white),
                onPressed: () {
                  cryptoProvider.loadAllCryptos();
                },
              ),
            ],
          ),
          body: cryptoProvider.isLoading && cryptoProvider.allCryptos.isEmpty
              ? const Center(child: CircularProgressIndicator())
              : _buildCryptoList(context, cryptoProvider),
          bottomNavigationBar: AppBottomNavigation(
            currentIndex: 1,
            onTap: (index) {
              if (index != 1) {
                switch (index) {
                  case 0:
                    Navigator.pushReplacementNamed(context, '/news');
                    break;
                  case 2:
                    Navigator.pushReplacementNamed(context, '/courses');
                    break;
                  case 3:
                    Navigator.pushReplacementNamed(context, '/saved_analyses');
                    break;
                  case 4:
                    Navigator.pushReplacementNamed(context, '/profile');
                    break;
                }
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildCryptoList(BuildContext context, CryptoProvider cryptoProvider) {
    final cryptos = cryptoProvider.allCryptos;

    if (cryptos.isEmpty) {
      return const Center(
        child: Text(
          'No cryptocurrency data available',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: cryptos.length,
      itemBuilder: (context, index) {
        final crypto = cryptos[index];
        return _buildCryptoListItem(context, crypto);
      },
    );
  }

  Widget _buildCryptoListItem(BuildContext context, CryptoCurrency crypto) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeSign = isPositive ? '+' : '';

    return Card(
      color: Colors.grey[900],
      margin: const EdgeInsets.only(bottom: 8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: InkWell(
        onTap: () => _openCryptoDetailScreen(context, crypto),
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Иконка криптовалюты
              _buildCryptoLogo(crypto),
              const SizedBox(width: 16),

              // Название и символ
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      crypto.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      crypto.symbol,
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),

              // Цена и изменение
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '\$${_formatPrice(crypto.price)}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                    style: TextStyle(
                      color: changeColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(2);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  // Метод для отображения логотипа криптовалюты
  Widget _buildCryptoLogo(CryptoCurrency crypto) {
    // Проверяем, есть ли URL логотипа
    if (crypto.imageUrl.isNotEmpty && !crypto.imageUrl.contains('placeholder')) {
      return ClipOval(
        child: SizedBox(
          width: 40,
          height: 40,
          child: CachedNetworkImage(
            imageUrl: crypto.imageUrl,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey[800],
                shape: BoxShape.circle,
              ),
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ),
            ),
            errorWidget: (context, url, error) {
              // В случае ошибки загрузки изображения сначала пробуем использовать локальную иконку
              final upperSymbol = crypto.symbol.trim().toUpperCase();
              if (LocalCryptoIconsService.availableIcons.contains(upperSymbol)) {
                return LocalCryptoIconsService.getIcon(
                  crypto.symbol,
                  size: 40,
                  borderRadius: BorderRadius.circular(20),
                );
              } else {
                // Если локальной иконки нет, показываем первые буквы символа
                return _buildFallbackLogo(crypto);
              }
            },
            // Включаем кэширование изображений
            cacheManager: DefaultCacheManager(),
            // Устанавливаем максимальный возраст кэша (7 дней)
            maxWidthDiskCache: 200,
            maxHeightDiskCache: 200,
            memCacheWidth: 100,
            memCacheHeight: 100,
          ),
        ),
      );
    } else {
      // Если URL логотипа отсутствует, сначала пробуем использовать локальную иконку
      final upperSymbol = crypto.symbol.trim().toUpperCase();
      if (LocalCryptoIconsService.availableIcons.contains(upperSymbol)) {
        return ClipOval(
          child: LocalCryptoIconsService.getIcon(
            crypto.symbol,
            size: 40,
            borderRadius: BorderRadius.circular(20),
          ),
        );
      } else {
        // Если локальной иконки нет, показываем первые буквы символа
        return _buildFallbackLogo(crypto);
      }
    }
  }

  // Вспомогательный метод для отображения заглушки логотипа
  Widget _buildFallbackLogo(CryptoCurrency crypto) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(20),
      ),
      alignment: Alignment.center,
      child: Text(
        crypto.symbol.substring(0, math.min(2, crypto.symbol.length)),
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
