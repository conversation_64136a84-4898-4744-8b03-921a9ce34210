import 'package:flutter/material.dart';

/// Пользовательский класс для анимированных переходов между экранами
class CustomPageTransitions {
  /// Создает анимированный переход с эффектом масштабирования и затухания
  static PageRouteBuilder buildScaleTransition({
    required Widget page,
    required RouteSettings settings,
  }) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        var curve = Curves.easeInOut;
        var curveTween = CurveTween(curve: curve);
        
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(animation.drive(curveTween));
        
        var scaleAnimation = Tween<double>(
          begin: 0.95,
          end: 1.0,
        ).animate(animation.drive(curveTween));
        
        return FadeTransition(
          opacity: fadeAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
    );
  }
  
  /// Создает анимированный переход с эффектом слайда
  static PageRouteBuilder buildSlideTransition({
    required Widget page,
    required RouteSettings settings,
  }) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        var curve = Curves.easeInOut;
        var curveTween = CurveTween(curve: curve);
        
        var slideAnimation = Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).animate(animation.drive(curveTween));
        
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(animation.drive(curveTween));
        
        return SlideTransition(
          position: slideAnimation,
          child: FadeTransition(
            opacity: fadeAnimation,
            child: child,
          ),
        );
      },
    );
  }

  /// Создает специальную анимацию для CoinDetailScreen с красивыми эффектами
  static PageRouteBuilder buildCoinDetailTransition({
    required Widget page,
    required RouteSettings settings,
  }) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 600), // Уменьшил длительность для более быстрого отклика
      reverseTransitionDuration: const Duration(milliseconds: 500),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Создаем более плавные анимации с улучшенными кривыми
        
        // Основная анимация входа - только плавный слайд без резких движений
        final slideAnimation = Tween<Offset>(
          begin: const Offset(0.0, 0.1), // Уменьшил смещение для более тонкого эффекта
          end: Offset.zero,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: const Interval(0.0, 1.0, curve: Curves.easeOutQuart), // Более плавная кривая
          ),
        );

        // Анимация затухания - очень быстрая и плавная
        final fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: const Interval(0.0, 0.5, curve: Curves.easeOut), // Быстрое появление
          ),
        );

        // Очень тонкая анимация масштабирования для глубины
        final scaleAnimation = Tween<double>(
          begin: 0.98, // Минимальное изменение масштаба
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
          ),
        );

        // Убираем анимацию поворота - она может вызывать дёргания
        
        // Анимация размытия фона (более тонкая)
        final blurAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: const Interval(0.0, 0.4, curve: Curves.easeOut), // Быстрее
          ),
        );

        return Transform.scale(
          scale: scaleAnimation.value,
          child: SlideTransition(
            position: slideAnimation,
            child: FadeTransition(
              opacity: fadeAnimation,
              child: Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(
                        0.2 * blurAnimation.value, // Уменьшил интенсивность тени
                      ),
                      blurRadius: 15 * blurAnimation.value, // Меньше размытие
                      spreadRadius: 2 * blurAnimation.value, // Меньше распространение
                    ),
                  ],
                ),
                child: child!,
              ),
            ),
          ),
        );
      },
    );
  }

  /// Создает анимацию morphing для элементов (используется с Hero)
  static PageRouteBuilder buildMorphingTransition({
    required Widget page,
    required RouteSettings settings,
  }) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 900),
      reverseTransitionDuration: const Duration(milliseconds: 700),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Создаем сложную морфинг анимацию
        
        final primaryAnimation = CurvedAnimation(
          parent: animation,
          curve: Curves.fastLinearToSlowEaseIn,
        );

        final secondaryTransition = CurvedAnimation(
          parent: secondaryAnimation,
          curve: Curves.fastLinearToSlowEaseIn,
        );

        return AnimatedBuilder(
          animation: primaryAnimation,
          builder: (context, child) {
            final progress = primaryAnimation.value;
            
            // Создаем эффект "расширения" от центра
            return Transform.scale(
              scale: 0.8 + (0.2 * progress),
              child: Opacity(
                opacity: progress,
                child: child!,
              ),
            );
          },
          child: child,
        );
      },
    );
  }
}
