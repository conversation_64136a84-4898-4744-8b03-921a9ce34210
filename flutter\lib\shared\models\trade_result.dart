import '../models/trading_simulator_models.dart';
import '../models/trade_action.dart';

// Класс для представления результата сделки
class TradeResult {
  final String symbol;        // Символ (пара) торговли
  final String timeframe;     // Таймфрейм
  final String direction;     // Направление сделки (buy/sell)
  final double entryPrice;    // Цена входа
  final double exitPrice;     // Цена выхода
  final double profit;        // Прибыль/убыток
  final double percentChange; // Процентное изменение цены
  final int leverage;         // Кредитное плечо
  final int timestamp;        // Время сделки
  final bool success;         // Успешность сделки

  TradeResult({
    required this.direction,
    required this.profit,
    required this.entryPrice,
    required this.exitPrice,
    required this.percentChange,
    required this.leverage,
    required this.timestamp,
    this.symbol = 'BTCUSDT',
    this.timeframe = '1h',
    this.success = false,
  });

  // Статический метод для расчета результата сделки
  static TradeResult calculate({
    required TradeAction action,
    required CandleData entryCandle,
    required CandleData resultCandle,
    required double tradeAmount,
    required double leverageMultiplier,
  }) {
    // Определяем направление сделки
    final String direction = action == TradeAction.buy ? 'buy' : 'sell';

    // Рассчитываем процентное изменение
    final double percentChange = ((resultCandle.close - entryCandle.close) / entryCandle.close) * 100;

    // Определяем, успешна ли сделка
    final bool isSuccess = (action == TradeAction.buy && percentChange > 0) ||
                          (action == TradeAction.sell && percentChange < 0);

    // Рассчитываем прибыль/убыток с учетом кредитного плеча
    final double profit = isSuccess
        ? tradeAmount * (percentChange.abs() / 100) * leverageMultiplier
        : -tradeAmount * (percentChange.abs() / 100) * leverageMultiplier;

    return TradeResult(
      direction: direction,
      profit: profit,
      entryPrice: entryCandle.close,
      exitPrice: resultCandle.close,
      percentChange: percentChange,
      leverage: leverageMultiplier.toInt(),
      timestamp: DateTime.now().millisecondsSinceEpoch,
      symbol: 'BTCUSDT',
      timeframe: '1h',
      success: isSuccess,
    );
  }
}
