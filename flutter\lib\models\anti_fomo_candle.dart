import 'dart:math' as math;

/// Class representing a candle in a candlestick chart for the Anti-FOMO simulator
class CandleData {
  final DateTime time;
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;

  CandleData({
    required this.time,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });

  bool get isGreen => close >= open;

  /// Generate a random candle based on a previous candle
  factory CandleData.generateRandom(
    CandleData previous, {
    double volatilityFactor = 1.0,
    bool? forceTrend,
  }) {
    final random = DateTime.now().millisecondsSinceEpoch % 100 / 100;
    final isUptrend = forceTrend ?? (random > 0.5);

    // Base volatility (0.1% to 0.5% normally)
    final baseVolatility = 0.001 + (random * 0.004);
    final appliedVolatility = baseVolatility * volatilityFactor;

    // Calculate new prices
    final basePrice = previous.close;
    final priceChange = basePrice * appliedVolatility * (isUptrend ? 1 : -1);

    final close = basePrice + priceChange;
    final open = previous.close;
    final high = math.max(open, close) * (1 + random * 0.002);
    final low = math.min(open, close) * (1 - random * 0.002);

    return CandleData(
      time: previous.time.add(const Duration(minutes: 30)),
      open: open,
      high: high,
      low: low,
      close: close,
      volume: previous.volume * (0.8 + random * 0.4),
    );
  }
}

/// Enum representing the different timeframes available for trading
enum TimeFrame {
  m30('30m', '30 min', Duration(minutes: 30)),
  h1('1h', '1 hour', Duration(hours: 1)),
  h4('4h', '4 hours', Duration(hours: 4)),
  d1('1d', '1 day', Duration(days: 1));

  const TimeFrame(this.apiValue, this.displayName, this.duration);
  final String apiValue;
  final String displayName;
  final Duration duration;
}
