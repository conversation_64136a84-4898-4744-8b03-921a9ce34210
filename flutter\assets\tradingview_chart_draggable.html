<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Draggable TradingView Chart</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #131722;
            overflow: hidden;
        }
        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            cursor: grab;
        }
        #chart-container:active {
            cursor: grabbing;
        }
        #result-popup {
            position: absolute;
            top: 50%;
            left: 25%;
            transform: translate(-50%, -50%);
            background-color: rgba(30, 34, 45, 0.9);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            min-width: 200px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: none;
        }
        .result-success { color: #4CAF50; }
        .result-failure { color: #F44336; }
        .result-profit {
            font-size: 20px;
            font-weight: bold;
            margin: 12px 0;
        }
        .profit-positive { color: #4CAF50; }
        .profit-negative { color: #F44336; }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="result-popup">
        <div class="result-title">Результат</div>
        <div id="result-status"></div>
        <div id="result-profit" class="result-profit"></div>
    </div>

    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Глобальные переменные
        let chart;
        let candleSeries;
        let allCandles = [];
        let visibleCandlesCount = 243;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;

        // Обработчик сообщений от Flutter
        window.addEventListener('message', function(event) {
            console.log('Received message:', event.data);
            try {
                const message = JSON.parse(event.data);

                switch (message.action) {
                    case 'loadCandles':
                        loadAllCandles(message.data);
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'determineResult':
                        determineResult();
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                    case 'centerLastCandle':
                        centerLastCandle();
                        break;
                    case 'checkChartState':
                        checkChartState();
                        break;
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });

        // Инициализация графика
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });

        function initChart() {
            const container = document.getElementById('chart-container');

            // Создаем график с базовыми настройками
            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: container.clientHeight,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    barSpacing: 6,
                },
                rightPriceScale: {
                    borderColor: '#2a2e39',
                    autoScale: true,
                },
                // Важно: включаем перемещение и масштабирование
                handleScroll: true,
                handleScale: true,
            });

            // Создаем серию свечей
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });

            // Обработчик изменения размера окна
            window.addEventListener('resize', function() {
                chart.resize(container.clientWidth, container.clientHeight);
                centerLastCandle();
            });

            // Сообщаем Flutter, что график инициализирован
            sendMessageToFlutter('chartInitialized', []);
        }

        // Загрузка всех свечей
        function loadAllCandles(candles) {
            try {
                allCandles = Array.isArray(candles) ? candles : JSON.parse(candles);
                console.log(`Loaded ${allCandles.length} candles`);

                // Показываем начальные свечи
                showInitialCandles();

                // Сообщаем Flutter, что свечи загружены
                sendMessageToFlutter('candlesLoaded', [allCandles.length]);
            } catch (e) {
                console.error('Error loading candles:', e);
            }
        }

        // Показать только первые 243 свечи
        function showInitialCandles() {
            if (!allCandles.length) return;

            try {
                // Берем только первые 243 свечи
                const initialCandles = allCandles.slice(0, visibleCandlesCount);

                // Устанавливаем свечи на график
                candleSeries.setData(initialCandles);

                // Центрируем последнюю свечу
                centerLastCandle();

                // Очищаем элементы графика
                clearChartElements();
            } catch (e) {
                console.error('Error showing initial candles:', e);
            }
        }

        // Показать все 250 свечей
        function showAllCandles() {
            if (!allCandles.length) return;

            try {
                // Устанавливаем все свечи на график
                candleSeries.setData(allCandles);

                // Центрируем последнюю свечу
                centerLastCandle();

                // Сообщаем Flutter, что все свечи отображены
                sendMessageToFlutter('allCandlesShown', []);
            } catch (e) {
                console.error('Error showing all candles:', e);
            }
        }

        // Центрирование последней свечи
        function centerLastCandle() {
            if (!chart || !allCandles.length) return;

            try {
                const container = document.getElementById('chart-container');
                const containerWidth = container.clientWidth;

                // Получаем индексы свечей
                const lastCandleIndex = candleSeries.dataByIndex().length - 1;
                if (lastCandleIndex < 0) return;

                // Рассчитываем позицию для последней свечи (15% правее центра)
                const barSpacing = 6;
                const visibleBarsCount = Math.floor(containerWidth / barSpacing);
                const rightOffset = Math.floor(visibleBarsCount * 0.15);

                // Устанавливаем видимый диапазон
                chart.timeScale().setVisibleLogicalRange({
                    from: Math.max(0, lastCandleIndex - visibleBarsCount + rightOffset),
                    to: lastCandleIndex + rightOffset
                });
            } catch (e) {
                console.error('Error centering last candle:', e);
            }
        }

        // Установка точки входа
        function setEntryPoint() {
            if (!allCandles.length) return;

            try {
                // Точка входа - последняя видимая свеча
                const entryCandle = allCandles[visibleCandlesCount - 1];
                if (!entryCandle) return;

                entryPointPrice = entryCandle.close;
                entryPointTime = entryCandle.time;

                // Удаляем предыдущую линию, если она есть
                if (horizontalLine) {
                    candleSeries.removePriceLine(horizontalLine);
                }

                // Создаем горизонтальную линию для точки входа
                horizontalLine = candleSeries.createPriceLine({
                    price: entryPointPrice,
                    color: 'rgba(255, 255, 255, 0.7)',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                    axisLabelVisible: true,
                    title: 'Entry',
                });

                // Сообщаем Flutter, что точка входа установлена
                sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);
            } catch (e) {
                console.error('Error setting entry point:', e);
            }
        }

        // Определение результата
        function determineResult() {
            if (!entryPointPrice || allCandles.length < visibleCandlesCount + 7) return;

            try {
                // Берем 7-ю свечу после точки входа
                const resultCandle = allCandles[visibleCandlesCount + 6];
                if (!resultCandle) return;

                // Сравниваем цену закрытия с ценой входа
                const priceChange = resultCandle.close - entryPointPrice;
                const percentChange = (priceChange / entryPointPrice) * 100;
                const isUp = priceChange > 0;

                // Показываем всплывающее окно с результатом
                showResultPopup(isUp, percentChange);

                // Сообщаем Flutter о результате
                sendMessageToFlutter('tradeResult', [
                    isUp,
                    percentChange,
                    resultCandle.close
                ]);
            } catch (e) {
                console.error('Error determining result:', e);
            }
        }

        // Показать всплывающее окно с результатом
        function showResultPopup(isSuccess, percentChange) {
            const popup = document.getElementById('result-popup');
            const statusElement = document.getElementById('result-status');
            const profitElement = document.getElementById('result-profit');

            // Устанавливаем статус
            statusElement.innerHTML = isSuccess ?
                '<span class="result-success">Правильный выбор</span>' :
                '<span class="result-failure">Неправильный выбор</span>';

            // Форматируем процент изменения
            const formattedPercent = Math.abs(percentChange).toFixed(2) + '%';

            // Устанавливаем прибыль/убыток
            profitElement.innerHTML = percentChange >= 0 ?
                '+' + formattedPercent :
                '-' + formattedPercent;
            profitElement.className = 'result-profit ' +
                (percentChange >= 0 ? 'profit-positive' : 'profit-negative');

            // Показываем всплывающее окно
            popup.style.display = 'block';

            // Скрываем через 5 секунд
            setTimeout(() => {
                popup.style.display = 'none';
            }, 5000);
        }

        // Очистка всех элементов графика
        function clearChartElements() {
            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;

            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Скрываем всплывающее окно с результатом
            document.getElementById('result-popup').style.display = 'none';
        }

        // Проверка состояния графика
        function checkChartState() {
            console.log('Checking chart state');

            try {
                // Проверяем, что график создан и настроен правильно
                if (!chart) {
                    console.error('Chart is not initialized');
                    return;
                }

                // Получаем текущие настройки графика
                const options = chart.options();

                // Проверяем, что перемещение и масштабирование включены
                console.log('Chart draggable status:', {
                    handleScroll: options.handleScroll,
                    handleScale: options.handleScale,
                    timeScale: options.timeScale ? 'configured' : 'not configured'
                });

                // Если перемещение отключено, включаем его
                if (!options.handleScroll || !options.handleScale) {
                    console.log('Enabling chart dragging and scaling');
                    chart.applyOptions({
                        handleScroll: true,
                        handleScale: true
                    });
                }

                // Проверяем, что серия свечей создана и содержит данные
                if (candleSeries) {
                    const candleCount = candleSeries.dataByIndex().length;
                    console.log('Candle series contains', candleCount, 'candles');
                } else {
                    console.error('Candle series is not initialized');
                }

                // Проверяем видимый диапазон
                const visibleRange = chart.timeScale().getVisibleLogicalRange();
                console.log('Visible range:', visibleRange);

                // Отправляем результаты проверки во Flutter
                sendMessageToFlutter('chartStateChecked', [
                    options.handleScroll,
                    options.handleScale,
                    candleSeries ? candleSeries.dataByIndex().length : 0
                ]);
            } catch (e) {
                console.error('Error checking chart state:', e);
            }
        }

        // Отправка сообщения в Flutter
        function sendMessageToFlutter(handler, args) {
            try {
                const message = JSON.stringify({
                    handler: handler,
                    args: args
                });

                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.postMessage(message);
                } else if (window.parent && window.parent !== window) {
                    window.parent.postMessage(message, '*');
                }
            } catch (e) {
                console.error('Error sending message to Flutter:', e);
            }
        }
    </script>
</body>
</html>
