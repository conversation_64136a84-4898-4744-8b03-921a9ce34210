<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Minimal TradingView Chart</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #131722;
        }

        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        #status {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            display: none;
        }

        #entry-line {
            position: absolute;
            left: 0;
            right: 0;
            height: 1px;
            background-color: rgba(255, 255, 255, 0.5);
            z-index: 900;
            display: none;
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="status"></div>
    <div id="entry-line"></div>

    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Глобальные переменные
        let chart;
        let candleSeries;
        let allCandles = [];
        let visibleCandlesCount = 243;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing chart...');
            initChart();
        });

        // Обработчик сообщений от Flutter
        window.addEventListener('message', function(event) {
            console.log('Received message:', event.data);
            try {
                const message = JSON.parse(event.data);

                switch (message.action) {
                    case 'loadCandles':
                        loadCandles(message.data);
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'determineResult':
                        determineResult();
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                    case 'resetChartPosition':
                        resetChartPosition();
                        break;
                    case 'centerLastCandle':
                        centerLastCandle();
                        break;
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });

        // Показать статус
        function showStatus(message, duration = 3000) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.display = 'block';

            setTimeout(() => {
                status.style.display = 'none';
            }, duration);
        }

        // Инициализация графика - минимальная версия
        function initChart() {
            console.log('Initializing minimal chart');
            const container = document.getElementById('chart-container');

            // Создаем график с минимальными настройками
            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: container.clientHeight,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    barSpacing: 6,
                    rightOffset: 5,
                },
                rightPriceScale: {
                    autoScale: true,
                },
                handleScroll: true,
                handleScale: true,
            });

            // Создаем серию свечей
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });

            // Обработчик изменения размера окна
            window.addEventListener('resize', function() {
                chart.resize(container.clientWidth, container.clientHeight);
            });

            // Сообщаем Flutter, что график инициализирован
            sendMessageToFlutter('chartInitialized', []);
            showStatus('Chart initialized');
        }

        // Загрузка свечей
        function loadCandles(candles) {
            console.log('Loading candles');
            try {
                allCandles = Array.isArray(candles) ? candles : JSON.parse(candles);
                console.log(`Loaded ${allCandles.length} candles`);

                // Показываем начальные свечи
                showInitialCandles();

                // Сообщаем Flutter, что свечи загружены
                sendMessageToFlutter('candlesLoaded', [allCandles.length]);
                showStatus(`Loaded ${allCandles.length} candles`);
            } catch (e) {
                console.error('Error loading candles:', e);
                showStatus('Error loading candles');
            }
        }

        // Создание дополнительных свечей для прокрутки вправо
        function createExtraCandles() {
            if (!allCandles || allCandles.length < 2) return [];

            const extraCandles = [];
            const lastCandle = allCandles[allCandles.length - 1];
            const secondLastCandle = allCandles[allCandles.length - 2];
            const timeStep = lastCandle.time - secondLastCandle.time;

            // Создаем дополнительные свечи с реалистичными данными
            let prevClose = lastCandle.close;
            for (let i = 1; i <= 300; i++) {
                const changePercent = (Math.random() * 4 - 2) * 1.0;
                const change = prevClose * (changePercent / 100);

                const close = prevClose + change;
                const open = prevClose;
                const high = Math.max(open, close) * (1 + Math.random() * 0.01);
                const low = Math.min(open, close) * (1 - Math.random() * 0.01);
                const volume = lastCandle.volume * (0.5 + Math.random());

                extraCandles.push({
                    time: lastCandle.time + timeStep * i,
                    open: open,
                    high: high,
                    low: low,
                    close: close,
                    volume: volume
                });

                prevClose = close;
            }

            console.log(`Created ${extraCandles.length} extra candles`);
            return extraCandles;
        }

        // Показать только первые 243 свечи
        function showInitialCandles() {
            console.log('Showing initial candles');

            if (!allCandles.length) {
                console.error('No candles available');
                return;
            }

            try {
                // Берем только первые 243 свечи
                const initialCandles = allCandles.slice(0, visibleCandlesCount);

                // Создаем дополнительные свечи для прокрутки вправо
                const extraCandles = createExtraCandles();

                // Объединяем начальные свечи с дополнительными
                const extendedCandles = [...initialCandles, ...extraCandles];

                // Устанавливаем свечи на график
                candleSeries.setData(extendedCandles);

                // Центрируем последнюю свечу
                setTimeout(() => {
                    centerLastCandle();

                    // Сообщаем Flutter, что начальные свечи отображены
                    sendMessageToFlutter('initialCandlesShown', []);
                    showStatus('Initial candles shown');
                }, 100);
            } catch (e) {
                console.error('Error showing initial candles:', e);
                showStatus('Error showing initial candles');
            }
        }

        // Показать все 250 свечей
        function showAllCandles() {
            console.log('Showing all candles');

            if (!allCandles.length) {
                console.error('No candles available');
                return;
            }

            try {
                // Создаем дополнительные свечи для прокрутки вправо
                const extraCandles = createExtraCandles();

                // Объединяем все свечи с дополнительными
                const extendedCandles = [...allCandles, ...extraCandles];

                // Устанавливаем свечи на график
                candleSeries.setData(extendedCandles);

                // Центрируем последнюю свечу
                setTimeout(() => {
                    centerLastCandle();

                    // Сообщаем Flutter, что все свечи отображены
                    sendMessageToFlutter('allCandlesShown', []);
                    showStatus('All candles shown');
                }, 100);
            } catch (e) {
                console.error('Error showing all candles:', e);
                showStatus('Error showing all candles');
            }
        }

        // Центрирование последней свечи
        function centerLastCandle() {
            console.log('Centering last candle');

            if (!chart || !candleSeries) {
                console.error('Chart or candleSeries not available');
                return;
            }

            try {
                // Получаем данные свечей
                const candles = candleSeries.dataByIndex();
                if (!candles || candles.length === 0) {
                    console.error('No candles available');
                    return;
                }

                // Получаем индекс последней видимой свечи
                const lastVisibleCandleIndex = Math.min(visibleCandlesCount, allCandles.length) - 1;

                // Устанавливаем видимый диапазон
                const visibleRange = {
                    from: Math.max(0, lastVisibleCandleIndex - 100),
                    to: lastVisibleCandleIndex + 20
                };

                chart.timeScale().setVisibleLogicalRange(visibleRange);

                // Отправляем сообщение во Flutter
                sendMessageToFlutter('lastCandleCentered', []);
                showStatus('Last candle centered');
            } catch (e) {
                console.error('Error centering last candle:', e);
                showStatus('Error centering last candle');
            }
        }

        // Сброс позиции графика
        function resetChartPosition() {
            console.log('Resetting chart position');

            if (!chart || !candleSeries) {
                console.error('Chart or candleSeries not available');
                return;
            }

            try {
                // Очищаем все элементы графика
                clearChartElements();

                // Центрируем последнюю свечу
                centerLastCandle();

                // Отправляем сообщение во Flutter
                sendMessageToFlutter('chartPositionReset', []);
                showStatus('Chart position reset');
            } catch (e) {
                console.error('Error resetting chart position:', e);
                showStatus('Error resetting chart position');
            }
        }

        // Установка точки входа
        function setEntryPoint() {
            console.log('Setting entry point');

            if (!allCandles || allCandles.length < visibleCandlesCount) {
                console.error('Not enough candles for entry point');
                return;
            }

            try {
                // Точка входа - последняя видимая свеча (243-я)
                const entryCandle = allCandles[visibleCandlesCount - 1];
                if (!entryCandle) {
                    console.error('Entry candle not found');
                    return;
                }

                entryPointPrice = entryCandle.close;
                entryPointTime = entryCandle.time;

                console.log('Entry point set at price:', entryPointPrice);

                // Удаляем предыдущую линию, если она есть
                if (horizontalLine) {
                    candleSeries.removePriceLine(horizontalLine);
                }

                // Создаем горизонтальную линию для точки входа
                horizontalLine = candleSeries.createPriceLine({
                    price: entryPointPrice,
                    color: 'rgba(255, 255, 255, 0.7)',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                    axisLabelVisible: true,
                    title: 'Entry',
                });

                // Сообщаем Flutter, что точка входа установлена
                sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);
                showStatus('Entry point set');
            } catch (e) {
                console.error('Error setting entry point:', e);
                showStatus('Error setting entry point');
            }
        }

        // Определение результата
        function determineResult() {
            console.log('Determining result');

            if (!entryPointPrice) {
                console.error('Entry point not set');
                return;
            }

            if (allCandles.length < visibleCandlesCount + 7) {
                console.error('Not enough candles for result');
                return;
            }

            try {
                // Берем 7-ю свечу после точки входа
                const resultCandle = allCandles[visibleCandlesCount + 6];
                if (!resultCandle) {
                    console.error('Result candle not found');
                    return;
                }

                // Сравниваем цену закрытия с ценой входа
                const priceChange = resultCandle.close - entryPointPrice;
                const percentChange = (priceChange / entryPointPrice) * 100;
                const isUp = priceChange > 0;

                console.log('Result determined:', {
                    isUp: isUp,
                    percentChange: percentChange.toFixed(2) + '%',
                    finalPrice: resultCandle.close
                });

                // Сообщаем Flutter о результате
                sendMessageToFlutter('tradeResult', [
                    isUp,
                    percentChange,
                    resultCandle.close
                ]);

                showStatus(`Result: ${isUp ? 'UP' : 'DOWN'} (${percentChange.toFixed(2)}%)`);
            } catch (e) {
                console.error('Error determining result:', e);
                showStatus('Error determining result');
            }
        }

        // Очистка всех элементов графика
        function clearChartElements() {
            console.log('Clearing chart elements');

            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;

            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }
        }

        // Отправка сообщения в Flutter
        function sendMessageToFlutter(handler, args) {
            try {
                const message = JSON.stringify({
                    handler: handler,
                    args: args
                });

                if (window.flutter_inappwebview) {
                    // Для мобильных платформ
                    window.flutter_inappwebview.postMessage(message);
                } else if (window.parent && window.parent !== window) {
                    // Для веб-платформы (iframe)
                    window.parent.postMessage(message, '*');
                } else {
                    console.log('Flutter interface not available');
                }
            } catch (e) {
                console.error('Error sending message to Flutter:', e);
            }
        }
    </script>
</body>
</html>
