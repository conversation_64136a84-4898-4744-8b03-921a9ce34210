import 'package:flutter/material.dart';
import 'dart:math' as math;

class WorkingSilkBackground extends StatefulWidget {
  final double speed;
  final double scale;
  final Color color;
  final double noiseIntensity;
  final double rotation;

  const WorkingSilkBackground({
    super.key,
    this.speed = 1.8,
    this.scale = 0.8,
    this.color = const Color(0xFF1C1C1C),
    this.noiseIntensity = 0.5,
    this.rotation = 0.48,
  });

  @override
  State<WorkingSilkBackground> createState() => _WorkingSilkBackgroundState();
}

class _WorkingSilkBackgroundState extends State<WorkingSilkBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: (3000 / widget.speed).round()),
      vsync: this,
    )..repeat();
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: WorkingSilkPainter(
            time: _animation.value,
            speed: widget.speed,
            scale: widget.scale,
            color: widget.color,
            noiseIntensity: widget.noiseIntensity,
            rotation: widget.rotation,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

class WorkingSilkPainter extends CustomPainter {
  final double time;
  final double speed;
  final double scale;
  final Color color;
  final double noiseIntensity;
  final double rotation;

  WorkingSilkPainter({
    required this.time,
    required this.speed,
    required this.scale,
    required this.color,
    required this.noiseIntensity,
    required this.rotation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Создаем базовый фон для проверки
    _paintTestLayer(canvas, size);
    
    // Основные шёлковые слои
    _paintSilkWaves(canvas, size, time);
    _paintSilkWaves(canvas, size, time + 1.0);
    _paintSilkWaves(canvas, size, time + 2.0);
  }

  void _paintTestLayer(Canvas canvas, Size size) {
    // Видимый тестовый слой - более яркий
    final paint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.bottomCenter,
        end: Alignment.topCenter,
        colors: [
          color.withOpacity(0.8), // Сделал гораздо более заметным
          color.withOpacity(0.4),
          color.withOpacity(0.6),
          color.withOpacity(0.2),
        ],
      ).createShader(Offset.zero & size);
    
    canvas.drawRect(Offset.zero & size, paint);
  }

  void _paintSilkWaves(Canvas canvas, Size size, double animTime) {
    final colors = <Color>[];
    final stops = <double>[];
    
    // Создаем 30 волновых точек
    for (int i = 0; i < 30; i++) {
      final progress = i / 29.0;
      
      // Простая волновая функция
      final wave1 = math.sin(progress * 8 * math.pi + animTime);
      final wave2 = math.sin(progress * 12 * math.pi + animTime * 1.5);
      final wave3 = math.cos(progress * 6 * math.pi + animTime * 0.8);
      
      final combinedWave = (wave1 + wave2 + wave3) / 3;
      final intensity = (0.5 + combinedWave * 0.3).clamp(0.0, 1.0);
      
      // Делаем очень заметным
      final opacity = intensity * 0.9; // Максимальная прозрачность
      colors.add(color.withOpacity(opacity));
      stops.add(progress);
    }
    
    // Движение снизу вверх
    final movementOffset = (animTime * 0.2) % 2.0 - 1.0;
    
    final paint = Paint()
      ..shader = LinearGradient(
        begin: Alignment(
          math.sin(rotation) * 0.3,
          1.0 + movementOffset,
        ),
        end: Alignment(
          -math.sin(rotation) * 0.3,
          -1.0 + movementOffset,
        ),
        colors: colors,
        stops: stops,
      ).createShader(Offset.zero & size)
      ..blendMode = BlendMode.screen; // Изменил blend mode для лучшей видимости
    
    canvas.drawRect(Offset.zero & size, paint);
  }

  @override
  bool shouldRepaint(covariant WorkingSilkPainter oldDelegate) {
    return oldDelegate.time != time;
  }
} 