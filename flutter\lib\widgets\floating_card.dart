import 'package:flutter/material.dart';
import 'dart:math' as math;

class FloatingCard extends StatefulWidget {
  final Widget child;
  const FloatingCard({Key? key, required this.child}) : super(key: key);

  @override
  State<FloatingCard> createState() => _FloatingCardState();
}

class _FloatingCardState extends State<FloatingCard>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final dy = 4 * math.sin(2 * math.pi * _controller.value);
        return Transform.translate(
          offset: Offset(0, dy * 4),
          child: child,
        );
      },
      child: widget.child,
    );
  }
} 