import 'package:flutter/material.dart';
import 'page_transitions.dart';

/// Улучшенный обработчик навигации с красивыми переходами
class EnhancedNavigationHandler {
  
  /// Навигация между основными экранами приложения
  static void navigateToScreen(BuildContext context, int targetIndex, int currentIndex) {
    String routeName = _getRouteNameByIndex(targetIndex);
    
    if (routeName.isEmpty) return;
    
    // Простая навигация без сложных анимаций - используем стандартную
    Navigator.pushReplacementNamed(context, routeName);
  }
  
  /// Получение имени маршрута по индексу
  static String _getRouteNameByIndex(int index) {
    switch (index) {
      case 0:
        return '/news';
      case 1:
        return '/charts';
      case 2:
        return '/sinusoid'; // Исправлено: правильный маршрут для Sinusoid
      case 3:
        return '/courses';
      case 4:
        return '/profile';
      default:
        return '';
    }
  }
  
  /// Анимированная навигация для модальных окон
  static Future<T?> showAnimatedModal<T>(
    BuildContext context,
    Widget page, {
    bool useSlideUp = true,
  }) {
    return Navigator.of(context).push<T>(
      useSlideUp 
        ? PageTransitions.smoothSlideUp<T>(page)
        : PageTransitions.smoothScale<T>(page),
    );
  }
  
  /// Анимированный bottom sheet
  static Future<T?> showAnimatedBottomSheet<T>(
    BuildContext context,
    Widget content, {
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: Colors.transparent,
      builder: (context) => AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(20),
            ),
          ),
          child: content,
        ),
      ),
    );
  }
}

/// Расширения для удобного использования
extension ContextNavigationExtensions on BuildContext {
  
  /// Навигация с красивыми переходами
  void navigateWithAnimation(int targetIndex, int currentIndex) {
    EnhancedNavigationHandler.navigateToScreen(this, targetIndex, currentIndex);
  }
  
  /// Показать анимированное модальное окно
  Future<T?> showAnimatedModal<T>(Widget page, {bool useSlideUp = true}) {
    return EnhancedNavigationHandler.showAnimatedModal<T>(
      this, 
      page, 
      useSlideUp: useSlideUp,
    );
  }
  
  /// Показать анимированный bottom sheet
  Future<T?> showAnimatedBottomSheet<T>(
    Widget content, {
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    return EnhancedNavigationHandler.showAnimatedBottomSheet<T>(
      this,
      content,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
    );
  }
} 