import 'dart:convert';
import 'dart:html' as html;
import 'dart:ui_web' as ui_web;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/candle.dart';

class WebTradingViewChart extends StatefulWidget {
  final List<Candle> initialCandles;
  final List<Candle> futureCandles;
  final bool showFutureCandles;
  final Function(double price, int time)? onEntryPointSet;
  final Function(bool isUp, double percentChange, double finalPrice)? onTradeResult;

  const WebTradingViewChart({
    super.key,
    required this.initialCandles,
    this.futureCandles = const [],
    this.showFutureCandles = false,
    this.onEntryPointSet,
    this.onTradeResult,
  });

  @override
  State<WebTradingViewChart> createState() => WebTradingViewChartState();
}

class WebTradingViewChartState extends State<WebTradingViewChart> {
  final String viewType = 'tradingview-chart-${DateTime.now().millisecondsSinceEpoch}';
  late html.IFrameElement _iframeElement;
  bool _isLoaded = false;
  String _htmlContent = '';

  @override
  void initState() {
    super.initState();
    _loadHtmlContent();
  }

  @override
  void didUpdateWidget(WebTradingViewChart oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Если изменились свечи, обновляем график
    if (widget.initialCandles != oldWidget.initialCandles) {
      _loadInitialCandles();
    }

    // Если нужно показать будущие свечи
    if (widget.showFutureCandles && !oldWidget.showFutureCandles) {
      _loadFutureCandles();
    }
  }

  Future<void> _loadHtmlContent() async {
    try {
      // Загружаем HTML-файл
      _htmlContent = await rootBundle.loadString('assets/tradingview_chart.html');
      debugPrint('HTML content loaded, length: ${_htmlContent.length}');

      // Регистрируем обработчик сообщений
      html.window.onMessage.listen(_handleMessage);

      // Создаем iframe
      _iframeElement = html.IFrameElement()
        ..style.border = 'none'
        ..style.height = '100%'
        ..style.width = '100%'
        ..srcdoc = _htmlContent;

      // Регистрируем view factory
      ui_web.platformViewRegistry.registerViewFactory(
        viewType,
        (int viewId) => _iframeElement,
      );

      setState(() {
        _isLoaded = true;
      });

      // Загружаем начальные свечи после небольшой задержки
      Future.delayed(const Duration(milliseconds: 500), () {
        _loadInitialCandles();
      });

    } catch (e) {
      debugPrint('Error loading HTML content: $e');
    }
  }

  void _handleMessage(html.MessageEvent event) {
    try {
      debugPrint('Received message from iframe: ${event.data}');

      // Проверяем, является ли сообщение строкой
      String messageStr;
      if (event.data is String) {
        messageStr = event.data as String;
      } else {
        debugPrint('Message is not a string: ${event.data.runtimeType}');
        return;
      }

      // Пытаемся декодировать JSON
      final Map<String, dynamic> data = jsonDecode(messageStr);

      // Проверяем наличие необходимых полей
      if (!data.containsKey('handler') || !data.containsKey('args')) {
        debugPrint('Message does not contain required fields: $data');
        return;
      }

      final String handler = data['handler'];
      final List<dynamic> args = data['args'];

      debugPrint('Processing handler: $handler with args: $args');

      switch (handler) {
        case 'entryPointSet':
          if (widget.onEntryPointSet != null && args.length >= 2) {
            widget.onEntryPointSet!(
              args[0] is num ? args[0].toDouble() : 0.0,
              args[1] is num ? args[1].toInt() : 0,
            );
          }
          break;
        case 'tradeResult':
          if (widget.onTradeResult != null && args.length >= 3) {
            widget.onTradeResult!(
              args[0] is bool ? args[0] : false,
              args[1] is num ? args[1].toDouble() : 0.0,
              args[2] is num ? args[2].toDouble() : 0.0,
            );
          }
          break;
        default:
          debugPrint('Unknown handler: $handler');
      }
    } catch (e, stackTrace) {
      debugPrint('Error handling message: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  Future<void> _loadInitialCandles() async {
    if (!_isLoaded || widget.initialCandles.isEmpty) {
      debugPrint('Cannot load candles: loaded: $_isLoaded, candles empty: ${widget.initialCandles.isEmpty}');
      return;
    }

    debugPrint('Loading ${widget.initialCandles.length} initial candles to iframe');

    try {
      // Преобразуем свечи в формат для TradingView
      final List<Map<String, dynamic>> candlesData = widget.initialCandles.map((candle) => {
        'time': candle.time,
        'open': candle.open,
        'high': candle.high,
        'low': candle.low,
        'close': candle.close,
        'volume': candle.volume,
      }).toList();

      final String jsonData = jsonEncode(candlesData);
      debugPrint('Candles JSON data length: ${jsonData.length}');

      // Отправляем данные в iframe
      final message = jsonEncode({
        'action': 'loadCandles',
        'data': candlesData,
      });

      debugPrint('Sending message to iframe: ${message.substring(0, 100)}...');

      // Даем время для инициализации iframe
      Future.delayed(const Duration(milliseconds: 1000), () {
        _iframeElement.contentWindow?.postMessage(message, '*');
        debugPrint('Message sent to iframe');
      });

      debugPrint('Initial candles sent to iframe');
    } catch (e) {
      debugPrint('Error loading initial candles: $e');
    }
  }

  Future<void> _loadFutureCandles() async {
    if (!_isLoaded || widget.futureCandles.isEmpty) {
      debugPrint('Cannot load future candles: loaded: $_isLoaded, candles empty: ${widget.futureCandles.isEmpty}');
      return;
    }

    debugPrint('Loading ${widget.futureCandles.length} future candles to iframe');

    try {
      // Преобразуем свечи в формат для TradingView
      final List<Map<String, dynamic>> candlesData = widget.futureCandles.map((candle) => {
        'time': candle.time,
        'open': candle.open,
        'high': candle.high,
        'low': candle.low,
        'close': candle.close,
        'volume': candle.volume,
      }).toList();

      final String jsonData = jsonEncode(candlesData);
      debugPrint('Future candles JSON data length: ${jsonData.length}');

      // Отправляем данные в iframe
      final message = jsonEncode({
        'action': 'loadFutureCandles',
        'data': candlesData,
      });

      debugPrint('Sending future candles message to iframe: ${message.substring(0, 100)}...');

      // Даем время для инициализации iframe
      Future.delayed(const Duration(milliseconds: 1000), () {
        _iframeElement.contentWindow?.postMessage(message, '*');
        debugPrint('Future candles message sent to iframe');
      });

      debugPrint('Future candles sent to iframe');
    } catch (e) {
      debugPrint('Error loading future candles: $e');
    }
  }

  Future<void> setEntryPoint() async {
    if (!_isLoaded) {
      debugPrint('Cannot set entry point: loaded: $_isLoaded');
      return;
    }

    debugPrint('Setting entry point');
    try {
      final message = jsonEncode({
        'action': 'setEntryPoint',
      });

      // Даем время для инициализации iframe
      Future.delayed(const Duration(milliseconds: 500), () {
        _iframeElement.contentWindow?.postMessage(message, '*');
        debugPrint('Entry point set command sent');
      });
    } catch (e) {
      debugPrint('Error setting entry point: $e');
    }
  }

  Future<void> determineResult() async {
    if (!_isLoaded) {
      debugPrint('Cannot determine result: loaded: $_isLoaded');
      return;
    }

    debugPrint('Determining result');
    try {
      final message = jsonEncode({
        'action': 'determineResult',
      });

      // Даем время для инициализации iframe
      Future.delayed(const Duration(milliseconds: 500), () {
        _iframeElement.contentWindow?.postMessage(message, '*');
        debugPrint('Determine result command sent');
      });
    } catch (e) {
      debugPrint('Error determining result: $e');
    }
  }

  // Новый метод для отображения всех свечей
  Future<void> showAllCandles() async {
    if (!_isLoaded) {
      debugPrint('Cannot show all candles: loaded: $_isLoaded');
      return;
    }

    debugPrint('Showing all candles');
    try {
      final message = jsonEncode({
        'action': 'showAllCandles',
      });

      // Отправляем сообщение в iframe
      _iframeElement.contentWindow?.postMessage(message, '*');
      debugPrint('Show all candles command sent');
    } catch (e) {
      debugPrint('Error showing all candles: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isLoaded) {
      return const Center(child: CircularProgressIndicator());
    }

    return HtmlElementView(viewType: viewType);
  }
}
