import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../utils/page_transitions.dart';
import '../utils/enhanced_navigation_handler.dart';

/// Демонстрационный экран для тестирования анимаций навигации
class NavigationDemoScreen extends StatelessWidget {
  const NavigationDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF000000),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Navigation Animations Demo',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: Icon<PERSON>utton(
          icon: const Icon(CupertinoIcons.back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF000000),
              Color(0xFF1A1A2E),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Тестирование анимаций переходов',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Выберите тип анимации для тестирования',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 30),
                
                Expanded(
                  child: ListView(
                    children: [
                      _buildAnimationCard(
                        context,
                        'Элегантный слайд',
                        'Плавный переход с затуханием и масштабированием',
                        CupertinoIcons.arrow_right_circle_fill,
                        const Color(0xFF007AFF),
                        () => _showDemoPage(context, PageTransitions.elegantSlide),
                      ),
                      
                      _buildAnimationCard(
                        context,
                        'Плавное масштабирование',
                        'Мягкое появление с эффектом увеличения',
                        CupertinoIcons.zoom_in,
                        const Color(0xFF34C759),
                        () => _showDemoPage(context, PageTransitions.smoothScale),
                      ),
                      
                      _buildAnimationCard(
                        context,
                        'Мягкое затухание',
                        'Деликатный переход с легким движением',
                        CupertinoIcons.circle_fill,
                        const Color(0xFFFF9500),
                        () => _showDemoPage(context, PageTransitions.softFade),
                      ),
                      
                      _buildAnimationCard(
                        context,
                        'Слайд снизу',
                        'Модальный переход снизу вверх',
                        CupertinoIcons.arrow_up_circle_fill,
                        const Color(0xFF5856D6),
                        () => _showDemoPage(context, PageTransitions.smoothSlideUp),
                      ),
                      
                      _buildAnimationCard(
                        context,
                        'Элегантное вращение',
                        'Красивый поворот с масштабированием',
                        CupertinoIcons.rotate_right_fill,
                        const Color(0xFFAF52DE),
                        () => _showDemoPage(context, PageTransitions.elegantRotation),
                      ),
                      
                      _buildAnimationCard(
                        context,
                        'Переворот страницы',
                        '3D эффект переворота',
                        CupertinoIcons.refresh_circled_solid,
                        const Color(0xFFFF2D92),
                        () => _showDemoPage(context, PageTransitions.flipTransition),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // Дополнительные демонстрации
                      _buildSectionHeader('Дополнительные возможности'),
                      
                      _buildAnimationCard(
                        context,
                        'Анимированное модальное окно',
                        'Красивое появление модального окна',
                        CupertinoIcons.square_stack_3d_up_fill,
                        const Color(0xFF32D74B),
                        () => _showAnimatedModal(context),
                      ),
                      
                      _buildAnimationCard(
                        context,
                        'Анимированный Bottom Sheet',
                        'Плавное появление снизу',
                        CupertinoIcons.rectangle_stack_fill,
                        const Color(0xFF64D2FF),
                        () => _showAnimatedBottomSheet(context),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildAnimationCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: color.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  CupertinoIcons.chevron_right,
                  color: color,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showDemoPage(BuildContext context, PageRouteBuilder Function(Widget) transitionBuilder) {
    Navigator.of(context).push(
      transitionBuilder(_DemoPage()),
    );
  }

  void _showAnimatedModal(BuildContext context) {
    context.showAnimatedModal(
      _DemoModalContent(),
      useSlideUp: true,
    );
  }

  void _showAnimatedBottomSheet(BuildContext context) {
    context.showAnimatedBottomSheet(
      _DemoBottomSheetContent(),
    );
  }
}

/// Демонстрационная страница
class _DemoPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF000000),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Demo Page',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(CupertinoIcons.back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.checkmark_circle_fill,
                color: Color(0xFF34C759),
                size: 80,
              ),
              SizedBox(height: 20),
              Text(
                'Анимация работает!',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Нажмите назад, чтобы вернуться',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Содержимое демонстрационного модального окна
class _DemoModalContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1C1C1E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Модальное окно',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(CupertinoIcons.xmark, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: const Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              CupertinoIcons.star_fill,
              color: Color(0xFFFF9500),
              size: 60,
            ),
            SizedBox(height: 20),
            Text(
              'Красивое модальное окно',
              style: TextStyle(
                color: Colors.white,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12),
            Text(
              'Это демонстрация анимированного модального окна с плавными переходами.',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Содержимое демонстрационного bottom sheet
class _DemoBottomSheetContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          const Icon(
            CupertinoIcons.heart_fill,
            color: Color(0xFFFF2D92),
            size: 50,
          ),
          const SizedBox(height: 16),
          const Text(
            'Анимированный Bottom Sheet',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Плавное появление снизу с красивыми анимациями.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
} 