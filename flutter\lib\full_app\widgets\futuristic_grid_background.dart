import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Элемент сетки для футуристического фона
class GridElement {
  // Позиция элемента
  final double x;
  final double y;
  
  // Параметры отображения
  final double opacity;
  final double size;
  
  // Для эффекта параллакса
  final double parallaxFactor;
  
  GridElement({
    required this.x,
    required this.y,
    required this.opacity,
    required this.size,
    required this.parallaxFactor,
  });
}

/// Линия сетки
class GridLine {
  final Offset start;
  final Offset end;
  final double opacity;
  final double width;
  final double parallaxFactor;
  
  GridLine({
    required this.start,
    required this.end,
    required this.opacity,
    required this.width,
    required this.parallaxFactor,
  });
}

/// Интерактивный футуристический фон с сеткой
class FuturisticGridBackground extends CustomPainter {
  final double animationValue;
  final Offset pointerPosition;
  final double scrollOffset;
  final Size screenSize;
  final Color primaryColor;
  final Color accentColor;
  
  // Элементы сетки
  final List<GridElement> _gridPoints = [];
  final List<GridLine> _gridLines = [];
  
  // Радиус влияния курсора
  final double cursorInfluenceRadius = 200.0;
  
  FuturisticGridBackground({
    required this.animationValue,
    required this.pointerPosition,
    required this.scrollOffset,
    required this.screenSize,
    this.primaryColor = Colors.white,
    this.accentColor = Colors.blue,
  }) {
    _initializeGrid();
  }
  
  /// Инициализация элементов сетки
  void _initializeGrid() {
    if (_gridPoints.isNotEmpty) return;
    
    final random = math.Random(42); // Фиксированный seed для консистентности
    
    // Создаем сетку точек
    final horizontalCount = 15;
    final verticalCount = 20;
    
    // Расстояние между точками сетки
    final horizontalSpacing = 1.0 / horizontalCount;
    final verticalSpacing = 1.0 / verticalCount;
    
    // Создание точек сетки
    for (int x = 0; x <= horizontalCount; x++) {
      for (int y = 0; y <= verticalCount; y++) {
        // Небольшое случайное смещение для органичности
        final jitterX = (random.nextDouble() - 0.5) * horizontalSpacing * 0.5;
        final jitterY = (random.nextDouble() - 0.5) * verticalSpacing * 0.5;
        
        final xPos = x * horizontalSpacing + jitterX;
        final yPos = y * verticalSpacing + jitterY;
        
        // Разные уровни параллакса для разных точек (создание эффекта глубины)
        final parallaxFactor = 0.5 + random.nextDouble() * 1.5;
        
        _gridPoints.add(GridElement(
          x: xPos,
          y: yPos,
          opacity: 0.3 + random.nextDouble() * 0.4,
          size: 1.0 + random.nextDouble() * 1.5,
          parallaxFactor: parallaxFactor,
        ));
      }
    }
    
    // Создание линий сетки
    // Горизонтальные линии
    for (int y = 0; y <= verticalCount; y++) {
      if (random.nextDouble() < 0.8) { // 80% шанс создания линии
        _gridLines.add(GridLine(
          start: Offset(0, y * verticalSpacing),
          end: Offset(1, y * verticalSpacing),
          opacity: 0.1 + random.nextDouble() * 0.1,
          width: 0.5 + random.nextDouble() * 0.5,
          parallaxFactor: 0.3 + random.nextDouble() * 0.7,
        ));
      }
    }
    
    // Вертикальные линии
    for (int x = 0; x <= horizontalCount; x++) {
      if (random.nextDouble() < 0.8) { // 80% шанс создания линии
        _gridLines.add(GridLine(
          start: Offset(x * horizontalSpacing, 0),
          end: Offset(x * horizontalSpacing, 1),
          opacity: 0.1 + random.nextDouble() * 0.1,
          width: 0.5 + random.nextDouble() * 0.5,
          parallaxFactor: 0.3 + random.nextDouble() * 0.7,
        ));
      }
    }
    
    // Диагональные линии
    for (int i = 0; i < 10; i++) {
      if (random.nextDouble() < 0.6) { // 60% шанс создания диагональной линии
        final startX = random.nextDouble();
        final startY = random.nextDouble();
        final length = 0.2 + random.nextDouble() * 0.5;
        final angle = random.nextDouble() * math.pi * 2;
        
        _gridLines.add(GridLine(
          start: Offset(startX, startY),
          end: Offset(
            startX + math.cos(angle) * length,
            startY + math.sin(angle) * length,
          ),
          opacity: 0.05 + random.nextDouble() * 0.1,
          width: 0.5 + random.nextDouble() * 0.5,
          parallaxFactor: 0.2 + random.nextDouble() * 0.6,
        ));
      }
    }
  }
  
  @override
  void paint(Canvas canvas, Size size) {
    // Заливка фона
    final backgroundPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(Offset.zero & size, backgroundPaint);
    
    // Вычисление влияния курсора и прокрутки на элементы
    // Рисование линий сетки
    for (final line in _gridLines) {
      // Эффект параллакса от положения курсора
      final cursorParallaxX = (pointerPosition.dx / screenSize.width - 0.5) * 30 * line.parallaxFactor;
      final cursorParallaxY = (pointerPosition.dy / screenSize.height - 0.5) * 30 * line.parallaxFactor;
      
      // Эффект параллакса от прокрутки
      final scrollParallax = scrollOffset * 0.1 * line.parallaxFactor;
      
      // Анимация "дыхания" линий
      final breathEffect = (math.sin(animationValue * math.pi * 2) * 0.1 + 0.9) * line.opacity;
      
      // Рисуем линию
      final startPoint = Offset(
        line.start.dx * size.width + cursorParallaxX,
        line.start.dy * size.height + cursorParallaxY - scrollParallax,
      );
      
      final endPoint = Offset(
        line.end.dx * size.width + cursorParallaxX,
        line.end.dy * size.height + cursorParallaxY - scrollParallax,
      );
      
      final linePaint = Paint()
        ..color = primaryColor.withOpacity(breathEffect)
        ..strokeWidth = line.width
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;
      
      canvas.drawLine(startPoint, endPoint, linePaint);
    }
    
    // Рисование точек сетки
    for (final point in _gridPoints) {
      // Эффект параллакса от положения курсора
      final cursorParallaxX = (pointerPosition.dx / screenSize.width - 0.5) * 50 * point.parallaxFactor;
      final cursorParallaxY = (pointerPosition.dy / screenSize.height - 0.5) * 50 * point.parallaxFactor;
      
      // Эффект параллакса от прокрутки
      final scrollParallax = scrollOffset * 0.15 * point.parallaxFactor;
      
      // Вычисляем финальную позицию
      final x = point.x * size.width + cursorParallaxX;
      final y = point.y * size.height + cursorParallaxY - scrollParallax;
      final position = Offset(x, y);
      
      // Вычисляем расстояние до курсора для интерактивности
      final distanceToCursor = (position - Offset(pointerPosition.dx, pointerPosition.dy)).distance;
      final cursorInfluence = distanceToCursor < cursorInfluenceRadius
          ? 1.0 - (distanceToCursor / cursorInfluenceRadius)
          : 0.0;
      
      // Эффект "пульсации" от времени анимации
      final pulseEffect = math.sin(animationValue * math.pi * 2 + point.x * 10 + point.y * 10) * 0.2 + 0.8;
      
      // Вычисляем финальный размер и прозрачность
      final finalSize = point.size * (1.0 + cursorInfluence * 2.0) * pulseEffect;
      final finalOpacity = point.opacity * (1.0 + cursorInfluence * 1.5);
      
      // Выбираем цвет в зависимости от близости к курсору
      final color = cursorInfluence > 0.3
          ? Color.lerp(primaryColor, accentColor, cursorInfluence)!
          : primaryColor;
      
      // Рисуем точку
      final pointPaint = Paint()
        ..color = color.withOpacity(finalOpacity)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(position, finalSize, pointPaint);
      
      // Эффект свечения для точек близких к курсору
      if (cursorInfluence > 0.2) {
        final glowPaint = Paint()
          ..color = accentColor.withOpacity(cursorInfluence * 0.3)
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 5.0 * cursorInfluence);
        
        canvas.drawCircle(position, finalSize * 2.5, glowPaint);
      }
    }
    
    // Рисуем "ореол" вокруг курсора
    if (pointerPosition != Offset.zero) {
      final glowPaint = Paint()
        ..color = accentColor.withOpacity(0.1)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 30.0);
      
      canvas.drawCircle(Offset(pointerPosition.dx, pointerPosition.dy), 40, glowPaint);
    }
  }
  
  @override
  bool shouldRepaint(FuturisticGridBackground oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.pointerPosition != pointerPosition ||
           oldDelegate.scrollOffset != scrollOffset;
  }
}
