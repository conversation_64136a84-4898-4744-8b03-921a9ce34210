import{TokenType as e,NumberType as a,isTokenIdent as n,isTokenPercentage as r,isTokenNumber as o,isTokenDelim as t,isTokenNumeric as l,isTokenComma as s,isTokenDimension as u,isTokenHash as c}from"@csstools/css-tokenizer";import{XYZ_D50_to_XYZ_D65 as i,XYZ_D50_to_XYZ_D50 as h,XYZ_D50_to_OKLab as m,XYZ_D50_to_OKLCH as p,XYZ_D50_to_LCH as N,XYZ_D50_to_Lab as b,XYZ_D50_to_HWB as g,XYZ_D50_to_HSL as f,XYZ_D50_to_a98_RGB as v,XYZ_D50_to_ProPhoto as d,XYZ_D50_to_rec_2020 as y,XYZ_D50_to_P3 as _,XYZ_D50_to_lin_sRGB as C,XYZ_D50_to_sRGB as w,XYZ_D65_to_XYZ_D50 as x,OKLCH_to_XYZ_D50 as H,LCH_to_XYZ_D50 as L,OKLab_to_XYZ_D50 as P,Lab_to_XYZ_D50 as k,HWB_to_XYZ_D50 as S,HSL_to_XYZ_D50 as M,ProPhoto_RGB_to_XYZ_D50 as z,a98_RGB_to_XYZ_D50 as F,rec_2020_to_XYZ_D50 as D,P3_to_XYZ_D50 as Z,lin_sRGB_to_XYZ_D50 as R,sRGB_to_XYZ_D50 as B,namedColors as V,inGamut as G,clip as T,gam_sRGB as A,mapGamutRayTrace as X,OKLCH_to_OKLab as K,OKLab_to_XYZ as Y,XYZ_to_lin_sRGB as I,lin_sRGB_to_XYZ as O,XYZ_to_OKLab as W,OKLab_to_OKLCH as E,contrast_ratio_wcag_2_1 as U,gam_P3 as $,XYZ_to_lin_P3 as j,lin_P3_to_XYZ as q}from"@csstools/color-helpers";import{isWhitespaceNode as J,isCommentNode as Q,isTokenNode as ee,isFunctionNode as ae,TokenNode as ne,isWhiteSpaceOrCommentNode as re,FunctionNode as oe,WhitespaceNode as te}from"@csstools/css-parser-algorithms";import{mathFunctionNames as le,calcFromComponentValues as se}from"@csstools/css-calc";var ue,ce;function convertNaNToZero(e){return[Number.isNaN(e[0])?0:e[0],Number.isNaN(e[1])?0:e[1],Number.isNaN(e[2])?0:e[2]]}function colorData_to_XYZ_D50(e){switch(e.colorNotation){case ue.HEX:case ue.RGB:case ue.sRGB:return{...e,colorNotation:ue.XYZ_D50,channels:B(convertNaNToZero(e.channels))};case ue.Linear_sRGB:return{...e,colorNotation:ue.XYZ_D50,channels:R(convertNaNToZero(e.channels))};case ue.Display_P3:return{...e,colorNotation:ue.XYZ_D50,channels:Z(convertNaNToZero(e.channels))};case ue.Rec2020:return{...e,colorNotation:ue.XYZ_D50,channels:D(convertNaNToZero(e.channels))};case ue.A98_RGB:return{...e,colorNotation:ue.XYZ_D50,channels:F(convertNaNToZero(e.channels))};case ue.ProPhoto_RGB:return{...e,colorNotation:ue.XYZ_D50,channels:z(convertNaNToZero(e.channels))};case ue.HSL:return{...e,colorNotation:ue.XYZ_D50,channels:M(convertNaNToZero(e.channels))};case ue.HWB:return{...e,colorNotation:ue.XYZ_D50,channels:S(convertNaNToZero(e.channels))};case ue.Lab:return{...e,colorNotation:ue.XYZ_D50,channels:k(convertNaNToZero(e.channels))};case ue.OKLab:return{...e,colorNotation:ue.XYZ_D50,channels:P(convertNaNToZero(e.channels))};case ue.LCH:return{...e,colorNotation:ue.XYZ_D50,channels:L(convertNaNToZero(e.channels))};case ue.OKLCH:return{...e,colorNotation:ue.XYZ_D50,channels:H(convertNaNToZero(e.channels))};case ue.XYZ_D50:return{...e,colorNotation:ue.XYZ_D50,channels:h(convertNaNToZero(e.channels))};case ue.XYZ_D65:return{...e,colorNotation:ue.XYZ_D50,channels:x(convertNaNToZero(e.channels))};default:throw new Error("Unsupported color notation")}}!function(e){e.A98_RGB="a98-rgb",e.Display_P3="display-p3",e.HEX="hex",e.HSL="hsl",e.HWB="hwb",e.LCH="lch",e.Lab="lab",e.Linear_sRGB="srgb-linear",e.OKLCH="oklch",e.OKLab="oklab",e.ProPhoto_RGB="prophoto-rgb",e.RGB="rgb",e.sRGB="srgb",e.Rec2020="rec2020",e.XYZ_D50="xyz-d50",e.XYZ_D65="xyz-d65"}(ue||(ue={})),function(e){e.ColorKeyword="color-keyword",e.HasAlpha="has-alpha",e.HasDimensionValues="has-dimension-values",e.HasNoneKeywords="has-none-keywords",e.HasNumberValues="has-number-values",e.HasPercentageAlpha="has-percentage-alpha",e.HasPercentageValues="has-percentage-values",e.HasVariableAlpha="has-variable-alpha",e.Hex="hex",e.LegacyHSL="legacy-hsl",e.LegacyRGB="legacy-rgb",e.NamedColor="named-color",e.RelativeColorSyntax="relative-color-syntax",e.ColorMix="color-mix",e.ColorMixVariadic="color-mix-variadic",e.ContrastColor="contrast-color",e.Experimental="experimental"}(ce||(ce={}));const ie=new Set([ue.A98_RGB,ue.Display_P3,ue.HEX,ue.Linear_sRGB,ue.ProPhoto_RGB,ue.RGB,ue.sRGB,ue.Rec2020,ue.XYZ_D50,ue.XYZ_D65]);function colorDataTo(e,a){const n={...e};if(e.colorNotation!==a){const e=colorData_to_XYZ_D50(n);switch(a){case ue.HEX:case ue.RGB:n.colorNotation=ue.RGB,n.channels=w(e.channels);break;case ue.sRGB:n.colorNotation=ue.sRGB,n.channels=w(e.channels);break;case ue.Linear_sRGB:n.colorNotation=ue.Linear_sRGB,n.channels=C(e.channels);break;case ue.Display_P3:n.colorNotation=ue.Display_P3,n.channels=_(e.channels);break;case ue.Rec2020:n.colorNotation=ue.Rec2020,n.channels=y(e.channels);break;case ue.ProPhoto_RGB:n.colorNotation=ue.ProPhoto_RGB,n.channels=d(e.channels);break;case ue.A98_RGB:n.colorNotation=ue.A98_RGB,n.channels=v(e.channels);break;case ue.HSL:n.colorNotation=ue.HSL,n.channels=f(e.channels);break;case ue.HWB:n.colorNotation=ue.HWB,n.channels=g(e.channels);break;case ue.Lab:n.colorNotation=ue.Lab,n.channels=b(e.channels);break;case ue.LCH:n.colorNotation=ue.LCH,n.channels=N(e.channels);break;case ue.OKLCH:n.colorNotation=ue.OKLCH,n.channels=p(e.channels);break;case ue.OKLab:n.colorNotation=ue.OKLab,n.channels=m(e.channels);break;case ue.XYZ_D50:n.colorNotation=ue.XYZ_D50,n.channels=h(e.channels);break;case ue.XYZ_D65:n.colorNotation=ue.XYZ_D65,n.channels=i(e.channels);break;default:throw new Error("Unsupported color notation")}}else n.channels=convertNaNToZero(e.channels);if(a===e.colorNotation)n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[0,1,2]);else if(ie.has(a)&&ie.has(e.colorNotation))n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[0,1,2]);else switch(a){case ue.HSL:switch(e.colorNotation){case ue.HWB:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[0]);break;case ue.Lab:case ue.OKLab:n.channels=carryForwardMissingComponents(e.channels,[2],n.channels,[0]);break;case ue.LCH:case ue.OKLCH:n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[2,1,0])}break;case ue.HWB:switch(e.colorNotation){case ue.HSL:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[0]);break;case ue.LCH:case ue.OKLCH:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[2])}break;case ue.Lab:case ue.OKLab:switch(e.colorNotation){case ue.HSL:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[2]);break;case ue.Lab:case ue.OKLab:n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[0,1,2]);break;case ue.LCH:case ue.OKLCH:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[0])}break;case ue.LCH:case ue.OKLCH:switch(e.colorNotation){case ue.HSL:n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[2,1,0]);break;case ue.HWB:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[2]);break;case ue.Lab:case ue.OKLab:n.channels=carryForwardMissingComponents(e.channels,[0],n.channels,[0]);break;case ue.LCH:case ue.OKLCH:n.channels=carryForwardMissingComponents(e.channels,[0,1,2],n.channels,[0,1,2])}}return n.channels=convertPowerlessComponentsToMissingComponents(n.channels,a),n}function convertPowerlessComponentsToMissingComponents(e,a){const n=[...e];switch(a){case ue.HSL:!Number.isNaN(n[1])&&reducePrecision(n[1],4)<=0&&(n[0]=Number.NaN);break;case ue.HWB:Math.max(0,reducePrecision(n[1],4))+Math.max(0,reducePrecision(n[2],4))>=100&&(n[0]=Number.NaN);break;case ue.LCH:!Number.isNaN(n[1])&&reducePrecision(n[1],4)<=0&&(n[2]=Number.NaN);break;case ue.OKLCH:!Number.isNaN(n[1])&&reducePrecision(n[1],6)<=0&&(n[2]=Number.NaN)}return n}function convertPowerlessComponentsToZeroValuesForDisplay(e,a){const n=[...e];switch(a){case ue.HSL:(reducePrecision(n[2])<=0||reducePrecision(n[2])>=100)&&(n[0]=Number.NaN,n[1]=Number.NaN),reducePrecision(n[1])<=0&&(n[0]=Number.NaN);break;case ue.HWB:Math.max(0,reducePrecision(n[1]))+Math.max(0,reducePrecision(n[2]))>=100&&(n[0]=Number.NaN);break;case ue.Lab:(reducePrecision(n[0])<=0||reducePrecision(n[0])>=100)&&(n[1]=Number.NaN,n[2]=Number.NaN);break;case ue.LCH:reducePrecision(n[1])<=0&&(n[2]=Number.NaN),(reducePrecision(n[0])<=0||reducePrecision(n[0])>=100)&&(n[1]=Number.NaN,n[2]=Number.NaN);break;case ue.OKLab:(reducePrecision(n[0])<=0||reducePrecision(n[0])>=1)&&(n[1]=Number.NaN,n[2]=Number.NaN);break;case ue.OKLCH:reducePrecision(n[1])<=0&&(n[2]=Number.NaN),(reducePrecision(n[0])<=0||reducePrecision(n[0])>=1)&&(n[1]=Number.NaN,n[2]=Number.NaN)}return n}function carryForwardMissingComponents(e,a,n,r){const o=[...n];for(const n of a)Number.isNaN(e[a[n]])&&(o[r[n]]=Number.NaN);return o}function normalizeRelativeColorDataChannels(e){const a=new Map;switch(e.colorNotation){case ue.RGB:case ue.HEX:a.set("r",dummyNumberToken(255*e.channels[0])),a.set("g",dummyNumberToken(255*e.channels[1])),a.set("b",dummyNumberToken(255*e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case ue.HSL:a.set("h",dummyNumberToken(e.channels[0])),a.set("s",dummyNumberToken(e.channels[1])),a.set("l",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case ue.HWB:a.set("h",dummyNumberToken(e.channels[0])),a.set("w",dummyNumberToken(e.channels[1])),a.set("b",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case ue.Lab:case ue.OKLab:a.set("l",dummyNumberToken(e.channels[0])),a.set("a",dummyNumberToken(e.channels[1])),a.set("b",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case ue.LCH:case ue.OKLCH:a.set("l",dummyNumberToken(e.channels[0])),a.set("c",dummyNumberToken(e.channels[1])),a.set("h",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case ue.sRGB:case ue.A98_RGB:case ue.Display_P3:case ue.Rec2020:case ue.Linear_sRGB:case ue.ProPhoto_RGB:a.set("r",dummyNumberToken(e.channels[0])),a.set("g",dummyNumberToken(e.channels[1])),a.set("b",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha));break;case ue.XYZ_D50:case ue.XYZ_D65:a.set("x",dummyNumberToken(e.channels[0])),a.set("y",dummyNumberToken(e.channels[1])),a.set("z",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&a.set("alpha",dummyNumberToken(e.alpha))}return a}function noneToZeroInRelativeColorDataChannels(e){const a=new Map(e);for(const[n,r]of e)Number.isNaN(r[4].value)&&a.set(n,dummyNumberToken(0));return a}function dummyNumberToken(n){return Number.isNaN(n)?[e.Number,"none",-1,-1,{value:Number.NaN,type:a.Number}]:[e.Number,n.toString(),-1,-1,{value:n,type:a.Number}]}function reducePrecision(e,a=7){if(Number.isNaN(e))return 0;const n=Math.pow(10,a);return Math.round(e*n)/n}function colorDataFitsRGB_Gamut(e){const a={...e,channels:[...e.channels]};a.channels=convertPowerlessComponentsToZeroValuesForDisplay(a.channels,a.colorNotation);return!colorDataTo(a,ue.RGB).channels.find((e=>e<-1e-5||e>1.00001))}function colorDataFitsDisplayP3_Gamut(e){const a={...e,channels:[...e.channels]};a.channels=convertPowerlessComponentsToZeroValuesForDisplay(a.channels,a.colorNotation);return!colorDataTo(a,ue.Display_P3).channels.find((e=>e<-1e-5||e>1.00001))}function normalize(e,a,n,r){return Math.min(Math.max(e/a,n),r)}const he=/[A-Z]/g;function toLowerCaseAZ(e){return e.replace(he,(e=>String.fromCharCode(e.charCodeAt(0)+32)))}function normalize_Color_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ce.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(r(t)){3!==l&&s.syntaxFlags.add(ce.HasPercentageValues);let n=normalize(t[4].value,100,-2147483647,2147483647);return 3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ce.HasNumberValues);let n=normalize(t[4].value,1,-2147483647,2147483647);return 3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}const me=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]);function color$1(e,a){const r=[],s=[],u=[],c=[];let i,h,m=!1,p=!1;const N={colorNotation:ue.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([])};let b=r;for(let o=0;o<e.value.length;o++){let g=e.value[o];if(J(g)||Q(g))for(;J(e.value[o+1])||Q(e.value[o+1]);)o++;else if(b===r&&r.length&&(b=s),b===s&&s.length&&(b=u),ee(g)&&t(g.value)&&"/"===g.value[4].value){if(b===c)return!1;b=c}else{if(ae(g)){if(b===c&&"var"===toLowerCaseAZ(g.getName())){N.syntaxFlags.add(ce.HasVariableAlpha),b.push(g);continue}if(!le.has(toLowerCaseAZ(g.getName())))return!1;const[[e]]=se([[g]],{censorIntoStandardRepresentableValues:!0,globals:h,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!e||!ee(e)||!l(e.value))return!1;Number.isNaN(e.value[4].value)&&(e.value[4].value=0),g=e}if(b===r&&0===r.length&&ee(g)&&n(g.value)&&me.has(toLowerCaseAZ(g.value[4].value))){if(m)return!1;m=toLowerCaseAZ(g.value[4].value),N.colorNotation=colorSpaceNameToColorNotation(m),p&&(p.colorNotation!==N.colorNotation&&(p=colorDataTo(p,N.colorNotation)),i=normalizeRelativeColorDataChannels(p),h=noneToZeroInRelativeColorDataChannels(i))}else if(b===r&&0===r.length&&ee(g)&&n(g.value)&&"from"===toLowerCaseAZ(g.value[4].value)){if(p)return!1;if(m)return!1;for(;J(e.value[o+1])||Q(e.value[o+1]);)o++;if(o++,g=e.value[o],p=a(g),!1===p)return!1;p.syntaxFlags.has(ce.Experimental)&&N.syntaxFlags.add(ce.Experimental),N.syntaxFlags.add(ce.RelativeColorSyntax)}else{if(!ee(g))return!1;if(n(g.value)&&i&&i.has(toLowerCaseAZ(g.value[4].value))){b.push(new ne(i.get(toLowerCaseAZ(g.value[4].value))));continue}b.push(g)}}}if(!m)return!1;if(1!==b.length)return!1;if(1!==r.length||1!==s.length||1!==u.length)return!1;if(!ee(r[0])||!ee(s[0])||!ee(u[0]))return!1;if(i&&!i.has("alpha"))return!1;const g=normalize_Color_ChannelValues(r[0].value,0,N);if(!g||!o(g))return!1;const f=normalize_Color_ChannelValues(s[0].value,1,N);if(!f||!o(f))return!1;const v=normalize_Color_ChannelValues(u[0].value,2,N);if(!v||!o(v))return!1;const d=[g,f,v];if(1===c.length)if(N.syntaxFlags.add(ce.HasAlpha),ee(c[0])){const e=normalize_Color_ChannelValues(c[0].value,3,N);if(!e||!o(e))return!1;d.push(e)}else N.alpha=c[0];else if(i&&i.has("alpha")){const e=normalize_Color_ChannelValues(i.get("alpha"),3,N);if(!e||!o(e))return!1;d.push(e)}return N.channels=[d[0][4].value,d[1][4].value,d[2][4].value],4===d.length&&(N.alpha=d[3][4].value),N}function colorSpaceNameToColorNotation(e){switch(e){case"srgb":return ue.sRGB;case"srgb-linear":return ue.Linear_sRGB;case"display-p3":return ue.Display_P3;case"a98-rgb":return ue.A98_RGB;case"prophoto-rgb":return ue.ProPhoto_RGB;case"rec2020":return ue.Rec2020;case"xyz":case"xyz-d65":return ue.XYZ_D65;case"xyz-d50":return ue.XYZ_D50;default:throw new Error("Unknown color space name: "+e)}}const pe=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","lab","oklab","xyz","xyz-d50","xyz-d65"]),Ne=new Set(["hsl","hwb","lch","oklch"]),be=new Set(["shorter","longer","increasing","decreasing"]);function colorMix(e,a){let r=null,o=null,t=null,l=!1;for(let u=0;u<e.value.length;u++){const c=e.value[u];if(!re(c)){if(ee(c)&&n(c.value)){if(!r&&"in"===toLowerCaseAZ(c.value[4].value)){r=c;continue}if(r&&!o){o=toLowerCaseAZ(c.value[4].value);continue}if(r&&o&&!t&&Ne.has(o)){t=toLowerCaseAZ(c.value[4].value);continue}if(r&&o&&t&&!l&&"hue"===toLowerCaseAZ(c.value[4].value)){l=!0;continue}return!1}return!(!ee(c)||!s(c.value))&&(!!o&&(t||l?!!(o&&t&&l&&Ne.has(o)&&be.has(t))&&colorMixPolar(o,t,colorMixComponents(e.value.slice(u+1),a)):pe.has(o)?colorMixRectangular(o,colorMixComponents(e.value.slice(u+1),a)):!!Ne.has(o)&&colorMixPolar(o,"shorter",colorMixComponents(e.value.slice(u+1),a))))}}return!1}function colorMixComponents(e,a){const n=[];let o=1,t=!1,u=!1;for(let o=0;o<e.length;o++){let c=e[o];if(!re(c)){if(!ee(c)||!s(c.value)){if(!t){const e=a(c);if(e){t=e;continue}}if(!u){if(ae(c)&&le.has(toLowerCaseAZ(c.getName()))){if([[c]]=se([[c]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0}),!c||!ee(c)||!l(c.value))return!1;Number.isNaN(c.value[4].value)&&(c.value[4].value=0)}if(ee(c)&&r(c.value)&&c.value[4].value>=0){u=c.value[4].value;continue}}return!1}if(!t)return!1;n.push({color:t,percentage:u}),t=!1,u=!1}}t&&n.push({color:t,percentage:u});let c=0,i=0;for(let e=0;e<n.length;e++){const a=n[e].percentage;if(!1!==a){if(a<0||a>100)return!1;c+=a}else i++}const h=Math.max(0,100-c);c=0;for(let e=0;e<n.length;e++)!1===n[e].percentage&&(n[e].percentage=h/i),c+=n[e].percentage;if(0===c)return{colors:[{color:{channels:[0,0,0],colorNotation:ue.sRGB,alpha:0,syntaxFlags:new Set},percentage:0}],alphaMultiplier:0};if(c>100)for(let e=0;e<n.length;e++){let a=n[e].percentage;a=a/c*100,n[e].percentage=a}if(c<100){o=c/100;for(let e=0;e<n.length;e++){let a=n[e].percentage;a=a/c*100,n[e].percentage=a}}return{colors:n,alphaMultiplier:o}}function colorMixRectangular(e,a){if(!a||!a.colors.length)return!1;const n=a.colors.slice();n.reverse();let r=ue.RGB;switch(e){case"srgb":r=ue.RGB;break;case"srgb-linear":r=ue.Linear_sRGB;break;case"display-p3":r=ue.Display_P3;break;case"a98-rgb":r=ue.A98_RGB;break;case"prophoto-rgb":r=ue.ProPhoto_RGB;break;case"rec2020":r=ue.Rec2020;break;case"lab":r=ue.Lab;break;case"oklab":r=ue.OKLab;break;case"xyz-d50":r=ue.XYZ_D50;break;case"xyz":case"xyz-d65":r=ue.XYZ_D65;break;default:return!1}if(1===n.length){const e=colorDataTo(n[0].color,r);return e.colorNotation=r,e.syntaxFlags.add(ce.ColorMixVariadic),"number"!=typeof e.alpha?!1:(e.alpha=e.alpha*a.alphaMultiplier,e)}for(;n.length>=2;){const e=n.pop(),a=n.pop();if(!e||!a)return!1;const o=colorMixRectangularPair(r,e.color,e.percentage,a.color,a.percentage);if(!o)return!1;n.push({color:o,percentage:e.percentage+a.percentage})}const o=n[0]?.color;return!!o&&(a.colors.some((e=>e.color.syntaxFlags.has(ce.Experimental)))&&o.syntaxFlags.add(ce.Experimental),"number"==typeof o.alpha&&(o.alpha=o.alpha*a.alphaMultiplier,2!==a.colors.length&&o.syntaxFlags.add(ce.ColorMixVariadic),o))}function colorMixRectangularPair(e,a,n,r,o){const t=n/(n+o);let l=a.alpha;if("number"!=typeof l)return!1;let s=r.alpha;if("number"!=typeof s)return!1;l=Number.isNaN(l)?s:l,s=Number.isNaN(s)?l:s;const u=colorDataTo(a,e).channels,c=colorDataTo(r,e).channels;u[0]=fillInMissingComponent(u[0],c[0]),c[0]=fillInMissingComponent(c[0],u[0]),u[1]=fillInMissingComponent(u[1],c[1]),c[1]=fillInMissingComponent(c[1],u[1]),u[2]=fillInMissingComponent(u[2],c[2]),c[2]=fillInMissingComponent(c[2],u[2]),u[0]=premultiply(u[0],l),u[1]=premultiply(u[1],l),u[2]=premultiply(u[2],l),c[0]=premultiply(c[0],s),c[1]=premultiply(c[1],s),c[2]=premultiply(c[2],s);const i=interpolate(l,s,t);return{colorNotation:e,channels:[un_premultiply(interpolate(u[0],c[0],t),i),un_premultiply(interpolate(u[1],c[1],t),i),un_premultiply(interpolate(u[2],c[2],t),i)],alpha:i,syntaxFlags:new Set([ce.ColorMix])}}function colorMixPolar(e,a,n){if(!n||!n.colors.length)return!1;const r=n.colors.slice();r.reverse();let o=ue.HSL;switch(e){case"hsl":o=ue.HSL;break;case"hwb":o=ue.HWB;break;case"lch":o=ue.LCH;break;case"oklch":o=ue.OKLCH;break;default:return!1}if(1===r.length){const e=colorDataTo(r[0].color,o);return e.colorNotation=o,e.syntaxFlags.add(ce.ColorMixVariadic),"number"!=typeof e.alpha?!1:(e.alpha=e.alpha*n.alphaMultiplier,e)}for(;r.length>=2;){const e=r.pop(),n=r.pop();if(!e||!n)return!1;const t=colorMixPolarPair(o,a,e.color,e.percentage,n.color,n.percentage);if(!t)return!1;r.push({color:t,percentage:e.percentage+n.percentage})}const t=r[0]?.color;return!!t&&(n.colors.some((e=>e.color.syntaxFlags.has(ce.Experimental)))&&t.syntaxFlags.add(ce.Experimental),"number"==typeof t.alpha&&(t.alpha=t.alpha*n.alphaMultiplier,2!==n.colors.length&&t.syntaxFlags.add(ce.ColorMixVariadic),t))}function colorMixPolarPair(e,a,n,r,o,t){const l=r/(r+t);let s=0,u=0,c=0,i=0,h=0,m=0,p=n.alpha;if("number"!=typeof p)return!1;let N=o.alpha;if("number"!=typeof N)return!1;p=Number.isNaN(p)?N:p,N=Number.isNaN(N)?p:N;const b=colorDataTo(n,e).channels,g=colorDataTo(o,e).channels;switch(e){case ue.HSL:case ue.HWB:s=b[0],u=g[0],c=b[1],i=g[1],h=b[2],m=g[2];break;case ue.LCH:case ue.OKLCH:c=b[0],i=g[0],h=b[1],m=g[1],s=b[2],u=g[2]}s=fillInMissingComponent(s,u),Number.isNaN(s)&&(s=0),u=fillInMissingComponent(u,s),Number.isNaN(u)&&(u=0),c=fillInMissingComponent(c,i),i=fillInMissingComponent(i,c),h=fillInMissingComponent(h,m),m=fillInMissingComponent(m,h);const f=u-s;switch(a){case"shorter":f>180?s+=360:f<-180&&(u+=360);break;case"longer":-180<f&&f<180&&(f>0?s+=360:u+=360);break;case"increasing":f<0&&(u+=360);break;case"decreasing":f>0&&(s+=360);break;default:throw new Error("Unknown hue interpolation method")}c=premultiply(c,p),h=premultiply(h,p),i=premultiply(i,N),m=premultiply(m,N);let v=[0,0,0];const d=interpolate(p,N,l);switch(e){case ue.HSL:case ue.HWB:v=[interpolate(s,u,l),un_premultiply(interpolate(c,i,l),d),un_premultiply(interpolate(h,m,l),d)];break;case ue.LCH:case ue.OKLCH:v=[un_premultiply(interpolate(c,i,l),d),un_premultiply(interpolate(h,m,l),d),interpolate(s,u,l)]}return{colorNotation:e,channels:v,alpha:d,syntaxFlags:new Set([ce.ColorMix])}}function fillInMissingComponent(e,a){return Number.isNaN(e)?a:e}function interpolate(e,a,n){return e*n+a*(1-n)}function premultiply(e,a){return Number.isNaN(a)?e:Number.isNaN(e)?Number.NaN:e*a}function un_premultiply(e,a){return 0===a||Number.isNaN(a)?e:Number.isNaN(e)?Number.NaN:e/a}function hex(e){const a=toLowerCaseAZ(e[4].value);if(a.match(/[^a-f0-9]/))return!1;const n={colorNotation:ue.HEX,channels:[0,0,0],alpha:1,syntaxFlags:new Set([ce.Hex])},r=a.length;if(3===r){const e=a[0],r=a[1],o=a[2];return n.channels=[parseInt(e+e,16)/255,parseInt(r+r,16)/255,parseInt(o+o,16)/255],n}if(6===r){const e=a[0]+a[1],r=a[2]+a[3],o=a[4]+a[5];return n.channels=[parseInt(e,16)/255,parseInt(r,16)/255,parseInt(o,16)/255],n}if(4===r){const e=a[0],r=a[1],o=a[2],t=a[3];return n.channels=[parseInt(e+e,16)/255,parseInt(r+r,16)/255,parseInt(o+o,16)/255],n.alpha=parseInt(t+t,16)/255,n.syntaxFlags.add(ce.HasAlpha),n}if(8===r){const e=a[0]+a[1],r=a[2]+a[3],o=a[4]+a[5],t=a[6]+a[7];return n.channels=[parseInt(e,16)/255,parseInt(r,16)/255,parseInt(o,16)/255],n.alpha=parseInt(t,16)/255,n.syntaxFlags.add(ce.HasAlpha),n}return!1}function normalizeHue(n){if(o(n))return n[4].value=n[4].value%360,n[1]=n[4].value.toString(),n;if(u(n)){let r=n[4].value;switch(toLowerCaseAZ(n[4].unit)){case"deg":break;case"rad":r=180*n[4].value/Math.PI;break;case"grad":r=.9*n[4].value;break;case"turn":r=360*n[4].value;break;default:return!1}return r%=360,[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}return!1}function normalize_legacy_HSL_ChannelValues(n,t,l){if(0===t){const e=normalizeHue(n);return!1!==e&&(u(n)&&l.syntaxFlags.add(ce.HasDimensionValues),e)}if(r(n)){3===t?l.syntaxFlags.add(ce.HasPercentageAlpha):l.syntaxFlags.add(ce.HasPercentageValues);let r=normalize(n[4].value,1,0,100);return 3===t&&(r=normalize(n[4].value,100,0,1)),[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}if(o(n)){if(3!==t)return!1;let r=normalize(n[4].value,1,0,100);return 3===t&&(r=normalize(n[4].value,1,0,1)),[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}return!1}function normalize_modern_HSL_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ce.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(0===l){const e=normalizeHue(t);return!1!==e&&(u(t)&&s.syntaxFlags.add(ce.HasDimensionValues),e)}if(r(t)){3===l?s.syntaxFlags.add(ce.HasPercentageAlpha):s.syntaxFlags.add(ce.HasPercentageValues);let n=t[4].value;return 3===l?n=normalize(t[4].value,100,0,1):1===l&&(n=normalize(t[4].value,1,0,2147483647)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ce.HasNumberValues);let n=t[4].value;return 3===l?n=normalize(t[4].value,1,0,1):1===l&&(n=normalize(t[4].value,1,0,2147483647)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function threeChannelLegacySyntax(e,a,n,r){const t=[],u=[],c=[],i=[],h={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let m=t;for(let a=0;a<e.value.length;a++){let n=e.value[a];if(!J(n)&&!Q(n)){if(ee(n)&&s(n.value)){if(m===t){m=u;continue}if(m===u){m=c;continue}if(m===c){m=i;continue}if(m===i)return!1}if(ae(n)){if(m===i&&"var"===n.getName().toLowerCase()){h.syntaxFlags.add(ce.HasVariableAlpha),m.push(n);continue}if(!le.has(n.getName().toLowerCase()))return!1;const[[e]]=se([[n]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!e||!ee(e)||!l(e.value))return!1;Number.isNaN(e.value[4].value)&&(e.value[4].value=0),n=e}if(!ee(n))return!1;m.push(n)}}if(1!==m.length)return!1;if(1!==t.length||1!==u.length||1!==c.length)return!1;if(!ee(t[0])||!ee(u[0])||!ee(c[0]))return!1;const p=a(t[0].value,0,h);if(!p||!o(p))return!1;const N=a(u[0].value,1,h);if(!N||!o(N))return!1;const b=a(c[0].value,2,h);if(!b||!o(b))return!1;const g=[p,N,b];if(1===i.length)if(h.syntaxFlags.add(ce.HasAlpha),ee(i[0])){const e=a(i[0].value,3,h);if(!e||!o(e))return!1;g.push(e)}else h.alpha=i[0];return h.channels=[g[0][4].value,g[1][4].value,g[2][4].value],4===g.length&&(h.alpha=g[3][4].value),h}function threeChannelSpaceSeparated(e,a,r,s,u){const c=[],i=[],h=[],m=[];let p,N,b=!1;const g={colorNotation:r,channels:[0,0,0],alpha:1,syntaxFlags:new Set(s)};let f=c;for(let a=0;a<e.value.length;a++){let o=e.value[a];if(J(o)||Q(o))for(;J(e.value[a+1])||Q(e.value[a+1]);)a++;else if(f===c&&c.length&&(f=i),f===i&&i.length&&(f=h),ee(o)&&t(o.value)&&"/"===o.value[4].value){if(f===m)return!1;f=m}else{if(ae(o)){if(f===m&&"var"===o.getName().toLowerCase()){g.syntaxFlags.add(ce.HasVariableAlpha),f.push(o);continue}if(!le.has(o.getName().toLowerCase()))return!1;const[[e]]=se([[o]],{censorIntoStandardRepresentableValues:!0,globals:N,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!e||!ee(e)||!l(e.value))return!1;Number.isNaN(e.value[4].value)&&(e.value[4].value=0),o=e}if(f===c&&0===c.length&&ee(o)&&n(o.value)&&"from"===o.value[4].value.toLowerCase()){if(b)return!1;for(;J(e.value[a+1])||Q(e.value[a+1]);)a++;if(a++,o=e.value[a],b=u(o),!1===b)return!1;b.syntaxFlags.has(ce.Experimental)&&g.syntaxFlags.add(ce.Experimental),g.syntaxFlags.add(ce.RelativeColorSyntax),b.colorNotation!==r&&(b=colorDataTo(b,r)),p=normalizeRelativeColorDataChannels(b),N=noneToZeroInRelativeColorDataChannels(p)}else{if(!ee(o))return!1;if(n(o.value)&&p){const e=o.value[4].value.toLowerCase();if(p.has(e)){f.push(new ne(p.get(e)));continue}}f.push(o)}}}if(1!==f.length)return!1;if(1!==c.length||1!==i.length||1!==h.length)return!1;if(!ee(c[0])||!ee(i[0])||!ee(h[0]))return!1;if(p&&!p.has("alpha"))return!1;const v=a(c[0].value,0,g);if(!v||!o(v))return!1;const d=a(i[0].value,1,g);if(!d||!o(d))return!1;const y=a(h[0].value,2,g);if(!y||!o(y))return!1;const _=[v,d,y];if(1===m.length)if(g.syntaxFlags.add(ce.HasAlpha),ee(m[0])){const e=a(m[0].value,3,g);if(!e||!o(e))return!1;_.push(e)}else g.alpha=m[0];else if(p&&p.has("alpha")){const e=a(p.get("alpha"),3,g);if(!e||!o(e))return!1;_.push(e)}return g.channels=[_[0][4].value,_[1][4].value,_[2][4].value],4===_.length&&(g.alpha=_[3][4].value),g}function hsl(e,a){if(e.value.some((e=>ee(e)&&s(e.value)))){const a=hslCommaSeparated(e);if(!1!==a)return a}{const n=hslSpaceSeparated(e,a);if(!1!==n)return n}return!1}function hslCommaSeparated(e){return threeChannelLegacySyntax(e,normalize_legacy_HSL_ChannelValues,ue.HSL,[ce.LegacyHSL])}function hslSpaceSeparated(e,a){return threeChannelSpaceSeparated(e,normalize_modern_HSL_ChannelValues,ue.HSL,[],a)}function normalize_HWB_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ce.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(0===l){const e=normalizeHue(t);return!1!==e&&(u(t)&&s.syntaxFlags.add(ce.HasDimensionValues),e)}if(r(t)){3===l?s.syntaxFlags.add(ce.HasPercentageAlpha):s.syntaxFlags.add(ce.HasPercentageValues);let n=t[4].value;return 3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ce.HasNumberValues);let n=t[4].value;return 3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function normalize_Lab_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ce.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(r(t)){3!==l&&s.syntaxFlags.add(ce.HasPercentageValues);let n=normalize(t[4].value,1,0,100);return 1===l||2===l?n=normalize(t[4].value,.8,-2147483647,2147483647):3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ce.HasNumberValues);let n=normalize(t[4].value,1,0,100);return 1===l||2===l?n=normalize(t[4].value,1,-2147483647,2147483647):3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function lab(e,a){return threeChannelSpaceSeparated(e,normalize_Lab_ChannelValues,ue.Lab,[],a)}function normalize_LCH_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ce.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(2===l){const e=normalizeHue(t);return!1!==e&&(u(t)&&s.syntaxFlags.add(ce.HasDimensionValues),e)}if(r(t)){3!==l&&s.syntaxFlags.add(ce.HasPercentageValues);let n=normalize(t[4].value,1,0,100);return 1===l?n=normalize(t[4].value,100/150,0,2147483647):3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ce.HasNumberValues);let n=normalize(t[4].value,1,0,100);return 1===l?n=normalize(t[4].value,1,0,2147483647):3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function lch(e,a){return threeChannelSpaceSeparated(e,normalize_LCH_ChannelValues,ue.LCH,[],a)}const ge=new Map;for(const[e,a]of Object.entries(V))ge.set(e,a);function namedColor(e){const a=ge.get(toLowerCaseAZ(e));return!!a&&{colorNotation:ue.RGB,channels:[a[0]/255,a[1]/255,a[2]/255],alpha:1,syntaxFlags:new Set([ce.ColorKeyword,ce.NamedColor])}}function normalize_OKLab_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ce.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(r(t)){3!==l&&s.syntaxFlags.add(ce.HasPercentageValues);let n=normalize(t[4].value,100,0,1);return 1===l||2===l?n=normalize(t[4].value,250,-2147483647,2147483647):3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ce.HasNumberValues);let n=normalize(t[4].value,1,0,1);return 1===l||2===l?n=normalize(t[4].value,1,-2147483647,2147483647):3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function oklab(e,a){return threeChannelSpaceSeparated(e,normalize_OKLab_ChannelValues,ue.OKLab,[],a)}function normalize_OKLCH_ChannelValues(t,l,s){if(n(t)&&"none"===toLowerCaseAZ(t[4].value))return s.syntaxFlags.add(ce.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(2===l){const e=normalizeHue(t);return!1!==e&&(u(t)&&s.syntaxFlags.add(ce.HasDimensionValues),e)}if(r(t)){3!==l&&s.syntaxFlags.add(ce.HasPercentageValues);let n=normalize(t[4].value,100,0,1);return 1===l?n=normalize(t[4].value,250,0,2147483647):3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ce.HasNumberValues);let n=normalize(t[4].value,1,0,1);return 1===l?n=normalize(t[4].value,1,0,2147483647):3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function oklch(e,a){return threeChannelSpaceSeparated(e,normalize_OKLCH_ChannelValues,ue.OKLCH,[],a)}function normalize_legacy_sRGB_ChannelValues(n,t,l){if(r(n)){3===t?l.syntaxFlags.add(ce.HasPercentageAlpha):l.syntaxFlags.add(ce.HasPercentageValues);const r=normalize(n[4].value,100,0,1);return[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}if(o(n)){3!==t&&l.syntaxFlags.add(ce.HasNumberValues);let r=normalize(n[4].value,255,0,1);return 3===t&&(r=normalize(n[4].value,1,0,1)),[e.Number,r.toString(),n[2],n[3],{value:r,type:a.Number}]}return!1}function normalize_modern_sRGB_ChannelValues(t,l,s){if(n(t)&&"none"===t[4].value.toLowerCase())return s.syntaxFlags.add(ce.HasNoneKeywords),[e.Number,"none",t[2],t[3],{value:Number.NaN,type:a.Number}];if(r(t)){3!==l&&s.syntaxFlags.add(ce.HasPercentageValues);let n=normalize(t[4].value,100,-2147483647,2147483647);return 3===l&&(n=normalize(t[4].value,100,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}if(o(t)){3!==l&&s.syntaxFlags.add(ce.HasNumberValues);let n=normalize(t[4].value,255,-2147483647,2147483647);return 3===l&&(n=normalize(t[4].value,1,0,1)),[e.Number,n.toString(),t[2],t[3],{value:n,type:a.Number}]}return!1}function rgb(e,a){if(e.value.some((e=>ee(e)&&s(e.value)))){const a=rgbCommaSeparated(e);if(!1!==a)return(!a.syntaxFlags.has(ce.HasNumberValues)||!a.syntaxFlags.has(ce.HasPercentageValues))&&a}else{const n=rgbSpaceSeparated(e,a);if(!1!==n)return n}return!1}function rgbCommaSeparated(e){return threeChannelLegacySyntax(e,normalize_legacy_sRGB_ChannelValues,ue.RGB,[ce.LegacyRGB])}function rgbSpaceSeparated(e,a){return threeChannelSpaceSeparated(e,normalize_modern_sRGB_ChannelValues,ue.RGB,[],a)}function XYZ_D50_to_sRGB_Gamut(e){const a=w(e);if(G(a))return T(a);let n=e;return n=p(n),n[0]<1e-6&&(n=[0,0,0]),n[0]>.999999&&(n=[1,0,0]),A(X(n,oklch_to_lin_srgb,lin_srgb_to_oklch))}function oklch_to_lin_srgb(e){return e=K(e),e=Y(e),I(e)}function lin_srgb_to_oklch(e){return e=O(e),e=W(e),E(e)}function contrastColor(e,a){let n=!1;for(let r=0;r<e.value.length;r++){const o=e.value[r];if(!J(o)&&!Q(o)&&(n||(n=a(o),!n)))return!1}if(!n)return!1;n.channels=convertNaNToZero(n.channels),n.channels=XYZ_D50_to_sRGB_Gamut(colorData_to_XYZ_D50(n).channels),n.colorNotation=ue.sRGB;const r={colorNotation:ue.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([ce.ContrastColor,ce.Experimental])},o=U(n.channels,[1,1,1]),t=U(n.channels,[0,0,0]);return r.channels=o>t?[1,1,1]:[0,0,0],r}function XYZ_D50_to_P3_Gamut(e){const a=_(e);if(G(a))return T(a);let n=e;return n=p(n),n[0]<1e-6&&(n=[0,0,0]),n[0]>.999999&&(n=[1,0,0]),$(X(n,oklch_to_lin_p3,lin_p3_to_oklch))}function oklch_to_lin_p3(e){return e=K(e),e=Y(e),j(e)}function lin_p3_to_oklch(e){return e=q(e),e=W(e),E(e)}function toPrecision(e,a=7){e=+e,a=+a;const n=(Math.floor(Math.abs(e))+"").length;if(a>n)return+e.toFixed(a-n);{const r=10**(n-a);return Math.round(e/r)*r}}function serializeWithAlpha(n,r,o,t){const l=[e.CloseParen,")",-1,-1,void 0];if("number"==typeof n.alpha){const s=Math.min(1,Math.max(0,toPrecision(Number.isNaN(n.alpha)?0:n.alpha)));return 1===toPrecision(s,4)?new oe(r,l,t):new oe(r,l,[...t,new te([o]),new ne([e.Delim,"/",-1,-1,{value:"/"}]),new te([o]),new ne([e.Number,toPrecision(s,4).toString(),-1,-1,{value:n.alpha,type:a.Integer}])])}return new oe(r,l,[...t,new te([o]),new ne([e.Delim,"/",-1,-1,{value:"/"}]),new te([o]),n.alpha])}function serializeP3(n,r=!0){n.channels=convertPowerlessComponentsToZeroValuesForDisplay(n.channels,n.colorNotation);let o=n.channels.map((e=>Number.isNaN(e)?0:e));r?o=XYZ_D50_to_P3_Gamut(colorData_to_XYZ_D50(n).channels):n.colorNotation!==ue.Display_P3&&(o=_(colorData_to_XYZ_D50(n).channels));const t=r?Math.min(1,Math.max(0,toPrecision(o[0],6))):toPrecision(o[0],6),l=r?Math.min(1,Math.max(0,toPrecision(o[1],6))):toPrecision(o[1],6),s=r?Math.min(1,Math.max(0,toPrecision(o[2],6))):toPrecision(o[2],6),u=[e.Function,"color(",-1,-1,{value:"color"}],c=[e.Whitespace," ",-1,-1,void 0];return serializeWithAlpha(n,u,c,[new ne([e.Ident,"display-p3",-1,-1,{value:"display-p3"}]),new te([c]),new ne([e.Number,t.toString(),-1,-1,{value:o[0],type:a.Number}]),new te([c]),new ne([e.Number,l.toString(),-1,-1,{value:o[1],type:a.Number}]),new te([c]),new ne([e.Number,s.toString(),-1,-1,{value:o[2],type:a.Number}])])}function serializeRGB(n,r=!0){n.channels=convertPowerlessComponentsToZeroValuesForDisplay(n.channels,n.colorNotation);let o=n.channels.map((e=>Number.isNaN(e)?0:e));o=r?XYZ_D50_to_sRGB_Gamut(colorData_to_XYZ_D50(n).channels):w(colorData_to_XYZ_D50(n).channels);const t=Math.min(255,Math.max(0,Math.round(255*toPrecision(o[0])))),l=Math.min(255,Math.max(0,Math.round(255*toPrecision(o[1])))),s=Math.min(255,Math.max(0,Math.round(255*toPrecision(o[2])))),u=[e.CloseParen,")",-1,-1,void 0],c=[e.Whitespace," ",-1,-1,void 0],i=[e.Comma,",",-1,-1,void 0],h=[new ne([e.Number,t.toString(),-1,-1,{value:Math.min(255,255*Math.max(0,o[0])),type:a.Integer}]),new ne(i),new te([c]),new ne([e.Number,l.toString(),-1,-1,{value:Math.min(255,255*Math.max(0,o[1])),type:a.Integer}]),new ne(i),new te([c]),new ne([e.Number,s.toString(),-1,-1,{value:Math.min(255,255*Math.max(0,o[2])),type:a.Integer}])];if("number"==typeof n.alpha){const r=Math.min(1,Math.max(0,toPrecision(Number.isNaN(n.alpha)?0:n.alpha)));return 1===toPrecision(r,4)?new oe([e.Function,"rgb(",-1,-1,{value:"rgb"}],u,h):new oe([e.Function,"rgba(",-1,-1,{value:"rgba"}],u,[...h,new ne(i),new te([c]),new ne([e.Number,toPrecision(r,4).toString(),-1,-1,{value:n.alpha,type:a.Number}])])}return new oe([e.Function,"rgba(",-1,-1,{value:"rgba"}],u,[...h,new ne(i),new te([c]),n.alpha])}function serializeHSL(n,r=!0){n.channels=convertPowerlessComponentsToZeroValuesForDisplay(n.channels,n.colorNotation);let o=n.channels.map((e=>Number.isNaN(e)?0:e));o=f(r?B(XYZ_D50_to_sRGB_Gamut(colorData_to_XYZ_D50(n).channels)):colorData_to_XYZ_D50(n).channels),o=o.map((e=>Number.isNaN(e)?0:e));const t=Math.min(360,Math.max(0,Math.round(toPrecision(o[0])))),l=Math.min(100,Math.max(0,Math.round(toPrecision(o[1])))),s=Math.min(100,Math.max(0,Math.round(toPrecision(o[2])))),u=[e.CloseParen,")",-1,-1,void 0],c=[e.Whitespace," ",-1,-1,void 0],i=[e.Comma,",",-1,-1,void 0],h=[new ne([e.Number,t.toString(),-1,-1,{value:o[0],type:a.Integer}]),new ne(i),new te([c]),new ne([e.Percentage,l.toString()+"%",-1,-1,{value:o[1]}]),new ne(i),new te([c]),new ne([e.Percentage,s.toString()+"%",-1,-1,{value:o[2]}])];if("number"==typeof n.alpha){const r=Math.min(1,Math.max(0,toPrecision(Number.isNaN(n.alpha)?0:n.alpha)));return 1===toPrecision(r,4)?new oe([e.Function,"hsl(",-1,-1,{value:"hsl"}],u,h):new oe([e.Function,"hsla(",-1,-1,{value:"hsla"}],u,[...h,new ne(i),new te([c]),new ne([e.Number,toPrecision(r,4).toString(),-1,-1,{value:n.alpha,type:a.Number}])])}return new oe([e.Function,"hsla(",-1,-1,{value:"hsla"}],u,[...h,new ne(i),new te([c]),n.alpha])}function serializeOKLCH(n){n.channels=convertPowerlessComponentsToZeroValuesForDisplay(n.channels,n.colorNotation);let r=n.channels.map((e=>Number.isNaN(e)?0:e));n.colorNotation!==ue.OKLCH&&(r=p(colorData_to_XYZ_D50(n).channels));const o=toPrecision(r[0],6),t=toPrecision(r[1],6),l=toPrecision(r[2],6),s=[e.Function,"oklch(",-1,-1,{value:"oklch"}],u=[e.Whitespace," ",-1,-1,void 0];return serializeWithAlpha(n,s,u,[new ne([e.Number,o.toString(),-1,-1,{value:r[0],type:a.Number}]),new te([u]),new ne([e.Number,t.toString(),-1,-1,{value:r[1],type:a.Number}]),new te([u]),new ne([e.Number,l.toString(),-1,-1,{value:r[2],type:a.Number}])])}function color(e){if(ae(e)){switch(toLowerCaseAZ(e.getName())){case"rgb":case"rgba":return rgb(e,color);case"hsl":case"hsla":return hsl(e,color);case"hwb":return a=color,threeChannelSpaceSeparated(e,normalize_HWB_ChannelValues,ue.HWB,[],a);case"lab":return lab(e,color);case"lch":return lch(e,color);case"oklab":return oklab(e,color);case"oklch":return oklch(e,color);case"color":return color$1(e,color);case"color-mix":return colorMix(e,color);case"contrast-color":return contrastColor(e,color)}}var a;if(ee(e)){if(c(e.value))return hex(e.value);if(n(e.value)){const a=namedColor(e.value[4].value);return!1!==a?a:"transparent"===toLowerCaseAZ(e.value[4].value)&&{colorNotation:ue.RGB,channels:[0,0,0],alpha:0,syntaxFlags:new Set([ce.ColorKeyword])}}}return!1}export{ue as ColorNotation,ce as SyntaxFlag,color,colorDataFitsDisplayP3_Gamut,colorDataFitsRGB_Gamut,serializeHSL,serializeOKLCH,serializeP3,serializeRGB};
