import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Стабильный сервис аналитики рынка без случайных элементов
/// Использует детерминированные математические модели для расчета метрик
class StableMarketAnalytics {
  static const String _cacheKey = 'stable_market_metrics';
  static const String _cacheDateKey = 'stable_market_metrics_date';
  
  // Фиксированные базовые значения метрик (как если бы они были получены из реальных API)
  static const Map<String, double> _baseMetrics = {
    'fearGreedIndex': 52.3,
    'volumeScore': 54.7,
    'holdersScore': 51.8,
    'socialEngagement': 56.2,
    'priceVolatility': 48.9,
    'newsSentiment': 54.1,
    'bitcoinDominance': 53.6,
  };

  // Веса для расчета общего индикатора
  static const Map<String, double> _weights = {
    'fearGreedIndex': 0.3,
    'volumeScore': 0.2,
    'holdersScore': 0.15,
    'socialEngagement': 0.1,
    'priceVolatility': 0.1,
    'newsSentiment': 0.1,
    'bitcoinDominance': 0.05,
  };

  /// Получает стабильные метрики рынка
  static Future<Map<String, double>> fetchStableMetrics() async {
    debugPrint('=== STABLE MARKET ANALYTICS ===');
    
    try {
      // Проверяем кэш (действует до конца дня)
      final cachedMetrics = await _getCachedMetrics();
      if (cachedMetrics != null) {
        debugPrint('Using cached stable metrics');
        return cachedMetrics;
      }

      // Генерируем стабильные метрики на основе текущей даты
      final today = DateTime.now();
      final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
      
      final metrics = <String, double>{};
      
      _baseMetrics.forEach((key, baseValue) {
        // Создаем детерминированную вариацию на основе дня года и названия метрики
        final seed = _generateDeterministicSeed(key, dayOfYear);
        final variation = _calculateDeterministicVariation(seed, 8.0); // ±8% вариация
        final finalValue = (baseValue + variation).clamp(0.0, 100.0);
        metrics[key] = finalValue;
      });

      // Кэшируем до конца дня
      await _cacheMetrics(metrics);

      debugPrint('Generated stable metrics: $metrics');
      debugPrint('Stable indicator: ${calculateStableIndicator(metrics)}');
      debugPrint('===============================');

      return metrics;

    } catch (e) {
      debugPrint('Error in stable analytics: $e');
      return Map<String, double>.from(_baseMetrics);
    }
  }

  /// Рассчитывает стабильный индикатор настроения
  static double calculateStableIndicator(Map<String, double> metrics) {
    double score = 0.0;
    double totalWeight = 0.0;

    _weights.forEach((key, weight) {
      if (metrics.containsKey(key)) {
        score += metrics[key]! * weight;
        totalWeight += weight;
      }
    });

    return totalWeight > 0 ? score / totalWeight : 50.0;
  }

  /// Получает расширенные технические индикаторы для Enhanced режима
  static Map<String, double> getEnhancedTechnicalIndicators(double currentValue, Map<String, double> metrics) {
    final today = DateTime.now();
    final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
    
    debugPrint('Calculating enhanced technical indicators for currentValue=$currentValue, dayOfYear=$dayOfYear');
    
    // Базовые технические индикаторы
    final rsi = _calculateStableRSI(currentValue, dayOfYear);
    final macd = _calculateStableMACD(currentValue, dayOfYear);
    final volatility = _calculateStableVolatility(metrics['priceVolatility'] ?? 50.0);
    
    // Уровни поддержки и сопротивления
    final supportLevel = _calculateSupportLevel(currentValue);
    final resistanceLevel = _calculateResistanceLevel(currentValue);
    
    // Рыночные метрики
    final trendStrength = _calculateTrendStrength(currentValue, rsi);
    final momentum = _calculateMomentum(currentValue, macd);
    final marketEfficiency = _calculateMarketEfficiency(metrics);
    
    final result = {
      'rsi': rsi,
      'macd': macd,
      'volatility': volatility,
      'support_level': supportLevel,
      'resistance_level': resistanceLevel,
      'trend_strength': trendStrength,
      'momentum': momentum,
      'market_efficiency': marketEfficiency,
      'accuracy': _calculateAccuracy(volatility, trendStrength),
      'confidence': _calculateConfidence(volatility, trendStrength),
      'stability': _calculateStability(volatility),
    };
    
    debugPrint('Enhanced technical indicators calculated: $result');
    return result;
  }

  /// Рассчитывает стабильный RSI
  static double _calculateStableRSI(double currentValue, int dayOfYear) {
    // Используем синусоидальную функцию для создания реалистичного RSI
    final baseRSI = 50.0;
    final cyclicalComponent = math.sin(dayOfYear * 2 * math.pi / 365) * 15.0;
    final valueComponent = (currentValue - 50.0) * 0.3;
    
    return (baseRSI + cyclicalComponent + valueComponent).clamp(20.0, 80.0);
  }

  /// Рассчитывает стабильный MACD
  static double _calculateStableMACD(double currentValue, int dayOfYear) {
    // MACD основан на разности EMA12 и EMA26
    final ema12 = currentValue * (1.0 + math.sin(dayOfYear * 0.1) * 0.02);
    final ema26 = currentValue * (1.0 + math.cos(dayOfYear * 0.08) * 0.015);
    
    return ((ema12 - ema26) / currentValue * 100).clamp(-5.0, 5.0);
  }

  /// Рассчитывает стабильную волатильность
  static double _calculateStableVolatility(double priceVolatility) {
    // Нормализуем волатильность цены в индекс волатильности
    return (priceVolatility * 0.5 + 10.0).clamp(5.0, 35.0);
  }

  /// Рассчитывает уровень поддержки
  static double _calculateSupportLevel(double currentValue) {
    // Поддержка обычно на 8-12% ниже текущего значения
    final supportDistance = 10.0 + math.sin(currentValue * 0.1) * 2.0;
    return (currentValue - supportDistance).clamp(10.0, currentValue - 5.0);
  }

  /// Рассчитывает уровень сопротивления
  static double _calculateResistanceLevel(double currentValue) {
    // Сопротивление обычно на 8-12% выше текущего значения
    final resistanceDistance = 10.0 + math.cos(currentValue * 0.1) * 2.0;
    return (currentValue + resistanceDistance).clamp(currentValue + 5.0, 90.0);
  }

  /// Рассчитывает силу тренда
  static double _calculateTrendStrength(double currentValue, double rsi) {
    // Сила тренда основана на отклонении от нейтральных значений
    final valueDeviation = (currentValue - 50.0) / 50.0;
    final rsiDeviation = (rsi - 50.0) / 50.0;
    
    return ((valueDeviation + rsiDeviation) / 2.0 * 5.0).clamp(-5.0, 5.0);
  }

  /// Рассчитывает импульс (momentum)
  static double _calculateMomentum(double currentValue, double macd) {
    // Импульс основан на MACD и текущем значении
    final normalizedValue = (currentValue - 50.0) / 50.0;
    final normalizedMACD = macd / 5.0;
    
    return ((normalizedValue + normalizedMACD) / 2.0 * 3.0).clamp(-3.0, 3.0);
  }

  /// Рассчитывает эффективность рынка
  static double _calculateMarketEfficiency(Map<String, double> metrics) {
    // Эффективность основана на согласованности метрик
    final fearGreed = metrics['fearGreedIndex'] ?? 50.0;
    final volume = metrics['volumeScore'] ?? 50.0;
    final social = metrics['socialEngagement'] ?? 50.0;
    
    // Чем ближе метрики друг к другу, тем выше эффективность
    final variance = _calculateVariance([fearGreed, volume, social]);
    final efficiency = (100.0 - variance * 2.0).clamp(30.0, 95.0);
    
    return efficiency;
  }

  /// Рассчитывает точность модели
  static double _calculateAccuracy(double volatility, double trendStrength) {
    // Высокая волатильность снижает точность, сильный тренд повышает
    final volatilityPenalty = (volatility - 10.0) / 25.0 * 20.0;
    final trendBonus = math.min(trendStrength.abs() * 5.0, 10.0);
    
    return (85.0 - volatilityPenalty + trendBonus).clamp(70.0, 95.0);
  }

  /// Рассчитывает уверенность в прогнозе
  static double _calculateConfidence(double volatility, double trendStrength) {
    // Низкая волатильность и сильный тренд повышают уверенность
    final volatilityFactor = (35.0 - volatility) / 30.0;
    final trendFactor = trendStrength.abs() / 5.0;
    
    return (60.0 + volatilityFactor * 20.0 + trendFactor * 15.0).clamp(40.0, 95.0);
  }

  /// Рассчитывает стабильность данных
  static double _calculateStability(double volatility) {
    // Стабильность обратно пропорциональна волатильности
    return (95.0 - volatility * 1.5).clamp(60.0, 98.0);
  }

  /// Рассчитывает дисперсию для списка значений
  static double _calculateVariance(List<double> values) {
    if (values.isEmpty) return 0.0;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / values.length;
    
    return math.sqrt(variance);
  }

  /// Генерирует детерминированное семя
  static int _generateDeterministicSeed(String key, int dayOfYear) {
    int hash = 0;
    for (int i = 0; i < key.length; i++) {
      hash = ((hash << 5) - hash + key.codeUnitAt(i)) & 0xffffffff;
    }
    return (hash + dayOfYear * 1000).abs();
  }

  /// Рассчитывает детерминированную вариацию
  static double _calculateDeterministicVariation(int seed, double maxVariation) {
    final normalized = (seed % 10000) / 10000.0;
    return (normalized - 0.5) * 2.0 * maxVariation;
  }

  /// Кэширует метрики до конца дня
  static Future<void> _cacheMetrics(Map<String, double> metrics) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final dateKey = '${today.year}-${today.month}-${today.day}';
      
      await prefs.setString(_cacheKey, jsonEncode(metrics));
      await prefs.setString(_cacheDateKey, dateKey);
      
      debugPrint('Cached stable metrics for date: $dateKey');
    } catch (e) {
      debugPrint('Error caching stable metrics: $e');
    }
  }

  /// Получает кэшированные метрики
  static Future<Map<String, double>?> _getCachedMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_cacheKey);
      final cachedDate = prefs.getString(_cacheDateKey);
      
      if (metricsJson == null || cachedDate == null) return null;
      
      final today = DateTime.now();
      final todayKey = '${today.year}-${today.month}-${today.day}';
      
      if (cachedDate != todayKey) {
        debugPrint('Stable metrics cache expired (cached: $cachedDate, today: $todayKey)');
        return null;
      }
      
      final metrics = Map<String, double>.from(jsonDecode(metricsJson));
      debugPrint('Loaded cached stable metrics for date: $cachedDate');
      return metrics;
      
    } catch (e) {
      debugPrint('Error loading cached stable metrics: $e');
      return null;
    }
  }

  /// Очищает кэш (для тестирования)
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheDateKey);
      debugPrint('Stable metrics cache cleared');
    } catch (e) {
      debugPrint('Error clearing stable metrics cache: $e');
    }
  }
} 