import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Добавляем импорт для HapticFeedback
import 'package:flutter_svg/flutter_svg.dart'; // Для SVG иконок
import 'icons/category_icons.dart'; // Наши кастомные SVG иконки
import 'package:finance_ai/models/sentiment_types.dart';

/// Виджет топбара для категорий новостей с анимированным эффектом при нажатии
class NewsCategoriesDock extends StatefulWidget {
  final Function(String) onCategoryChanged;
  final String selectedCategory;
  final Color selectedColor;
  final Color unselectedColor;
  final double itemSpacing;
  final double height;
  
  const NewsCategoriesDock({
    Key? key,
    required this.onCategoryChanged,
    this.selectedCategory = 'all',
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.itemSpacing = 16.0,
    this.height = 56.0,
  }) : super(key: key);

  @override
  State<NewsCategoriesDock> createState() => _NewsCategoriesDockState();
}

class _NewsCategoriesDockState extends State<NewsCategoriesDock> with SingleTickerProviderStateMixin {
  String? _hoveredCategory;
  late AnimationController _animationController;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildCategoryItem(
                'all', 
                'All News',
                Icons.public,
              ),
              SizedBox(width: widget.itemSpacing),
              _buildCategoryItem(
                'crypto', 
                'Cryptocurrencies',
                Icons.trending_up,
              ),
              SizedBox(width: widget.itemSpacing),
              _buildCategoryItem(
                'stock', 
                'Stocks',
                Icons.show_chart,
              ),
              SizedBox(width: widget.itemSpacing),
              _buildCategoryItem(
                'whales', 
                'Whales',
                Icons.water,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryItem(String category, String label, IconData icon) {
    final bool isSelected = widget.selectedCategory == category;
    final bool isHovered = _hoveredCategory == category;
    
    return GestureDetector(
      onTap: () => widget.onCategoryChanged(category),
      child: MouseRegion(
        onEnter: (_) => setState(() {
          _hoveredCategory = category;
          _animationController.forward();
        }),
        onExit: (_) => setState(() {
          _hoveredCategory = null;
          _animationController.reverse();
        }),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          transform: Matrix4.identity()
            ..scale(isHovered || isSelected ? 1.1 : 1.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? widget.selectedColor : widget.unselectedColor,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? widget.selectedColor : widget.unselectedColor,
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Константы для док-панели
const double kDefaultDockHeight = 50.0;  // Высота панели
const double kDefaultDockWidth = 320.0;  // Ширина панели
const double kExpandedDockWidth = 383.0;  // Расширенная ширина (увеличено на 3px)
const double kDefaultIconSize = 20.0;  // Размер иконок
const double kHoverIconSize = 18.0;    // Размер иконок при наведении
const double kHoverDistance = 80.0;    // Радиус действия
const Duration kAnimationDuration = Duration(milliseconds: 200);  // Анимация

/// Более продвинутая версия с анимацией в стиле док-панели MacOS
class AnimatedNewsCategoriesDock extends StatefulWidget {
  final Function(String) onCategoryChanged;
  final String selectedCategory;
  final Color selectedColor;
  final Color unselectedColor;
  final double itemSpacing;
  final double height;
  final double defaultWidth;
  final double expandedWidth;
  final bool useGlow;
  final List<Color> gradientColors;
  final double shrinkFactor;
  
  const AnimatedNewsCategoriesDock({
    Key? key,
    required this.onCategoryChanged,
    this.selectedCategory = 'all',
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.itemSpacing = 8.0,
    this.height = kDefaultDockHeight,
    this.defaultWidth = kDefaultDockWidth,
    this.expandedWidth = kExpandedDockWidth,
    this.useGlow = true,
    this.gradientColors = const [Color(0xFF1A1A1A), Color(0xFF303030)],
    this.shrinkFactor = 0.9,
  }) : super(key: key);

  @override
  State<AnimatedNewsCategoriesDock> createState() => _AnimatedNewsCategoriesDockState();
}

class _AnimatedNewsCategoriesDockState extends State<AnimatedNewsCategoriesDock> with SingleTickerProviderStateMixin {
  String? _hoveredCategory;
  bool _isDockHovered = false;
  Offset? _mousePosition;
  
  // Анимация расширения/сжатия ширины дока
  late AnimationController _widthController;
  late Animation<double> _widthAnimation;
  late Animation<double> _glowAnimation;
  
  // Ключи для каждого элемента, чтобы иметь доступ к их позициям
  final Map<String, GlobalKey> _keys = {
    'all': GlobalKey(),
    'crypto': GlobalKey(),
    'stock': GlobalKey(),
    'whales': GlobalKey(),
  };
  
  @override
  void initState() {
    super.initState();
    
    _widthController = AnimationController(
      vsync: this,
      duration: kAnimationDuration,
    );
    
    _widthAnimation = Tween<double>(
      begin: widget.defaultWidth,
      end: widget.expandedWidth,
    ).animate(CurvedAnimation(
      parent: _widthController, 
      curve: Curves.easeOutCubic,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _widthController, 
      curve: Curves.easeOutCubic,
    ));
  }
  
  @override
  void dispose() {
    _widthController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: MouseRegion(
        onEnter: (_) {
          setState(() {
            _isDockHovered = true;
          });
          _widthController.forward();
        },
        onHover: (event) {
          setState(() {
            _mousePosition = event.position;
          });
        },
        onExit: (_) {
          setState(() {
            _mousePosition = null;
            _isDockHovered = false;
          });
          _widthController.reverse();
        },
        child: RepaintBoundary(
          child: ClipRect(
            child: AnimatedBuilder(
              animation: Listenable.merge([_widthAnimation, _glowAnimation]),
              builder: (context, child) {
                return Container(
                  height: widget.height + 8,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: widget.gradientColors,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                      if (widget.useGlow) BoxShadow(
                        color: widget.selectedColor.withOpacity(0.2 * _glowAnimation.value),
                        blurRadius: 10,
                        spreadRadius: 1 * _glowAnimation.value,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      children: [
                        _buildAnimatedCategoryItem(
                          'all', 
                          'All News',
                          Icons.public,
                        ),
                        SizedBox(width: widget.itemSpacing),
                        _buildAnimatedCategoryItem(
                          'crypto', 
                          'Cryptocurrencies',
                          CryptoIcon(
                            size: kDefaultIconSize,
                            color: widget.selectedCategory == 'crypto'
                              ? widget.selectedColor
                              : widget.unselectedColor,
                          ),
                        ),
                        SizedBox(width: widget.itemSpacing),
                        _buildAnimatedCategoryItem(
                          'stock', 
                          'Stocks',
                          StockMarketIcon(
                            size: kDefaultIconSize,
                            color: widget.selectedCategory == 'stock'
                              ? widget.selectedColor
                              : widget.unselectedColor,
                          ),
                        ),
                        SizedBox(width: widget.itemSpacing),
                        _buildAnimatedCategoryItem(
                          'whales', 
                          'Whales',
                          WhaleIcon(
                            size: kDefaultIconSize,
                            color: widget.selectedCategory == 'whales'
                              ? widget.selectedColor
                              : widget.unselectedColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedCategoryItem(String category, String label, dynamic icon) {
    final bool isSelected = widget.selectedCategory == category;
    final key = _keys[category]!;
    final bool isHovered = _hoveredCategory == category;
    final double colorIntensity = isSelected ? 1.0 : (isHovered ? 0.95 : 0.7);
    final Color itemColor = isSelected ? widget.selectedColor : widget.unselectedColor;
    final bool showText = (_widthAnimation.value >= widget.expandedWidth - 0.1);

    if (!showText) {
      // Только иконка, без текста и без фиксированной ширины
      return GestureDetector(
        key: key,
        onTap: () => widget.onCategoryChanged(category),
        child: MouseRegion(
          onEnter: (_) => setState(() => _hoveredCategory = category),
          onExit: (_) => setState(() => _hoveredCategory = null),
          child: Container(
            alignment: Alignment.center,
            child: FittedBox(
              fit: BoxFit.contain,
              child: icon is IconData
                  ? Icon(icon, color: itemColor.withOpacity(colorIntensity), size: kDefaultIconSize)
                  : icon,
            ),
          ),
        ),
      );
    }

    // Панель раскрыта — показываем иконку + текст
    return GestureDetector(
      key: key,
      onTap: () => widget.onCategoryChanged(category),
      child: MouseRegion(
        onEnter: (_) => setState(() => _hoveredCategory = category),
        onExit: (_) => setState(() => _hoveredCategory = null),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FittedBox(
              fit: BoxFit.contain,
              child: icon is IconData
                  ? Icon(icon, color: itemColor.withOpacity(colorIntensity), size: kDefaultIconSize)
                  : icon,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  label,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                    color: itemColor.withOpacity(colorIntensity),
                    fontWeight: isSelected ? FontWeight.bold : isHovered ? FontWeight.w600 : FontWeight.normal,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
