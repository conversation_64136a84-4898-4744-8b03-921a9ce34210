import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_web/webview_flutter_web.dart';
import '../models/candle.dart';

class TradingViewChart extends StatefulWidget {
  final List<Candle> candles;
  final double? entryPrice;
  final bool showEntryPoint;
  final int visibleFutureCandles;
  final bool showFutureCandles;
  final VoidCallback? onAnimationComplete;

  const TradingViewChart({
    super.key,
    required this.candles,
    this.entryPrice,
    this.showEntryPoint = false,
    this.visibleFutureCandles = 0,
    this.showFutureCandles = false,
    this.onAnimationComplete,
  });

  @override
  State<TradingViewChart> createState() => _TradingViewChartState();
}

class _TradingViewChartState extends State<TradingViewChart> {
  late WebViewController _controller;
  bool _isWebViewReady = false;

  @override
  void initState() {
    super.initState();
    _initController();
  }

  void _initController() {
    // Создаем контроллер WebView
    _controller = WebViewController();

    // Настраиваем контроллер с проверкой платформы
    if (!kIsWeb) {
      _controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    }

    _controller
      ..setBackgroundColor(Colors.transparent)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            developer.log('WebView page loaded: $url', name: 'TradingViewChart');

            // Проверяем, что JavaScript-функции доступны
            _controller.runJavaScript('''
              console.log("Checking if chart is initialized");
              if (typeof initChart === 'function') {
                console.log("initChart function found");
                if (!chart) {
                  console.log("Chart not initialized, initializing now");
                  initChart();
                }
              } else {
                console.error("initChart function not found");
              }
            ''').then((_) {
              setState(() {
                _isWebViewReady = true;
              });
              _updateChart();
            }).catchError((error) {
              developer.log('Error checking chart initialization: $error',
                name: 'TradingViewChart', error: error);
            });
          },
          onWebResourceError: (WebResourceError error) {
            developer.log(
              'WebView error: ${error.description}',
              name: 'TradingViewChart',
              error: error,
            );
          },
        ),
      );

    // Добавляем обработчик сообщений от JavaScript
    _controller.addJavaScriptChannel(
      'Flutter',
      onMessageReceived: (JavaScriptMessage message) {
        developer.log('Received message from JavaScript: ${message.message}',
          name: 'TradingViewChart');

        if (message.message == 'animationComplete' && widget.onAnimationComplete != null) {
          widget.onAnimationComplete!();
        }
      },
    );

    // Загружаем HTML-файл
    _controller.loadFlutterAsset('assets/tradingview_chart.html');
  }

  @override
  void didUpdateWidget(TradingViewChart oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update chart when props change
    if (_isWebViewReady &&
        (widget.candles != oldWidget.candles ||
        widget.entryPrice != oldWidget.entryPrice ||
        widget.showEntryPoint != oldWidget.showEntryPoint ||
        widget.showFutureCandles != oldWidget.showFutureCandles ||
        widget.visibleFutureCandles != oldWidget.visibleFutureCandles)) {
      _updateChart();
    }
  }

  void _updateChart() {
    if (!_isWebViewReady) return;

    try {
      // Convert candles to a format that can be sent to the WebView
      final candlesData = widget.candles.map((candle) => {
        'time': candle.timestamp.millisecondsSinceEpoch ~/ 1000,
        'open': candle.open,
        'high': candle.high,
        'low': candle.low,
        'close': candle.close,
        'volume': 1000.0, // Dummy volume
      }).toList();

      // Create message
      final message = {
        'type': 'updateChart',
        'candles': candlesData,
        'entryPrice': widget.entryPrice,
        'showEntryPoint': widget.showEntryPoint,
        'visibleFutureCandles': widget.visibleFutureCandles,
        'showFutureCandles': widget.showFutureCandles,
      };

      // Send message to WebView using the global function
      final messageJson = jsonEncode(message);
      final script = '''
        try {
          if (window.updateChartData) {
            window.updateChartData($messageJson);
            console.log("Called updateChartData directly");
            true;
          } else {
            console.error("updateChartData function not found");
            false;
          }
        } catch (error) {
          console.error('Error updating chart:', error);
          false;
        }
      ''';

      _controller.runJavaScript(script).then((result) {
        developer.log('Chart update script executed', name: 'TradingViewChart');
      }).catchError((error) {
        developer.log('Error executing chart update script: $error', name: 'TradingViewChart', error: error);
      });
    } catch (e) {
      developer.log('Error updating chart: $e', name: 'TradingViewChart', error: e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black12,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white24),
      ),
      child: kIsWeb
        ? _buildWebPlaceholder()
        : WebViewWidget(controller: _controller),
    );
  }

  Widget _buildWebPlaceholder() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.candlestick_chart, size: 48, color: Colors.green),
          SizedBox(height: 16),
          Text(
            'Chart Visualization',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white),
          ),
          SizedBox(height: 8),
          Text(
            'Interactive charts not available in web version',
            style: TextStyle(fontSize: 14, color: Colors.grey[400]),
          ),
        ],
      ),
    );
  }
}
