import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Класс для представления кометы
class Comet {
  /// Текущая позиция кометы
  Offset position;

  /// Скорость движения кометы
  double speed;

  /// Угол движения кометы (в радианах)
  double angle;

  /// Размер головы кометы
  double size;

  /// Длина хвоста кометы
  double tailLength;

  /// Яркость кометы (от 0.0 до 1.0)
  double brightness;

  /// Цвет кометы
  Color color;

  /// Время жизни кометы (в миллисекундах)
  int lifespan;

  /// Текущее время жизни кометы
  int currentLifespan;

  /// Флаг активности кометы
  bool isActive;

  Comet({
    required this.position,
    required this.speed,
    required this.angle,
    required this.size,
    required this.tailLength,
    this.brightness = 1.0,
    this.color = Colors.white,
    this.lifespan = 3000,
    this.currentLifespan = 0,
    this.isActive = true,
  });

  /// Обновление позиции кометы
  void update(Size screenSize, int deltaTime) {
    if (!isActive) return;

    // Обновляем позицию на основе скорости и угла
    position = Offset(
      position.dx + math.cos(angle) * speed,
      position.dy + math.sin(angle) * speed,
    );

    // Обновляем время жизни
    currentLifespan += deltaTime;

    // Если комета вышла за пределы экрана или истекло время жизни, деактивируем ее
    if (position.dy > screenSize.height + tailLength ||
        position.dx < -tailLength ||
        position.dx > screenSize.width + tailLength ||
        currentLifespan >= lifespan) {
      isActive = false;
    }
  }

  /// Получение текущей прозрачности кометы
  double getOpacity() {
    // В начале и конце жизни комета менее яркая
    final lifespanRatio = currentLifespan / lifespan;

    if (lifespanRatio < 0.2) {
      // Постепенное появление
      return (lifespanRatio / 0.2) * brightness;
    } else if (lifespanRatio > 0.8) {
      // Постепенное исчезновение
      return ((1.0 - lifespanRatio) / 0.2) * brightness;
    }

    return brightness;
  }
}

/// Виджет для отображения анимации кометы
class CometAnimation extends StatefulWidget {
  /// Интервал между появлениями комет (в миллисекундах)
  final int spawnInterval;

  /// Минимальный размер кометы
  final double minSize;

  /// Максимальный размер кометы
  final double maxSize;

  /// Минимальная скорость кометы
  final double minSpeed;

  /// Максимальная скорость кометы
  final double maxSpeed;

  /// Минимальная длина хвоста кометы
  final double minTailLength;

  /// Максимальная длина хвоста кометы
  final double maxTailLength;

  /// Цвет кометы
  final Color cometColor;

  const CometAnimation({
    super.key,
    this.spawnInterval = 5000,
    this.minSize = 2.0,
    this.maxSize = 4.0,
    this.minSpeed = 3.0,
    this.maxSpeed = 6.0,
    this.minTailLength = 50.0,
    this.maxTailLength = 150.0,
    this.cometColor = Colors.white,
  });

  @override
  State<CometAnimation> createState() => _CometAnimationState();
}

class _CometAnimationState extends State<CometAnimation> with SingleTickerProviderStateMixin {
  /// Список комет
  final List<Comet> _comets = [];

  /// Контроллер анимации
  late AnimationController _controller;

  /// Время последнего обновления
  int _lastUpdateTime = 0;

  /// Время последнего создания кометы
  int _lastSpawnTime = 0;

  @override
  void initState() {
    super.initState();

    // Инициализируем контроллер анимации
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 16), // ~60 FPS
    )..repeat();

    // Инициализируем время
    _lastUpdateTime = DateTime.now().millisecondsSinceEpoch;
    _lastSpawnTime = _lastUpdateTime;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// Создание новой кометы
  void _spawnComet(Size screenSize) {
    final random = math.Random();

    // Случайный размер
    final size = widget.minSize + random.nextDouble() * (widget.maxSize - widget.minSize);

    // Случайная скорость
    final speed = widget.minSpeed + random.nextDouble() * (widget.maxSpeed - widget.minSpeed);

    // Случайная длина хвоста
    final tailLength = widget.minTailLength + random.nextDouble() * (widget.maxTailLength - widget.minTailLength);

    // Случайная яркость
    final brightness = 0.7 + random.nextDouble() * 0.3; // от 0.7 до 1.0

    // Случайная позиция за пределами экрана (сверху или сбоку)
    Offset position;
    double angle;

    if (random.nextBool()) {
      // Сверху
      position = Offset(
        random.nextDouble() * screenSize.width,
        -tailLength,
      );
      angle = math.pi / 2 + (random.nextDouble() - 0.5) * 0.5; // Вниз с небольшим отклонением
    } else {
      // Сбоку (слева или справа)
      if (random.nextBool()) {
        // Слева
        position = Offset(
          -tailLength,
          random.nextDouble() * screenSize.height / 2,
        );
        angle = random.nextDouble() * 0.5; // Вправо с небольшим отклонением вниз
      } else {
        // Справа
        position = Offset(
          screenSize.width + tailLength,
          random.nextDouble() * screenSize.height / 2,
        );
        angle = math.pi - random.nextDouble() * 0.5; // Влево с небольшим отклонением вниз
      }
    }

    // Создаем комету
    _comets.add(Comet(
      position: position,
      speed: speed,
      angle: angle,
      size: size,
      tailLength: tailLength,
      brightness: brightness,
      color: widget.cometColor,
      lifespan: 3000 + random.nextInt(2000), // от 3 до 5 секунд
    ));
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final size = MediaQuery.of(context).size;
        final currentTime = DateTime.now().millisecondsSinceEpoch;
        final deltaTime = currentTime - _lastUpdateTime;

        // Проверяем, нужно ли создать новую комету
        if (currentTime - _lastSpawnTime > widget.spawnInterval) {
          _spawnComet(size);
          _lastSpawnTime = currentTime;
        }

        // Обновляем позиции комет
        for (var comet in _comets) {
          comet.update(size, deltaTime);
        }

        // Удаляем неактивные кометы
        _comets.removeWhere((comet) => !comet.isActive);

        // Обновляем время последнего обновления
        _lastUpdateTime = currentTime;

        return CustomPaint(
          size: size,
          painter: CometPainter(_comets),
        );
      },
    );
  }
}

/// Painter для отрисовки комет
class CometPainter extends CustomPainter {
  final List<Comet> comets;

  CometPainter(this.comets);

  @override
  void paint(Canvas canvas, Size size) {
    for (var comet in comets) {
      // Получаем текущую прозрачность кометы
      final opacity = comet.getOpacity();

      // Создаем более реалистичный хвост кометы с использованием кривых Безье
      final path = Path();

      // Направление хвоста
      final dirX = -math.cos(comet.angle);
      final dirY = -math.sin(comet.angle);

      // Перпендикулярное направление для создания ширины
      final perpX = -dirY;
      final perpY = dirX;

      // Начальная точка (голова кометы)
      final startWidth = comet.size * 1.2; // Шире, чем голова кометы для более заметного эффекта

      // Конечная точка (конец хвоста)
      final endWidth = comet.size * 0.02; // Очень тонкий конец

      // Длина хвоста с небольшой вариацией
      final tailLength = comet.tailLength * (0.9 + math.sin(DateTime.now().millisecondsSinceEpoch / 300) * 0.1);

      // Точка головы кометы
      final headPoint = comet.position;

      // Точка конца хвоста
      final tailEnd = Offset(
        headPoint.dx + dirX * tailLength,
        headPoint.dy + dirY * tailLength,
      );

      // Контрольные точки для кривой Безье
      final controlPoint1 = Offset(
        headPoint.dx + dirX * (tailLength * 0.3),
        headPoint.dy + dirY * (tailLength * 0.3),
      );

      final controlPoint2 = Offset(
        headPoint.dx + dirX * (tailLength * 0.7),
        headPoint.dy + dirY * (tailLength * 0.7),
      );

      // Верхняя и нижняя точки начала хвоста
      final topStart = Offset(
        headPoint.dx + perpX * startWidth,
        headPoint.dy + perpY * startWidth,
      );

      final bottomStart = Offset(
        headPoint.dx - perpX * startWidth,
        headPoint.dy - perpY * startWidth,
      );

      // Верхняя и нижняя точки конца хвоста
      final topEnd = Offset(
        tailEnd.dx + perpX * endWidth,
        tailEnd.dy + perpY * endWidth,
      );

      final bottomEnd = Offset(
        tailEnd.dx - perpX * endWidth,
        tailEnd.dy - perpY * endWidth,
      );

      // Рисуем хвост с использованием кривых Безье для более плавной формы
      path.moveTo(topStart.dx, topStart.dy);

      // Верхняя кривая хвоста
      path.quadraticBezierTo(
        controlPoint1.dx + perpX * (startWidth * 0.7),
        controlPoint1.dy + perpY * (startWidth * 0.7),
        controlPoint2.dx + perpX * (startWidth * 0.3),
        controlPoint2.dy + perpY * (startWidth * 0.3),
      );

      path.quadraticBezierTo(
        tailEnd.dx + perpX * (endWidth * 2),
        tailEnd.dy + perpY * (endWidth * 2),
        topEnd.dx,
        topEnd.dy,
      );

      // Соединяем с нижней частью
      path.lineTo(bottomEnd.dx, bottomEnd.dy);

      // Нижняя кривая хвоста
      path.quadraticBezierTo(
        tailEnd.dx - perpX * (endWidth * 2),
        tailEnd.dy - perpY * (endWidth * 2),
        controlPoint2.dx - perpX * (startWidth * 0.3),
        controlPoint2.dy - perpY * (startWidth * 0.3),
      );

      path.quadraticBezierTo(
        controlPoint1.dx - perpX * (startWidth * 0.7),
        controlPoint1.dy - perpY * (startWidth * 0.7),
        bottomStart.dx,
        bottomStart.dy,
      );

      // Замыкаем путь
      path.close();

      // Создаем более реалистичный градиент для хвоста кометы
      final tailGradient = RadialGradient(
        center: Alignment(-0.9, 0.0), // Смещаем центр градиента ближе к голове кометы
        radius: 1.5,
        colors: [
          Colors.white.withOpacity(opacity), // Яркое белое ядро
          Colors.white.withOpacity(opacity * 0.9), // Переход к основному цвету
          comet.color.withOpacity(opacity * 0.8), // Основной цвет кометы
          comet.color.withOpacity(opacity * 0.5),
          comet.color.withOpacity(opacity * 0.2),
          comet.color.withOpacity(0.0),
        ],
        stops: const [0.0, 0.05, 0.1, 0.3, 0.6, 1.0],
      );

      // Создаем paint для хвоста
      final tailPaint = Paint()
        ..shader = tailGradient.createShader(
          Rect.fromPoints(
            comet.position,
            tailEnd,
          ),
        )
        ..style = PaintingStyle.fill;

      // Рисуем хвост
      canvas.drawPath(path, tailPaint);

      // Создаем более реалистичную голову кометы с несколькими слоями свечения

      // Внешнее свечение (самое большое)
      final outerGlowPaint = Paint()
        ..color = comet.color.withOpacity(opacity * 0.3)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, comet.size * 2.0);

      // Среднее свечение
      final middleGlowPaint = Paint()
        ..color = comet.color.withOpacity(opacity * 0.6)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, comet.size * 1.2);

      // Внутреннее свечение
      final innerGlowPaint = Paint()
        ..color = Colors.white.withOpacity(opacity * 0.8)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, comet.size * 0.6);

      // Ядро кометы
      final corePaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;

      // Рисуем слои свечения от внешнего к внутреннему
      canvas.drawCircle(
        comet.position,
        comet.size * 3.0, // Увеличиваем размер внешнего свечения
        outerGlowPaint,
      );

      canvas.drawCircle(
        comet.position,
        comet.size * 2.0, // Увеличиваем размер среднего свечения
        middleGlowPaint,
      );

      canvas.drawCircle(
        comet.position,
        comet.size * 1.2, // Увеличиваем размер внутреннего свечения
        innerGlowPaint,
      );

      // Рисуем ядро кометы
      canvas.drawCircle(
        comet.position,
        comet.size * 0.8, // Увеличиваем размер ядра
        corePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CometPainter oldDelegate) => true;
}
