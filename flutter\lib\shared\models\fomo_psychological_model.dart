import '../models/anti_fomo_simulator_models.dart';

/// A class that analyzes user decisions for FOMO patterns
class FomoPsychologicalModel {
  // Store user action history
  final List<UserActionRecord> _actionHistory = [];
  
  // FOMO metrics
  double _impulsivityScore = 50.0; // 0-100 scale
  double _emotionalReactivityScore = 50.0; // 0-100 scale
  double _riskTolerance = 50.0; // 0-100 scale
  double _fatalismScore = 50.0; // 0-100 scale (belief that missing out is catastrophic)
  
  // Getters for FOMO metrics
  double get impulsivityScore => _impulsivityScore;
  double get emotionalReactivityScore => _emotionalReactivityScore;
  double get riskTolerance => _riskTolerance;
  double get fatalismScore => _fatalismScore;
  
  /// Overall FOMO susceptibility score (0-100)
  double get overallFomoScore {
    return (_impulsivityScore * 0.3) + 
           (_emotionalReactivityScore * 0.3) + 
           (_riskTolerance * 0.2) + 
           (_fatalismScore * 0.2);
  }
  
  /// Record a user action for analysis
  void recordAction(UserActionRecord action) {
    _actionHistory.add(action);
    _updateScores(action);
  }
  
  /// Update psychological scores based on the latest action
  void _updateScores(UserActionRecord action) {
    // Only update if we have at least a few actions to analyze
    if (_actionHistory.length < 3) return;
    
    // Calculate recent metrics
    final recentActions = _actionHistory.length >= 5 
        ? _actionHistory.sublist(_actionHistory.length - 5)
        : _actionHistory;
    
    // Update impulsivity score - quick decisions under high social pressure
    if (action.decisionTimeSeconds < action.availableTimeSeconds * 0.3 && 
        action.socialHypeLevel > 70) {
      _impulsivityScore = _clampScore(_impulsivityScore + 5.0);
    } else if (action.decisionTimeSeconds > action.availableTimeSeconds * 0.8) {
      _impulsivityScore = _clampScore(_impulsivityScore - 3.0);
    }
    
    // Update emotional reactivity - following social hype trends
    final socialHypeAligned = _isActionAlignedWithSocialHype(action);
    if (socialHypeAligned && action.socialHypeLevel > 70) {
      _emotionalReactivityScore = _clampScore(_emotionalReactivityScore + 4.0);
    } else if (!socialHypeAligned && action.socialHypeLevel > 70) {
      _emotionalReactivityScore = _clampScore(_emotionalReactivityScore - 2.0);
    }
    
    // Update risk tolerance - taking high risk actions under uncertainty
    if (action.action != FomoTradeAction.hold && 
        _actionHistory.length >= 3 &&
        _getConsecutiveActionType(recentActions, action.action) >= 2) {
      _riskTolerance = _clampScore(_riskTolerance + 5.0);
    } else if (action.action == FomoTradeAction.hold && action.socialHypeLevel > 60) {
      _riskTolerance = _clampScore(_riskTolerance - 3.0);
    }
    
    // Update fatalism score - making desperate recovery attempts after losses
    if (_actionHistory.length >= 3) {
      final previousResult = _actionHistory[_actionHistory.length - 2].profitPercentage;
      if (previousResult < -5.0 && action.action != FomoTradeAction.hold) {
        _fatalismScore = _clampScore(_fatalismScore + 5.0);
      } else if (previousResult < -5.0 && action.action == FomoTradeAction.hold) {
        _fatalismScore = _clampScore(_fatalismScore - 3.0);
      }
    }
  }
  
  /// Check if user action aligns with social hype
  bool _isActionAlignedWithSocialHype(UserActionRecord action) {
    if (action.socialHypeLevel > 60) {
      // High social hype typically encourages buying
      return action.action == FomoTradeAction.buy;
    } else if (action.socialHypeLevel < 40) {
      // Low social hype typically encourages selling
      return action.action == FomoTradeAction.sell;
    }
    return false;
  }
  
  /// Count consecutive actions of the same type
  int _getConsecutiveActionType(List<UserActionRecord> records, FomoTradeAction action) {
    int count = 0;
    for (int i = records.length - 1; i >= 0; i--) {
      if (records[i].action == action) {
        count++;
      } else {
        break;
      }
    }
    return count;
  }
  
  /// Get FOMO analysis results
  FomoAnalysisResult getAnalysisResult() {
    if (_actionHistory.isEmpty) {
      return FomoAnalysisResult(
        overallScore: 50.0,
        cognitiveDistortions: [],
        recommendations: [],
        detailedMetrics: {
          'Impulsivity': 50.0,
          'Emotional Reactivity': 50.0,
          'Risk Tolerance': 50.0,
          'Fatalism': 50.0,
        },
      );
    }
    
    // Identify cognitive distortions
    final distortions = <String>[];
    final recommendations = <String>[];
    
    // Analyze patterns
    if (_impulsivityScore > 70) {
      distortions.add('Action Bias: You tend to make hasty decisions to feel in control');
      recommendations.add('Practice delayed gratification: Wait 10 extra seconds before each trade decision');
    }
    
    if (_emotionalReactivityScore > 70) {
      distortions.add('Social Contagion: Your decisions are heavily influenced by social sentiment');
      recommendations.add('Use a pre-trade checklist focusing on technical factors, not social media');
    }
    
    if (_riskTolerance > 70) {
      distortions.add('Gambling Fallacy: You see patterns in random market movements');
      recommendations.add('Use position sizing rules to limit exposure on each trade');
    }
    
    if (_fatalismScore > 70) {
      distortions.add('Catastrophizing: You perceive missed opportunities as devastating');
      recommendations.add('Keep a missed opportunities journal to track that most are not actually significant');
    }
    
    // Add general recommendations if we have enough data
    if (_actionHistory.length >= 5) {
      // Check win/loss ratio
      int wins = 0;
      for (final record in _actionHistory) {
        if (record.profitPercentage > 0) wins++;
      }
      final winRate = wins / _actionHistory.length;
      
      if (winRate < 0.4) {
        recommendations.add('Consider using a paper trading account until your win rate improves');
      }
    }
    
    return FomoAnalysisResult(
      overallScore: overallFomoScore,
      cognitiveDistortions: distortions,
      recommendations: recommendations,
      detailedMetrics: {
        'Impulsivity': _impulsivityScore,
        'Emotional Reactivity': _emotionalReactivityScore,
        'Risk Tolerance': _riskTolerance,
        'Fatalism': _fatalismScore,
      },
    );
  }
  
  /// Clamp score to 0-100 range
  double _clampScore(double score) {
    if (score < 0) return 0;
    if (score > 100) return 100;
    return score;
  }
  
  /// Reset the model
  void reset() {
    _actionHistory.clear();
    _impulsivityScore = 50.0;
    _emotionalReactivityScore = 50.0;
    _riskTolerance = 50.0;
    _fatalismScore = 50.0;
  }
}

/// Class to store user action records
class UserActionRecord {
  final DateTime timestamp;
  final FomoTradeAction action;
  final FomoTradeAction optimalAction;
  final int socialHypeLevel;
  final double profitPercentage;
  final double profitAmount;
  final int decisionTimeSeconds;
  final int availableTimeSeconds;
  
  UserActionRecord({
    required this.timestamp,
    required this.action,
    required this.optimalAction,
    required this.socialHypeLevel,
    required this.profitPercentage,
    required this.profitAmount,
    required this.decisionTimeSeconds,
    required this.availableTimeSeconds,
  });
}

/// Class to represent FOMO analysis results
class FomoAnalysisResult {
  final double overallScore;
  final List<String> cognitiveDistortions;
  final List<String> recommendations;
  final Map<String, double> detailedMetrics;
  
  FomoAnalysisResult({
    required this.overallScore,
    required this.cognitiveDistortions,
    required this.recommendations,
    required this.detailedMetrics,
  });
}

/// Class representing the social hype in the market
class SocialHype {
  int _level = 50;
  
  int get level => _level;
  
  void update(int newLevel) {
    // Apply randomness to simulate market unpredictability
    final random = -10 + (DateTime.now().millisecondsSinceEpoch % 20);
    _level = (newLevel + random).clamp(0, 100);
  }
} 