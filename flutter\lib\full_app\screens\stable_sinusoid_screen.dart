import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:fl_chart/fl_chart.dart';
import '../widgets/app_bottom_navigation.dart';
import '../services/stable_market_prediction_service.dart';

class StableSinusoidScreen extends StatefulWidget {
  const StableSinusoidScreen({super.key});

  @override
  State<StableSinusoidScreen> createState() => _StableSinusoidScreenState();
}

class _StableSinusoidScreenState extends State<StableSinusoidScreen> {
  final StableMarketPredictionService _stableService = StableMarketPredictionService();

  // Current state
  bool _isLoading = true;
  double _currentSentiment = 50.0;
  double _confidence = 0.0;
  String _trendDirection = 'Neutral';
  double _trendStrength = 0.0;
  String _marketCondition = 'Unknown';

  // Market data
  Map<String, double> _marketMetrics = {};
  
  // Technical indicators
  Map<String, double> _technicalIndicators = {};
  
  // Predictions
  List<Map<String, dynamic>> _predictions = [];
  bool _isLoadingPredictions = true;

  // Chart data for trend visualization
  List<FlSpot> _trendData = [];
  List<FlSpot> _predictionData = [];

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  Future<void> _loadAllData() async {
    setState(() {
      _isLoading = true;
      _isLoadingPredictions = true;
    });

    try {
      // Load current market data and predictions in parallel
      await Future.wait([
        _loadCurrentMarketData(),
        _loadPredictions(),
      ]);
    } catch (e) {
      debugPrint('Error loading stable data: $e');
      _showErrorSnackBar('Error loading market data: $e');
    }
  }

  Future<void> _loadCurrentMarketData() async {
    try {
      // Get stable market data
      final marketData = await _stableService.getStableMarketData();
      
      // Get current stable sentiment
      final currentPrediction = await _stableService.getCurrentStableSentiment();
      
      // Get stable technical indicators
      final technicalIndicators = _stableService.getStableTechnicalIndicators();
      
      // Get market analysis
      final marketAnalysis = _stableService.getMarketAnalysis();
      
      if (mounted) {
        setState(() {
          _currentSentiment = currentPrediction;
          _confidence = 0.92; // Высокая уверенность для стабильных данных
          _trendDirection = marketAnalysis['trend'] ?? 'Neutral';
          _trendStrength = marketAnalysis['trend_strength'] ?? 0.0;
          _marketCondition = marketAnalysis['market_condition'] ?? 'Balanced, Normal Volatility';
          _marketMetrics = Map<String, double>.from(marketData);
          _technicalIndicators = technicalIndicators;
          _isLoading = false;
        });
      }

      debugPrint('Stable market data loaded - Sentiment: $_currentSentiment, Confidence: $_confidence');
    } catch (e) {
      debugPrint('Error loading stable market data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      rethrow;
    }
  }

  Future<void> _loadPredictions() async {
    try {
      // Get stable predictions for next 7 days
      final predictions = await _stableService.getStablePredictions(7);
      
      // Prepare chart data
      final trendData = <FlSpot>[];
      final predictionData = <FlSpot>[];
      
      // Add current value as starting point
      trendData.add(FlSpot(0, _currentSentiment));
      
      // Convert predictions to the format expected by the UI
      final predictionMaps = <Map<String, dynamic>>[];
      for (int i = 0; i < predictions.length; i++) {
        final prediction = predictions[i];
        predictionData.add(FlSpot(i + 1.0, prediction.value));
        
        final confidence = prediction.metrics['confidence'] ?? 80.0;
        predictionMaps.add({
          'value': prediction.value,
          'confidence': confidence / 100.0, // Конвертируем в 0.0-1.0
          'day': i + 1,
        });
      }

      if (mounted) {
        setState(() {
          _predictions = predictionMaps;
          _trendData = trendData;
          _predictionData = predictionData;
          _isLoadingPredictions = false;
        });
      }

      debugPrint('Stable predictions loaded: ${predictions.length} days');
    } catch (e) {
      debugPrint('Error loading stable predictions: $e');
      if (mounted) {
        setState(() {
          _isLoadingPredictions = false;
        });
      }
      rethrow;
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Стабильный Анализ Рынка'),
        backgroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAllData,
            tooltip: 'Обновить данные',
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: () async {
              await _stableService.clearCache();
              _loadAllData();
            },
            tooltip: 'Очистить кэш',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildStabilityInfoCard(),
                    const SizedBox(height: 16),
                    _buildCurrentSentimentCard(),
                    const SizedBox(height: 16),
                    _buildTechnicalAnalysisCard(),
                    const SizedBox(height: 16),
                    _buildPredictionChartCard(),
                    const SizedBox(height: 16),
                    _buildMarketMetricsCard(),
                    const SizedBox(height: 16),
                    _buildPredictionsListCard(),
                  ],
                ),
              ),
            ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 2,
        onTap: (index) {
          if (index != 2) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 1:
                Navigator.pushReplacementNamed(context, '/charts');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }

  Widget _buildStabilityInfoCard() {
    return Card(
      color: Colors.green[900],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.verified, color: Colors.green[300], size: 24),
                const SizedBox(width: 8),
                Text(
                  'СТАБИЛЬНАЯ СИСТЕМА ПРОГНОЗИРОВАНИЯ',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[300],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '✓ Детерминированные расчеты без случайных элементов\n'
              '✓ Одинаковые результаты при каждом обновлении\n'
              '✓ Математические модели на основе реальных данных\n'
              '✓ Кэширование до конца дня для стабильности',
              style: TextStyle(
                fontSize: 12,
                color: Colors.green[200],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentSentimentCard() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'Текущее Настроение Рынка',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Gauge
                Expanded(
                  flex: 2,
                  child: SizedBox(
                    height: 200,
                    child: SfRadialGauge(
                      axes: <RadialAxis>[
                        RadialAxis(
                          minimum: 0,
                          maximum: 100,
                          ranges: <GaugeRange>[
                            GaugeRange(startValue: 0, endValue: 20, color: Colors.red),
                            GaugeRange(startValue: 20, endValue: 40, color: Colors.orange),
                            GaugeRange(startValue: 40, endValue: 60, color: Colors.yellow),
                            GaugeRange(startValue: 60, endValue: 80, color: Colors.lightGreen),
                            GaugeRange(startValue: 80, endValue: 100, color: Colors.green),
                          ],
                          pointers: <GaugePointer>[
                            NeedlePointer(
                              value: _currentSentiment,
                              needleColor: Colors.white,
                              knobStyle: const KnobStyle(color: Colors.white),
                            ),
                          ],
                          annotations: <GaugeAnnotation>[
                            GaugeAnnotation(
                              widget: Text(
                                _currentSentiment.toStringAsFixed(1),
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              angle: 90,
                              positionFactor: 0.5,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                // Info
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoRow('Уровень', _getSentimentLevel(_currentSentiment)),
                      _buildInfoRow('Уверенность', '${(_confidence * 100).toStringAsFixed(1)}%'),
                      _buildInfoRow('Тренд', _trendDirection),
                      _buildInfoRow('Сила', '${(_trendStrength * 100).toStringAsFixed(1)}%'),
                      _buildInfoRow('Состояние', _marketCondition),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechnicalAnalysisCard() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Технические Индикаторы',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: _technicalIndicators.entries.map((entry) {
                return _buildTechnicalIndicator(entry.key, entry.value);
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechnicalIndicator(String name, double value) {
    Color color = _getIndicatorColor(name, value);
    String displayValue = _formatIndicatorValue(name, value);
    String displayName = _getIndicatorDisplayName(name);
    
    return Container(
      width: 150,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            displayName,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            displayValue,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPredictionChartCard() {
    if (_isLoadingPredictions) {
      return Card(
        color: Colors.grey[900],
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Стабильный Прогноз на 7 Дней',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    horizontalInterval: 20,
                    verticalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Colors.grey[800]!,
                        strokeWidth: 1,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: Colors.grey[800]!,
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          if (value == 0) return const Text('Сейчас', style: TextStyle(color: Colors.white, fontSize: 12));
                          return Text('День ${value.toInt()}', style: const TextStyle(color: Colors.white, fontSize: 12));
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: 20,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          return Text(value.toInt().toString(), style: const TextStyle(color: Colors.white, fontSize: 12));
                        },
                        reservedSize: 42,
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: Colors.grey[700]!),
                  ),
                  minX: 0,
                  maxX: 7,
                  minY: 0,
                  maxY: 100,
                  lineBarsData: [
                    // Current point
                    LineChartBarData(
                      spots: _trendData,
                      isCurved: false,
                      color: Colors.blue,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 6,
                            color: Colors.blue,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                    ),
                    // Predictions
                    LineChartBarData(
                      spots: _predictionData,
                      isCurved: true,
                      color: Colors.green,
                      barWidth: 2,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: Colors.green,
                            strokeWidth: 1,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarketMetricsCard() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Стабильные Рыночные Метрики',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            ..._marketMetrics.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: _buildMetricRow(_getMetricDisplayName(entry.key), entry.value),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String name, double value) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            name,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: LinearProgressIndicator(
            value: value / 100,
            backgroundColor: Colors.grey[800],
            valueColor: AlwaysStoppedAnimation<Color>(_getSentimentColor(value)),
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 40,
          child: Text(
            value.toStringAsFixed(1),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: _getSentimentColor(value),
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Widget _buildPredictionsListCard() {
    if (_isLoadingPredictions) {
      return Card(
        color: Colors.grey[900],
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Детальные Стабильные Прогнозы',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            ..._predictions.take(5).map((prediction) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: _buildPredictionRow(prediction),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildPredictionRow(Map<String, dynamic> prediction) {
    final value = prediction['value'] as double;
    final confidence = prediction['confidence'] as double;
    final day = prediction['day'] as int;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[800]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'День $day',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  _getSentimentLevel(value),
                  style: TextStyle(
                    fontSize: 12,
                    color: _getSentimentColor(value),
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value.toStringAsFixed(1),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _getSentimentColor(value),
                ),
              ),
              Text(
                'Уверенность: ${(confidence * 100).toStringAsFixed(0)}%',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  String _getSentimentLevel(double value) {
    if (value <= 20) return 'Крайний Страх';
    if (value <= 40) return 'Страх';
    if (value <= 60) return 'Нейтрально';
    if (value <= 80) return 'Жадность';
    return 'Крайняя Жадность';
  }

  Color _getSentimentColor(double value) {
    if (value <= 20) return Colors.red;
    if (value <= 40) return Colors.orange;
    if (value <= 60) return Colors.yellow;
    if (value <= 80) return Colors.lightGreen;
    return Colors.green;
  }

  Color _getIndicatorColor(String name, double value) {
    switch (name) {
      case 'rsi':
        if (value > 70) return Colors.red;
        if (value < 30) return Colors.green;
        return Colors.yellow;
      case 'macd':
        return value > 0 ? Colors.green : Colors.red;
      case 'momentum':
        return value > 0 ? Colors.green : Colors.red;
      default:
        return Colors.blue;
    }
  }

  String _formatIndicatorValue(String name, double value) {
    switch (name) {
      case 'rsi':
        return value.toStringAsFixed(1);
      case 'macd':
        return value.toStringAsFixed(3);
      case 'sma_50':
      case 'sma_200':
        return '\$${value.toStringAsFixed(0)}';
      case 'bollinger_upper':
      case 'bollinger_lower':
        return value.toStringAsFixed(1);
      default:
        return value.toStringAsFixed(2);
    }
  }

  String _getIndicatorDisplayName(String name) {
    switch (name) {
      case 'rsi': return 'RSI';
      case 'macd': return 'MACD';
      case 'sma_50': return 'SMA 50';
      case 'sma_200': return 'SMA 200';
      case 'bollinger_upper': return 'BB Верх';
      case 'bollinger_lower': return 'BB Низ';
      case 'momentum': return 'Моментум';
      case 'volatility': return 'Волатильность';
      case 'volume_trend': return 'Объем';
      case 'social_sentiment': return 'Соц. Настроение';
      default: return name;
    }
  }

  String _getMetricDisplayName(String name) {
    switch (name) {
      case 'fear_greed_index': return 'Индекс Страха/Жадности';
      case 'bitcoin_price_trend': return 'Тренд Цены Bitcoin';
      case 'market_cap_change': return 'Изменение Рын. Кап.';
      case 'trading_volume': return 'Объем Торгов';
      case 'social_sentiment': return 'Социальное Настроение';
      case 'volatility_index': return 'Индекс Волатильности';
      case 'institutional_flow': return 'Институц. Потоки';
      default: return name;
    }
  }
} 