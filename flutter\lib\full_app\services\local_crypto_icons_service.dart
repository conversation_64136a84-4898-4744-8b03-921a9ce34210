import 'package:flutter/material.dart';

class LocalCryptoIconsService {
  // Список символов криптовалют, для которых у нас есть локальные иконки
  static final Set<String> availableIcons = {
    'BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'XRP', 'DOT', 'DOGE', 'LTC', 'LINK', 'XLM', 'MATIC',
    'NEO', 'MANA', 'SAND', 'AXS', 'GALA', 'ENJ', 'CHZ', 'ONE', 'HOT', 'ZIL',
    'BCC', 'QTUM', 'EOS', 'TUSD', 'IOTA', 'ONT', 'TRX', 'ETC', 'ICX', 'NULS',
    'VET', 'USDC', 'WAVES', 'ONG',
    // Добавляем новые токены
    'ZRX', 'FET', 'BAT', 'XMR', 'ZEC'
  };

  // Получение виджета с иконкой криптовалюты
  static Widget getIcon(String symbol, {double size = 32.0, BorderRadius? borderRadius}) {
    // Приводим символ к верхнему регистру
    final upperSymbol = symbol.trim().toUpperCase();

    // Путь к иконке
    final String iconPath = availableIcons.contains(upperSymbol)
        ? 'assets/images/crypto_icons/${upperSymbol.toLowerCase()}.png'
        : 'assets/images/crypto_icons/generic_crypto.png';

    // Создаем виджет с иконкой
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(size / 4),
      child: Image.asset(
        iconPath,
        width: size,
        height: size,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          // В случае ошибки загрузки изображения показываем первую букву символа
          return Container(
            width: size,
            height: size,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.grey.shade800,
              borderRadius: borderRadius ?? BorderRadius.circular(size / 4),
            ),
            child: Text(
              upperSymbol.substring(0, 1),
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: size * 0.5,
              ),
            ),
          );
        },
      ),
    );
  }
}
