import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';

// Shared imports
import 'shared/shared.dart';
import 'shared/providers/app_mode_provider.dart';
import 'shared/widgets/app_mode_switcher.dart';

// App imports
import 'full_app/screens/home_screen.dart';
import 'lite_app/screens/lite_home_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Инициализируем провайдер режима приложения
  final appModeProvider = AppModeProvider();
  await appModeProvider.initialize();
  
  runApp(TMMAppWithSwitcher(appModeProvider: appModeProvider));
}

class TMMAppWithSwitcher extends StatelessWidget {
  final AppModeProvider appModeProvider;

  const TMMAppWithSwitcher({
    Key? key,
    required this.appModeProvider,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: appModeProvider),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => CryptoProvider()),
        ChangeNotifierProvider(create: (_) => NewsProvider()),
      ],
      child: Consumer<AppModeProvider>(
        builder: (context, appMode, child) {
          if (!appMode.isInitialized) {
            return const MaterialApp(
              home: LoadingScreen(),
              debugShowCheckedModeBanner: false,
            );
          }

          return MaterialApp(
            title: appMode.isLiteMode ? 'TMM Lite' : 'TMM Full',
            debugShowCheckedModeBanner: false,
            theme: _buildTheme(appMode.isLiteMode),
            home: AppModeWrapper(),
          );
        },
      ),
    );
  }

  ThemeData _buildTheme(bool isLiteMode) {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      scaffoldBackgroundColor: AppColors.background,
      primaryColor: AppColors.primary,
      colorScheme: ColorScheme.dark(
        primary: isLiteMode ? AppColors.secondary : AppColors.primary,
        secondary: AppColors.secondary,
        surface: AppColors.surface,
        background: AppColors.background,
        error: AppColors.error,
        onPrimary: AppColors.textPrimary,
        onSecondary: AppColors.textPrimary,
        onSurface: AppColors.textPrimary,
        onBackground: AppColors.textPrimary,
        onError: AppColors.textPrimary,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.backgroundLight,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: AppColors.textPrimary,
          fontSize: isLiteMode ? 18 : 20,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

class AppModeWrapper extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<AppModeProvider>(
      builder: (context, appMode, child) {
        return Stack(
          children: [
            // Основное приложение
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (child, animation) {
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.1, 0),
                      end: Offset.zero,
                    ).animate(animation),
                    child: child,
                  ),
                );
              },
              child: appMode.isLiteMode 
                  ? const LiteHomeScreen(key: ValueKey('lite'))
                  : const FullHomeScreen(key: ValueKey('full')),
            ),
            
            // Плавающий переключатель (только в debug режиме)
            if (kDebugMode)
              Positioned(
                top: 50,
                right: 16,
                child: AppModeSwitcher(showAsFloatingButton: true),
              ),
          ],
        );
      },
    );
  }
}

// Экран загрузки
class LoadingScreen extends StatelessWidget {
  const LoadingScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.trending_up,
                color: AppColors.primary,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'TMM',
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 32,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Инициализация...',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 32),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ],
        ),
      ),
    );
  }
}

// Заглушка для полного экрана (пока не адаптирован)
class FullHomeScreen extends StatelessWidget {
  const FullHomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('TMM Full'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              _showModeSettings(context);
            },
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.desktop_windows,
              color: AppColors.primary,
              size: 80,
            ),
            const SizedBox(height: 24),
            const Text(
              'Full Mode',
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 32,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Полная версия с расширенным функционалом',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                context.read<AppModeProvider>().switchMode(true);
              },
              icon: const Icon(Icons.phone_android),
              label: const Text('Переключить на Lite'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.secondary,
                foregroundColor: AppColors.textPrimary,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: kDebugMode ? const AppModeFAB() : null,
    );
  }

  void _showModeSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppColors.backgroundLight,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Настройки режима',
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            const AppModeSwitcher(),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context.read<AppModeProvider>().resetToDefault();
                Navigator.pop(context);
              },
              child: const Text('Сбросить к умолчанию'),
            ),
          ],
        ),
      ),
    );
  }
}
