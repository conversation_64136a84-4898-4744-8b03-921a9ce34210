import 'package:flutter/material.dart';

class ModernFilledButton extends StatefulWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  const ModernFilledButton({Key? key, required this.text, required this.onPressed, this.isLoading=false}) : super(key: key);

  @override
  State<ModernFilledButton> createState() => _ModernFilledButtonState();
}

class _ModernFilledButtonState extends State<ModernFilledButton> {
  bool _isHovered = false;
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        height: 48,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: _isHovered
                ? [const Color(0xFF5E626C), const Color(0xFF4A4E56)]
                : [const Color(0xFF4A4D55), const Color(0xFF3A3D43)],
          ),
          border: Border.all(
            color: _isHovered
                ? Colors.black.withOpacity(0.8)
                : Colors.black.withOpacity(0.6),
            width: 1.4,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
            if (_isHovered)
              BoxShadow(
                color: const Color(0xFF16213E).withOpacity(0.4),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: widget.isLoading ? null : widget.onPressed,
            child: Center(
              child: widget.isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      widget.text,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
} 