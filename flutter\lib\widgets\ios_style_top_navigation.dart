import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// iOS-стиль верхней навигационной панели с сегментированным контролом
class IOSStyleTopNavigation extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;

  const IOSStyleTopNavigation({
    Key? key,
    required this.selectedIndex,
    required this.onItemSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: CupertinoSlidingSegmentedControl<int>(
        groupValue: selectedIndex,
        children: {
          0: _buildSegment('All', CupertinoIcons.news),
          1: _buildSegment('Crypto', CupertinoIcons.bitcoin),
          2: _buildSegment('Stocks', CupertinoIcons.chart_bar),
          3: _buildSegment('Whales', CupertinoIcons.money_dollar),
        },
        onValueChanged: (value) {
          if (value != null) {
            onItemSelected(value);
          }
        },
        thumbColor: isDarkMode ? const Color(0xFF2C2C2E) : Colors.white,
        backgroundColor: isDarkMode ? const Color(0xFF1C1C1E) : const Color(0xFFF2F2F7),
      ),
    );
  }

  Widget _buildSegment(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 16,
            color: selectedIndex == _getIndexForTitle(title)
                ? CupertinoColors.systemBlue
                : CupertinoColors.systemGrey,
          ),
          const SizedBox(width: 6),
          Text(
            title,
            style: TextStyle(
              fontSize: 13,
              fontWeight: selectedIndex == _getIndexForTitle(title)
                  ? FontWeight.bold
                  : FontWeight.normal,
              color: selectedIndex == _getIndexForTitle(title)
                  ? CupertinoColors.systemBlue
                  : CupertinoColors.systemGrey,
            ),
          ),
        ],
      ),
    );
  }

  int _getIndexForTitle(String title) {
    switch (title) {
      case 'All': return 0;
      case 'Crypto': return 1;
      case 'Stocks': return 2;
      case 'Whales': return 3;
      default: return 0;
    }
  }
}
