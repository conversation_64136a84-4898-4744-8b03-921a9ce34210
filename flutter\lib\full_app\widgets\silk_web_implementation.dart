import 'package:flutter/material.dart';

class SilkImplementation extends StatelessWidget {
  const SilkImplementation({super.key});

  @override
  Widget build(BuildContext context) {
    // Временная заглушка для веб-версии
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            const Color(0xFF1C1C1C).withOpacity(0.4),
            const Color(0xFF1C1C1C).withOpacity(0.1),
            const Color(0xFF1C1C1C).withOpacity(0.3),
            const Color(0xFF1C1C1C).withOpacity(0.05),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: const Center(
        child: Text(
          'Silk Background',
          style: TextStyle(
            color: Colors.white24,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
} 