import 'package:flutter/material.dart';

class VerticalCompactTabBar extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onTabChanged;

  const VerticalCompactTabBar({
    Key? key,
    required this.selectedIndex,
    required this.onTabChanged,
  }) : super(key: key);

  static const _tabs = [
    _TabData('Home', Icons.home_rounded),
    _TabData('Measure', Icons.straighten_rounded),
    _TabData('Analyze', Icons.analytics_rounded),
    _TabData('Reduce', Icons.delete_sweep_rounded),
    _TabData('Report', Icons.assignment_rounded),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF0f2f27), Color(0xFF1a4d42)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          for (int i = 0; i < _tabs.length; i++) ...[
            _TabButton(
              data: _tabs[i],
              isActive: selectedIndex == i,
              onTap: () => onTabChanged(i),
            ),
            if (i != _tabs.length - 1) const SizedBox(height: 12),
          ],
        ],
      ),
    );
  }
}

class _TabButton extends StatelessWidget {
  final _TabData data;
  final bool isActive;
  final VoidCallback onTap;

  const _TabButton({
    required this.data,
    required this.isActive,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 220),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        color: isActive ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(22),
        border: isActive
            ? null
            : Border.all(color: Colors.white.withOpacity(0.4), width: 1),
        boxShadow: isActive
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.10),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ]
            : [],
      ),
      height: 45,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(22),
        onTap: onTap,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              data.icon,
              color: isActive ? const Color(0xFF0f2f27) : Colors.white,
              size: 22,
            ),
            const SizedBox(width: 10),
            Text(
              data.label,
              style: TextStyle(
                color: isActive ? const Color(0xFF0f2f27) : Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TabData {
  final String label;
  final IconData icon;
  const _TabData(this.label, this.icon);
} 