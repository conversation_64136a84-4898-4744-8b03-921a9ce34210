import 'package:flutter/material.dart';
import '../models/anti_fomo_simulator_models.dart';
import 'dart:math';
import 'dart:async';

/// A widget that displays fake social media messages
class FakeChat extends StatefulWidget {
  final List<ChatMessage> messages;
  final int? onlineCount;

  const FakeChat({
    super.key,
    required this.messages,
    this.onlineCount,
  });

  @override
  State<FakeChat> createState() => _FakeChatState();
}

class _FakeChatState extends State<FakeChat> {
  late final int onlineCount;
  late final Stream<int> _messageStream;

  @override
  void initState() {
    super.initState();
    onlineCount = widget.onlineCount ?? (20 + Random().nextInt(100));
    _messageStream = Stream.periodic(
      Duration(seconds: 1 + Random().nextInt(4)),
      (count) => count,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[800]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Chat header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Crypto Chat',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$onlineCount online',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 13,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Chat messages
          Expanded(
            child: StreamBuilder<int>(
              stream: _messageStream,
              builder: (context, snapshot) {
                return ListView.builder(
                  padding: const EdgeInsets.all(12),
                  itemCount: widget.messages.length,
                  reverse: true,
                  itemBuilder: (context, index) {
                    final message = widget.messages[index];
                    return _buildChatMessage(message);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatMessage(ChatMessage message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Username and timestamp
          Row(
            children: [
              Text(
                message.username,
                style: TextStyle(
                  color: message.isPositive ? Colors.green[400] : Colors.red[400],
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _formatTimestamp(message.timestamp),
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 10,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          // Message content
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: message.isPositive 
                  ? Colors.green.withOpacity(0.1) 
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: message.isPositive 
                    ? Colors.green.withOpacity(0.3) 
                    : Colors.red.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Text(
              message.message,
              style: TextStyle(
                color: message.isPositive 
                    ? Colors.white 
                    : Colors.red.shade300,
                fontSize: 12,
              ),
            ),
          ),
          
          const SizedBox(height: 4),
          
          // Likes
          if (message.likes > 0)
            Row(
              children: [
                const Icon(
                  Icons.thumb_up,
                  color: Colors.blue,
                  size: 12,
                ),
                const SizedBox(width: 4),
                Text(
                  message.likes.toString(),
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 10,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inSeconds < 60) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
