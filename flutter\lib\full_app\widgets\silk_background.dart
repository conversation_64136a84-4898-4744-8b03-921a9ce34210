import 'dart:math';
import 'dart:ui';
import 'package:flutter/material.dart';

class SilkBackground extends StatefulWidget {
  final Widget? child;
  const SilkBackground({Key? key, this.child}) : super(key: key);

  @override
  State<SilkBackground> createState() => _SilkBackgroundState();
}

class _SilkBackgroundState extends State<SilkBackground> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _CellsPainter(_controller),
      child: widget.child ?? const SizedBox.expand(),
    );
  }
}

class _CellsPainter extends CustomPainter {
  final Animation<double> animation;
  _CellsPainter(this.animation) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final cellSize = 60.0;
    final cols = (size.width / cellSize).ceil();
    final rows = (size.height / cellSize).ceil();
    final time = animation.value * 2 * pi;

    for (int i = 0; i < cols; i++) {
      for (int j = 0; j < rows; j++) {
        final x = i * cellSize + cellSize / 2;
        final y = j * cellSize + cellSize / 2;
        final dx = sin(time + i * 0.5 + j * 0.7) * 12;
        final dy = cos(time + i * 0.7 - j * 0.5) * 12;
        final color = Color.lerp(
          const Color(0xFF1c1c21),
          const Color(0xFF2f3037),
          0.5 + 0.5 * sin(time + i + j),
        )!;
        final paint = Paint()
          ..color = color.withOpacity(0.7)
          ..style = PaintingStyle.fill;
        canvas.drawCircle(Offset(x + dx, y + dy), cellSize * 0.45, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant _CellsPainter oldDelegate) => true;
}

// Специальный фон для новостного экрана
class NewsScreenSilkBackground extends StatelessWidget {
  final Widget child;

  const NewsScreenSilkBackground({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SilkBackground(
      child: child,
    );
  }
}
