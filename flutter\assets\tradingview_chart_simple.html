<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Simple TradingView Chart</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #131722;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            cursor: grab;
        }

        #chart-container:active {
            cursor: grabbing;
        }

        #drag-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            display: flex;
            align-items: center;
            opacity: 0.9;
            transition: opacity 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }

        #result-popup {
            position: absolute;
            top: 50%;
            left: 25%;
            transform: translate(-50%, -50%);
            background-color: rgba(30, 34, 45, 0.9);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            min-width: 200px;
            display: none;
        }

        .result-success { color: #4CAF50; }
        .result-failure { color: #F44336; }
        .result-profit {
            font-size: 20px;
            font-weight: bold;
            margin: 12px 0;
        }
        .profit-positive { color: #4CAF50; }
        .profit-negative { color: #F44336; }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="drag-indicator">Chart is fully draggable in ALL directions</div>
    <div id="result-popup">
        <div class="result-title">Результат</div>
        <div id="result-status"></div>
        <div id="result-profit" class="result-profit"></div>
    </div>

    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Глобальные переменные
        let chart;
        let candleSeries;
        let allCandles = [];
        let visibleCandlesCount = 243;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;
        let extraCandlesCount = 300;

        // Флаг для отслеживания инициализации графика
        let isChartInitialized = false;

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            if (!isChartInitialized) {
                initChart();
                isChartInitialized = true;
            }
        });

        // Обработчик сообщений от Flutter
        window.addEventListener('message', function(event) {
            console.log('Received message:', event.data);
            try {
                const message = JSON.parse(event.data);

                switch (message.action) {
                    case 'loadCandles':
                        loadCandles(message.data);
                        break;
                    case 'forceLoadNewCandles':
                        // Принудительная загрузка новых свечей (сбрасываем флаг)
                        resetCandlesLoadedFlag();
                        loadCandles(message.data);
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'determineResult':
                        determineResult();
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                    case 'resetChartPosition':
                        resetChartPosition();
                        break;
                    case 'centerLastCandle':
                        centerLastCandle();
                        break;
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });

        // Инициализация графика - максимально простая версия
        function initChart() {
            // Проверяем, не инициализирован ли уже график
            if (chart) {
                console.log('Chart already initialized, skipping initialization');
                return;
            }

            console.log('Initializing chart with SUPER SIMPLE approach');
            const container = document.getElementById('chart-container');

            if (!container) {
                console.error('Chart container not found!');
                return;
            }

            console.log('Container dimensions:', container.clientWidth, 'x', container.clientHeight);

            // Создаем график с минимальными настройками
            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: container.clientHeight,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    barSpacing: 6,
                    // КРИТИЧЕСКИ ВАЖНО: отключаем все ограничения прокрутки
                    fixRightEdge: false,
                    fixLeftEdge: false,
                    rightBarStaysOnScroll: false,
                    lockVisibleTimeRangeOnResize: false,
                    shiftVisibleRangeOnNewBar: false,
                    rightOffset: 30,
                },
                rightPriceScale: {
                    autoScale: true,
                },
                // ВАЖНО: явно включаем перемещение и масштабирование
                handleScroll: true,
                handleScale: true,
            });

            if (!chart) {
                console.error('Failed to create chart!');
                return;
            }

            console.log('Chart created successfully');

            // Создаем серию свечей
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });

            if (!candleSeries) {
                console.error('Failed to create candle series!');
                return;
            }

            console.log('Candle series created successfully');

            // Обработчик изменения размера окна
            window.addEventListener('resize', function() {
                if (!chart) return;

                chart.resize(container.clientWidth, container.clientHeight);
                console.log('Chart resized to:', container.clientWidth, 'x', container.clientHeight);

                // Центрируем последнюю свечу при изменении размера
                setTimeout(centerLastCandle, 100);
            });

            // Показываем индикатор перемещения
            const dragIndicator = document.getElementById('drag-indicator');
            if (dragIndicator) {
                dragIndicator.style.display = 'block';
                dragIndicator.textContent = 'Chart initialized! You can drag in ALL directions';
                setTimeout(() => {
                    dragIndicator.style.display = 'none';
                }, 5000);
            }

            // Сообщаем Flutter, что график инициализирован
            sendMessageToFlutter('chartInitialized', []);
            console.log('Chart initialization complete');
        }

        // Флаг для отслеживания загрузки свечей
        let candlesLoaded = false;

        // Загрузка свечей
        function loadCandles(candles) {
            console.log('Loading candles');
            try {
                // Если свечи уже загружены и мы в середине раунда, не загружаем их заново
                if (candlesLoaded && allCandles.length > 0) {
                    console.log(`Candles already loaded (${allCandles.length}), using existing candles`);

                    // Показываем начальные свечи
                    showInitialCandles();

                    // Сообщаем Flutter, что свечи загружены
                    sendMessageToFlutter('candlesLoaded', [allCandles.length]);
                    return;
                }

                // Загружаем новые свечи
                allCandles = Array.isArray(candles) ? candles : JSON.parse(candles);
                console.log(`Loaded ${allCandles.length} candles`);

                // Устанавливаем флаг, что свечи загружены
                candlesLoaded = true;

                // Показываем начальные свечи
                showInitialCandles();

                // Сообщаем Flutter, что свечи загружены
                sendMessageToFlutter('candlesLoaded', [allCandles.length]);
            } catch (e) {
                console.error('Error loading candles:', e);
            }
        }

        // Создание дополнительных свечей для прокрутки вправо
        function createExtraCandles() {
            if (!allCandles || allCandles.length < 2) return [];

            const extraCandles = [];
            const lastCandle = allCandles[allCandles.length - 1];
            const secondLastCandle = allCandles[allCandles.length - 2];
            const timeStep = lastCandle.time - secondLastCandle.time;

            // Создаем дополнительные свечи с реалистичными данными
            let prevClose = lastCandle.close;
            for (let i = 1; i <= extraCandlesCount; i++) {
                const changePercent = (Math.random() * 4 - 2) * 1.0;
                const change = prevClose * (changePercent / 100);

                const close = prevClose + change;
                const open = prevClose;
                const high = Math.max(open, close) * (1 + Math.random() * 0.01);
                const low = Math.min(open, close) * (1 - Math.random() * 0.01);
                const volume = lastCandle.volume * (0.5 + Math.random());

                extraCandles.push({
                    time: lastCandle.time + timeStep * i,
                    open: open,
                    high: high,
                    low: low,
                    close: close,
                    volume: volume
                });

                prevClose = close;
            }

            console.log(`Created ${extraCandles.length} extra candles for right scrolling`);
            return extraCandles;
        }

        // Показать только первые 243 свечи - с новым подходом к центрированию
        function showInitialCandles() {
            console.log('Showing initial candles with new centering approach');

            if (!allCandles.length) {
                console.error('No candles available');
                return;
            }

            try {
                // Берем только первые 243 свечи
                const initialCandles = allCandles.slice(0, visibleCandlesCount);
                console.log(`Using ${initialCandles.length} initial candles`);

                // Создаем дополнительные свечи для прокрутки вправо
                const extraCandles = createExtraCandles();
                console.log(`Created ${extraCandles.length} extra candles`);

                // Объединяем начальные свечи с дополнительными
                const extendedCandles = [...initialCandles, ...extraCandles];
                console.log(`Total extended candles: ${extendedCandles.length}`);

                // Устанавливаем свечи на график
                candleSeries.setData(extendedCandles);

                // Применяем новый подход к центрированию
                // Шаг 1: Подгоняем весь график
                chart.timeScale().fitContent();

                // Шаг 2: Смещаем график вправо
                const scrollPosition = 50;
                chart.timeScale().scrollToPosition(scrollPosition, false);

                // Шаг 3: Дополнительное центрирование с задержкой
                setTimeout(() => {
                    chart.timeScale().scrollToPosition(scrollPosition, false);

                    // Автоматически устанавливаем точку входа на последней видимой свече (243-й)
                    // Это важно для отображения точки входа сразу при загрузке графика
                    setEntryPoint();

                    // Сообщаем Flutter, что начальные свечи отображены
                    sendMessageToFlutter('initialCandlesShown', []);
                }, 300);

                // Шаг 4: Еще одно центрирование с большей задержкой
                setTimeout(() => {
                    chart.timeScale().scrollToPosition(scrollPosition, false);
                }, 600);

                console.log('Initial candles shown with new centering approach');
            } catch (e) {
                console.error('Error showing initial candles:', e);
            }
        }

        // Показать все 250 свечей - с новым подходом к центрированию
        function showAllCandles() {
            console.log('Showing all candles with new centering approach');

            if (!allCandles.length) {
                console.error('No candles available');
                return;
            }

            try {
                // Важно: используем все свечи, включая будущие 7 свечей
                // Проверяем, что у нас достаточно свечей
                if (allCandles.length < visibleCandlesCount + 7) {
                    console.error(`Not enough candles: ${allCandles.length}, need at least ${visibleCandlesCount + 7}`);
                    return;
                }

                // Берем все свечи, включая будущие 7 свечей
                const candlesToShow = allCandles.slice(0, visibleCandlesCount + 7);
                console.log(`Showing ${candlesToShow.length} candles (${visibleCandlesCount} visible + 7 future)`);

                // Создаем дополнительные свечи для прокрутки вправо
                const extraCandles = createExtraCandles();
                console.log(`Created ${extraCandles.length} extra candles`);

                // Объединяем все свечи с дополнительными
                const extendedCandles = [...candlesToShow, ...extraCandles];
                console.log(`Total extended candles: ${extendedCandles.length}`);

                // Устанавливаем свечи на график
                candleSeries.setData(extendedCandles);

                // Применяем новый подход к центрированию
                // Шаг 1: Подгоняем весь график
                chart.timeScale().fitContent();

                // Шаг 2: Смещаем график вправо
                const scrollPosition = 50;
                chart.timeScale().scrollToPosition(scrollPosition, false);

                // Шаг 3: Дополнительное центрирование с задержкой
                setTimeout(() => {
                    chart.timeScale().scrollToPosition(scrollPosition, false);

                    // Сообщаем Flutter, что все свечи отображены
                    sendMessageToFlutter('allCandlesShown', []);
                }, 300);

                // Шаг 4: Еще одно центрирование с большей задержкой
                setTimeout(() => {
                    chart.timeScale().scrollToPosition(scrollPosition, false);
                }, 600);

                console.log('All candles shown with new centering approach');
            } catch (e) {
                console.error('Error showing all candles:', e);
            }
        }

        // Центрирование последней свечи - СОВЕРШЕННО НОВЫЙ подход с использованием fitContent и scrollToPosition
        function centerLastCandle() {
            console.log('Centering last candle with COMPLETELY NEW approach using fitContent and scrollToPosition');

            if (!chart || !candleSeries) {
                console.error('Chart or candleSeries not available');
                return;
            }

            try {
                // Получаем данные свечей
                const candles = candleSeries.dataByIndex();
                if (!candles || candles.length === 0) {
                    console.error('No candles available');
                    return;
                }

                console.log('Total candles in series:', candles.length);
                console.log('Total candles in allCandles:', allCandles.length);

                // Отключаем все ограничения прокрутки
                chart.applyOptions({
                    timeScale: {
                        fixRightEdge: false,
                        fixLeftEdge: false,
                        rightBarStaysOnScroll: false,
                        lockVisibleTimeRangeOnResize: false,
                        shiftVisibleRangeOnNewBar: false,
                    },
                    handleScroll: true,
                    handleScale: true,
                });

                console.log('Applying new centering approach with fitContent and scrollToPosition');

                // Шаг 1: Подгоняем весь график, чтобы все свечи были видны
                chart.timeScale().fitContent();
                console.log('Step 1: fitContent applied');

                // Шаг 2: Смещаем график вправо, чтобы последняя свеча была в левой части экрана
                // Используем большое положительное значение для смещения вправо
                const scrollPosition = 50; // Можно настроить это значение для точной позиции
                chart.timeScale().scrollToPosition(scrollPosition, false);
                console.log('Step 2: scrollToPosition applied with position:', scrollPosition);

                // Шаг 3: Дополнительное центрирование с задержкой для гарантии применения
                setTimeout(() => {
                    chart.timeScale().scrollToPosition(scrollPosition, false);
                    console.log('Step 3: scrollToPosition applied again after 300ms');

                    // Показываем индикатор перемещения
                    const dragIndicator = document.getElementById('drag-indicator');
                    if (dragIndicator) {
                        dragIndicator.style.display = 'block';
                        dragIndicator.textContent = 'Chart centered with new approach! You can drag in ALL directions';
                    }

                    // Отправляем сообщение во Flutter
                    sendMessageToFlutter('lastCandleCentered', []);
                }, 300);

                // Шаг 4: Еще одно центрирование с большей задержкой
                setTimeout(() => {
                    chart.timeScale().scrollToPosition(scrollPosition, false);
                    console.log('Step 4: scrollToPosition applied again after 600ms');

                    // Скрываем индикатор перемещения через 5 секунд
                    setTimeout(() => {
                        const dragIndicator = document.getElementById('drag-indicator');
                        if (dragIndicator) {
                            dragIndicator.style.display = 'none';
                        }
                    }, 5000);
                }, 600);

                console.log('Centering process initiated with new approach');
            } catch (e) {
                console.error('Error centering last candle with new approach:', e);
            }
        }

        // Сброс позиции графика - использует новый подход с fitContent и scrollToPosition
        function resetChartPosition() {
            console.log('Resetting chart position with new approach');

            if (!chart || !candleSeries) {
                console.error('Chart or candleSeries not available');
                return;
            }

            try {
                // Отключаем все ограничения прокрутки
                chart.applyOptions({
                    timeScale: {
                        fixRightEdge: false,
                        fixLeftEdge: false,
                        rightBarStaysOnScroll: false,
                        lockVisibleTimeRangeOnResize: false,
                        shiftVisibleRangeOnNewBar: false,
                    },
                    handleScroll: true,
                    handleScale: true,
                });

                // Очищаем все элементы графика
                clearChartElements();

                // Шаг 1: Подгоняем весь график, чтобы все свечи были видны
                chart.timeScale().fitContent();

                // Шаг 2: Смещаем график вправо, чтобы последняя свеча была в левой части экрана
                const scrollPosition = 50;
                chart.timeScale().scrollToPosition(scrollPosition, false);

                // Шаг 3: Дополнительное центрирование с задержкой для гарантии применения
                setTimeout(() => {
                    chart.timeScale().scrollToPosition(scrollPosition, false);

                    // Показываем индикатор перемещения
                    const dragIndicator = document.getElementById('drag-indicator');
                    if (dragIndicator) {
                        dragIndicator.style.display = 'block';
                        dragIndicator.textContent = 'Chart position reset! You can drag in ALL directions';
                    }

                    // Отправляем сообщение во Flutter
                    sendMessageToFlutter('chartPositionReset', []);
                }, 300);

                // Шаг 4: Еще одно центрирование с большей задержкой
                setTimeout(() => {
                    chart.timeScale().scrollToPosition(scrollPosition, false);

                    // Скрываем индикатор перемещения через 5 секунд
                    setTimeout(() => {
                        const dragIndicator = document.getElementById('drag-indicator');
                        if (dragIndicator) {
                            dragIndicator.style.display = 'none';
                        }
                    }, 5000);
                }, 600);

                console.log('Chart position reset complete with new approach');
            } catch (e) {
                console.error('Error resetting chart position:', e);
            }
        }

        // Установка точки входа
        function setEntryPoint() {
            console.log('Setting entry point');

            if (!allCandles || allCandles.length < visibleCandlesCount) {
                console.error('Not enough candles to set entry point');
                return;
            }

            try {
                // Точка входа - ВСЕГДА последняя видимая свеча (243-я)
                const entryPointIndex = visibleCandlesCount - 1;
                const entryCandle = allCandles[entryPointIndex];

                if (!entryCandle) {
                    console.error('Entry candle not found at index:', entryPointIndex);
                    return;
                }

                entryPointPrice = entryCandle.close;
                entryPointTime = entryCandle.time;

                console.log('Entry point set at index:', entryPointIndex);
                console.log('Entry point set at price:', entryPointPrice);
                console.log('Entry point set at time:', entryPointTime);
                console.log('Total candles:', allCandles.length);
                console.log('Visible candles count:', visibleCandlesCount);

                // Удаляем предыдущую линию, если она есть
                if (horizontalLine) {
                    candleSeries.removePriceLine(horizontalLine);
                }

                // Создаем горизонтальную линию для точки входа с улучшенной видимостью
                horizontalLine = candleSeries.createPriceLine({
                    price: entryPointPrice,
                    color: '#2196F3', // Более яркий синий цвет
                    lineWidth: 2, // Увеличенная толщина линии
                    lineStyle: LightweightCharts.LineStyle.Solid, // Сплошная линия вместо пунктирной
                    axisLabelVisible: true,
                    title: 'Entry',
                });

                // Сообщаем Flutter, что точка входа установлена
                sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);
            } catch (e) {
                console.error('Error setting entry point:', e);
            }
        }

        // Определение результата
        function determineResult() {
            console.log('Determining result');

            if (!entryPointPrice) return;

            if (allCandles.length < visibleCandlesCount + 7) return;

            try {
                // Берем 7-ю свечу после точки входа
                const resultCandle = allCandles[visibleCandlesCount + 6];
                if (!resultCandle) return;

                // Сравниваем цену закрытия с ценой входа
                const priceChange = resultCandle.close - entryPointPrice;
                const percentChange = (priceChange / entryPointPrice) * 100;
                const isUp = priceChange > 0;

                console.log('Result determined:', {
                    isUp: isUp,
                    percentChange: percentChange.toFixed(2) + '%',
                    finalPrice: resultCandle.close
                });

                // Показываем всплывающее окно с результатом
                showResultPopup(isUp, percentChange);

                // Сообщаем Flutter о результате
                sendMessageToFlutter('tradeResult', [
                    isUp,
                    percentChange,
                    resultCandle.close
                ]);
            } catch (e) {
                console.error('Error determining result:', e);
            }
        }

        // Показать всплывающее окно с результатом
        function showResultPopup(isSuccess, percentChange) {
            const popup = document.getElementById('result-popup');
            const statusElement = document.getElementById('result-status');
            const profitElement = document.getElementById('result-profit');

            if (!popup || !statusElement || !profitElement) return;

            // Устанавливаем статус
            statusElement.innerHTML = isSuccess ?
                '<span class="result-success">Правильный выбор</span>' :
                '<span class="result-failure">Неправильный выбор</span>';

            // Форматируем процент изменения
            const formattedPercent = Math.abs(percentChange).toFixed(2) + '%';

            // Устанавливаем прибыль/убыток
            profitElement.innerHTML = percentChange >= 0 ?
                '+' + formattedPercent :
                '-' + formattedPercent;
            profitElement.className = 'result-profit ' +
                (percentChange >= 0 ? 'profit-positive' : 'profit-negative');

            // Показываем всплывающее окно
            popup.style.display = 'block';

            // Скрываем через 5 секунд
            setTimeout(() => {
                popup.style.display = 'none';
            }, 5000);
        }

        // Очистка всех элементов графика
        function clearChartElements() {
            console.log('Clearing chart elements');

            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;

            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Скрываем всплывающее окно с результатом
            const popup = document.getElementById('result-popup');
            if (popup) {
                popup.style.display = 'none';
            }

            // НЕ сбрасываем флаг загрузки свечей, чтобы использовать те же свечи в следующем раунде
            // candlesLoaded = false;
        }

        // Сброс флага загрузки свечей
        function resetCandlesLoadedFlag() {
            console.log('Resetting candles loaded flag - forcing new candles to be loaded');
            candlesLoaded = false;
            allCandles = [];

            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;

            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }
        }

        // Отправка сообщения в Flutter
        function sendMessageToFlutter(handler, args) {
            try {
                const message = JSON.stringify({
                    handler: handler,
                    args: args
                });

                if (window.flutter_inappwebview) {
                    // Для мобильных платформ
                    window.flutter_inappwebview.postMessage(message);
                } else if (window.parent && window.parent !== window) {
                    // Для веб-платформы (iframe)
                    window.parent.postMessage(message, '*');
                } else {
                    console.log('Flutter interface not available');
                }
            } catch (e) {
                console.error('Error sending message to Flutter:', e);
            }
        }
    </script>
</body>
</html>
