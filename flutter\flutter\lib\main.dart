import 'package:flutter/material.dart';
import 'screens/news_screen.dart';
import 'screens/crypto_markets_screen.dart';
import 'screens/charts_screen_final_carousel.dart';
import 'screens/profile_screen.dart';
import 'screens/courses_screen.dart';
import 'screens/course_detail_screen.dart';
import 'screens/materials_screen.dart';
import 'screens/games_screen.dart';
import 'screens/saved_analyses_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Finance AI Flutter',
      theme: ThemeData(
        brightness: Brightness.dark,
        primarySwatch: Colors.blue,
        fontFamily: 'Roboto',
        scaffoldBackgroundColor: Colors.black,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          backgroundColor: Colors.black,
          selectedItemColor: Colors.white,
          unselectedItemColor: Colors.grey,
        ),
      ),
      initialRoute: '/news',
      routes: {
        '/news': (context) => const NewsScreen(),
        '/crypto_markets': (context) => const CryptoMarketsScreen(),
        '/charts': (context) => const ChartsScreenFinalCarousel(),
        '/profile': (context) => const ProfileScreen(),
        '/courses': (context) => const CoursesScreen(),
        '/course_detail': (context) => const CourseDetailScreen(),
        '/materials': (context) => const MaterialsScreen(),
        '/games': (context) => const GamesScreen(),
        '/saved_analyses': (context) => const SavedAnalysesScreen(),
      },
      debugShowCheckedModeBanner: false,
    );
  }
}
