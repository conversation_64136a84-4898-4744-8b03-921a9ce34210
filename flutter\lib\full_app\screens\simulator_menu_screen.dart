import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/trading_simulator_provider.dart';
import '../models/trading_simulator_models.dart' as models;
import 'crypto_trading_simulator_screen.dart';
import 'simulator_settings_screen.dart';

class SimulatorMenuScreen extends StatelessWidget {
  const SimulatorMenuScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CupertinoColors.systemBackground,
      appBar: AppBar(
        backgroundColor: CupertinoColors.systemBackground,
        elevation: 0,
        title: const Text(
          'Crypto Trading Simulator',
          style: TextStyle(
            color: CupertinoColors.label,
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Infinite Patterns Mode
          _buildModeCard(
            context,
            'Infinite Patterns',
            'Trade with random patterns and test your skills',
            CupertinoIcons.arrow_up_arrow_down,
            CupertinoColors.systemGreen,
            () => _launchSimulator(
              context,
              models.SimulatorMode.infinitePatterns,
            ),
          ),
          const SizedBox(height: 16),

          // Practice Mode
          _buildModeCard(
            context,
            'Practice Mode',
            'Learn trading patterns with predefined scenarios',
            CupertinoIcons.book,
            CupertinoColors.systemBlue,
            () => _launchSimulator(
              context,
              models.SimulatorMode.practice,
            ),
          ),
          const SizedBox(height: 16),

          // Custom Mode
          _buildModeCard(
            context,
            'Custom Mode',
            'Configure your own trading environment',
            CupertinoIcons.slider_horizontal_3,
            CupertinoColors.systemPurple,
            () => _launchSimulator(
              context,
              models.SimulatorMode.custom,
            ),
          ),
          const SizedBox(height: 32),

          // Settings Button
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              Navigator.push(
                context,
                CupertinoPageRoute(
                  builder: (context) => const SimulatorSettingsScreen(),
                ),
              );
            },
            child: Container(
              width: double.infinity,
              height: 50,
              decoration: BoxDecoration(
                color: CupertinoColors.systemGrey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Text(
                  'Settings',
                  style: TextStyle(
                    color: CupertinoColors.label,
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModeCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: CupertinoColors.systemBackground,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: CupertinoColors.systemGrey.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: CupertinoColors.label,
                      fontSize: 17,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      color: CupertinoColors.secondaryLabel,
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              CupertinoIcons.chevron_right,
              color: CupertinoColors.secondaryLabel,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _launchSimulator(BuildContext context, models.SimulatorMode mode) {
    final provider = context.read<TradingSimulatorProvider>();
    provider.setMode(mode);
    
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (context) => CryptoTradingSimulatorScreen(
          mode: mode,
          leverage: provider.leverage,
          initialBalance: provider.balance,
          symbol: provider.currentSymbol,
          timeframe: provider.currentTimeframe,
        ),
      ),
    );
  }
} 