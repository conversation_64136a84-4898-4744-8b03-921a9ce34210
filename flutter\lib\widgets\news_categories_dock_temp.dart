import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Добавляем импорт для HapticFeedback
import 'package:flutter_svg/flutter_svg.dart'; // Для SVG иконок
import 'icons/category_icons.dart'; // Наши кастомные SVG иконки
import 'package:finance_ai/models/sentiment_types.dart';

/// Виджет топбара для категорий новостей с анимированным эффектом при нажатии
class NewsCategoriesDock extends StatefulWidget {
  final Function(String) onCategoryChanged;
  final String selectedCategory;
  final Color selectedColor;
  final Color unselectedColor;
  final double itemSpacing;
  final double height;
  
  const NewsCategoriesDock({
    Key? key,
    required this.onCategoryChanged,
    this.selectedCategory = 'all',
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.itemSpacing = 16.0,
    this.height = 56.0,
  }) : super(key: key);

  @override
  State<NewsCategoriesDock> createState() => _NewsCategoriesDockState();
}

class _NewsCategoriesDockState extends State<NewsCategoriesDock> with SingleTickerProviderStateMixin {
  String? _hoveredCategory;
  late AnimationController _animationController;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildCategoryItem(
                'all', 
                'All', 
                Icons.public,
              ),
              SizedBox(width: widget.itemSpacing),
              _buildCategoryItem(
                'crypto', 
                'Crypto', 
                Icons.trending_up,
              ),
              SizedBox(width: widget.itemSpacing),
              _buildCategoryItem(
                'stock', 
                'Stock', 
                Icons.show_chart,
              ),
              SizedBox(width: widget.itemSpacing),
              _buildCategoryItem(
                'whales', 
                'Whales', 
                Icons.water,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryItem(String category, String label, IconData icon) {
    final bool isSelected = widget.selectedCategory == category;
    final bool isHovered = _hoveredCategory == category;
    
    return GestureDetector(
      onTap: () => widget.onCategoryChanged(category),
      child: MouseRegion(
        onEnter: (_) => setState(() {
          _hoveredCategory = category;
          _animationController.forward();
        }),
        onExit: (_) => setState(() {
          _hoveredCategory = null;
          _animationController.reverse();
        }),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          transform: Matrix4.identity()
            ..scale(isHovered || isSelected ? 1.1 : 1.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? widget.selectedColor : widget.unselectedColor,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? widget.selectedColor : widget.unselectedColor,
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Константы для док-панели
const double kDefaultDockHeight = 50.0;  // Высота панели
const double kDefaultDockWidth = 320.0;  // Ширина панели
const double kExpandedDockWidth = 380.0;  // Расширенная ширина
const double kDefaultIconSize = 20.0;  // Размер иконок
const double kHoverIconSize = 18.0;    // Размер иконок при наведении
const double kHoverDistance = 80.0;    // Радиус действия
const Duration kAnimationDuration = Duration(milliseconds: 200);  // Анимация

/// Более продвинутая версия с анимацией в стиле док-панели MacOS
class AnimatedNewsCategoriesDock extends StatefulWidget {
  final Function(String) onCategoryChanged;
  final String selectedCategory;
  final Color selectedColor;
  final Color unselectedColor;
  final double itemSpacing;
  final double height;
  final double defaultWidth;
  final double expandedWidth;
  final bool useGlow;
  final List<Color> gradientColors;
  final double shrinkFactor;
  
  const AnimatedNewsCategoriesDock({
    Key? key,
    required this.onCategoryChanged,
    this.selectedCategory = 'all',
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.itemSpacing = 8.0,
    this.height = kDefaultDockHeight,
    this.defaultWidth = kDefaultDockWidth,
    this.expandedWidth = kExpandedDockWidth,
    this.useGlow = true,
    this.gradientColors = const [Color(0xFF1A1A1A), Color(0xFF303030)],
    this.shrinkFactor = 0.9,
  }) : super(key: key);

  @override
  State<AnimatedNewsCategoriesDock> createState() => _AnimatedNewsCategoriesDockState();
}

class _AnimatedNewsCategoriesDockState extends State<AnimatedNewsCategoriesDock> with SingleTickerProviderStateMixin {
  String? _hoveredCategory;
  bool _isDockHovered = false;
  Offset? _mousePosition;
  
  // Анимация расширения/сжатия ширины дока
  late AnimationController _widthController;
  late Animation<double> _widthAnimation;
  late Animation<double> _glowAnimation;
  
  // Ключи для каждого элемента, чтобы иметь доступ к их позициям
  final Map<String, GlobalKey> _keys = {
    'all': GlobalKey(),
    'crypto': GlobalKey(),
    'stock': GlobalKey(),
    'whales': GlobalKey(),
  };
  
  @override
  void initState() {
    super.initState();
    
    _widthController = AnimationController(
      vsync: this,
      duration: kAnimationDuration,
    );
    
    _widthAnimation = Tween<double>(
      begin: widget.defaultWidth,
      end: widget.expandedWidth,
    ).animate(CurvedAnimation(
      parent: _widthController, 
      curve: Curves.easeOutCubic,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _widthController, 
      curve: Curves.easeOutCubic,
    ));
  }
  
  @override
  void dispose() {
    _widthController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0), // Уменьшенный отступ
      child: MouseRegion(
        onEnter: (_) {
          setState(() {
            _isDockHovered = true;
          });
          _widthController.forward();
        },
        onHover: (event) {
          setState(() {
            _mousePosition = event.position;
          });
        },
        onExit: (_) {
          setState(() {
            _mousePosition = null;
            _isDockHovered = false;
          });
          _widthController.reverse();
        },
        child: RepaintBoundary(
          child: AnimatedBuilder(
            animation: Listenable.merge([_widthAnimation, _glowAnimation]),
            builder: (context, child) {
              return Container(
                height: widget.height + 8, // Достаточно места для контента
                width: _widthAnimation.value,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: widget.gradientColors,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                    if (widget.useGlow) BoxShadow(
                      color: widget.selectedColor.withOpacity(0.2 * _glowAnimation.value),
                      blurRadius: 10,
                      spreadRadius: 1 * _glowAnimation.value,
                    ),
                  ],
                ),
                // Используем ClipRRect, чтобы обрезать все содержимое по краям контейнера
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          _buildAnimatedCategoryItem(
                            'all', 
                            'All', 
                            Icons.public,
                          ),
                          SizedBox(width: widget.itemSpacing),
                          _buildAnimatedCategoryItem(
                            'crypto', 
                            'Crypto', 
                            CryptoIcon(
                              size: kDefaultIconSize,
                              color: widget.selectedCategory == 'crypto'
                                ? widget.selectedColor
                                : widget.unselectedColor,
                            ),
                          ),
                          SizedBox(width: widget.itemSpacing),
                          _buildAnimatedCategoryItem(
                            'stock', 
                            'Stock', 
                            StockMarketIcon(
                              size: kDefaultIconSize,
                              color: widget.selectedCategory == 'stock'
                                ? widget.selectedColor
                                : widget.unselectedColor,
                            ),
                          ),
                          SizedBox(width: widget.itemSpacing),
                          _buildAnimatedCategoryItem(
                            'whales', 
                            'Whales', 
                            WhaleIcon(
                              size: kDefaultIconSize,
                              color: widget.selectedCategory == 'whales'
                                ? widget.selectedColor
                                : widget.unselectedColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedCategoryItem(String category, String label, dynamic icon) {
    final bool isSelected = widget.selectedCategory == category;
    final key = _keys[category]!;
    
    // Расчет уменьшения в зависимости от позиции мыши
    double scale = 1.0; // По умолчанию обычный размер
    if (_mousePosition != null && _isDockHovered) {
      final RenderBox? renderBox = key.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final position = renderBox.localToGlobal(Offset.zero);
        final size = renderBox.size;
        final itemCenterX = position.dx + size.width / 2;
        
        // Расстояние между центром элемента и курсором мыши
        final distance = (_mousePosition!.dx - itemCenterX).abs();
        
        // Максимальное расстояние для эффекта уменьшения
        const double maxDistance = kHoverDistance;
        
        if (distance < maxDistance) {
          // Вычисляем фактор уменьшения - чем ближе курсор, тем меньше иконка
          double shrinkPercent = (1 - distance / maxDistance); // 0-1, где 1 - прямо над курсором
          // Линейно интерполируем между 1.0 и фактором уменьшения
          scale = 1.0 - (1.0 - widget.shrinkFactor) * shrinkPercent;
          // Обновляем состояние для плавной анимации
          WidgetsBinding.instance.addPostFrameCallback((_) => setState(() {}));
        }
      }
    }
    
    // Если элемент выбран, не уменьшаем его даже при наведении
    if (isSelected) {
      scale = 1.0;  // Обычный размер для выбранного элемента
    }
    
    final bool isHovered = _hoveredCategory == category;
    
    // На сколько ярким и насыщенным должен быть цвет (0.0 - 1.0)
    final double colorIntensity = isSelected ? 1.0 : (isHovered ? 0.95 : 0.7);
    final Color itemColor = isSelected ? widget.selectedColor : widget.unselectedColor;
    
    return GestureDetector(
      onTap: () => widget.onCategoryChanged(category),
      child: MouseRegion(
        onEnter: (_) => setState(() {
          _hoveredCategory = category;
          // Добавляем небольшую вибрацию при наведении для привлечения внимания
          try {
            HapticFeedback.lightImpact();
          } catch (e) {
            // Игнорируем ошибки вибрации
          }
        }),
        onExit: (_) => setState(() {
          _hoveredCategory = null;
        }),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          // Фиксированные размеры без возможности переполнения
          width: 40, // Фиксированная ширина
          height: 42, // Фиксированная высота
          padding: const EdgeInsets.all(2.0),
          margin: EdgeInsets.symmetric(horizontal: 2.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Иконка с эффектом свечения
              Container(
                height: 24,
                width: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected || isHovered 
                      ? itemColor.withOpacity(0.15) 
                      : Colors.transparent,
                ),
                child: Center(
                  child: icon is IconData
                    ? Icon(
                        icon,
                        color: itemColor.withOpacity(colorIntensity),
                        size: kDefaultIconSize * scale,
                      )
                    : SizedBox(
                        height: kDefaultIconSize * scale,
                        width: kDefaultIconSize * scale,
                        child: icon,
                      ),
                ),
              ),
              const SizedBox(height: 2),
              // Текст под иконкой
              SizedBox(
                width: 36,
                child: Text(
                  label,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                    color: itemColor.withOpacity(colorIntensity),
                    fontWeight: isSelected ? FontWeight.bold : 
                               isHovered ? FontWeight.w600 : FontWeight.normal,
                    fontSize: 9, // Очень маленький шрифт
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
