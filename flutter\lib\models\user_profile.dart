class UserProfile {
  final String id;
  final String name;
  final String email;
  final String photoUrl;
  final UserStats stats;
  final UserSettings settings;
  final List<String> favoriteCryptos;

  UserProfile({
    required this.id,
    required this.name,
    required this.email,
    required this.photoUrl,
    required this.stats,
    required this.settings,
    required this.favoriteCryptos,
  });

  // Mock data factory
  static UserProfile getMockUser() {
    return UserProfile(
      id: 'user123',
      name: '<PERSON>',
      email: '<EMAIL>',
      photoUrl: 'https://via.placeholder.com/150?text=AJ',
      stats: UserStats(
        newsRead: 42,
        coursesCompleted: 2,
        gamesPlayed: 5,
        analysesCreated: 7,
      ),
      settings: UserSettings(
        darkMode: true,
        notificationsEnabled: true,
        language: 'English',
        currency: 'USD',
      ),
      favoriteCryptos: ['bitcoin', 'ethereum', 'solana'],
    );
  }
}

class UserStats {
  final int newsRead;
  final int coursesCompleted;
  final int gamesPlayed;
  final int analysesCreated;

  UserStats({
    required this.newsRead,
    required this.coursesCompleted,
    required this.gamesPlayed,
    required this.analysesCreated,
  });
}

class UserSettings {
  final bool darkMode;
  final bool notificationsEnabled;
  final String language;
  final String currency;

  UserSettings({
    required this.darkMode,
    required this.notificationsEnabled,
    required this.language,
    required this.currency,
  });
}
