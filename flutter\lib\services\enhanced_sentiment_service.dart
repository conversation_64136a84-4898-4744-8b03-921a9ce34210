import 'dart:convert'; // Для JSON операций
import 'dart:math' as math; // Заменяю Random на math для детерминированных расчетов
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentiment_history_model.dart';
import 'stable_market_analytics.dart'; // Добавляю импорт стабильной аналитики

/// Enhanced service for fetching and calculating market sentiment with improved stability
/// and more accurate predictions using deterministic calculations
class EnhancedSentimentService {
  // Cache keys
  static const String _lastUpdateCacheKey = 'enhanced_sentiment_last_update';
  static const String _historicalDataKey = 'enhanced_sentiment_historical_data';
  static const String _predictionsKey = 'enhanced_sentiment_predictions';
  static const String _predictionsTimestampKey = 'enhanced_sentiment_predictions_timestamp';

  // Cache duration (10 minutes)
  static const int _cacheDurationMillis = 10 * 60 * 1000;

  // Singleton instance
  static final EnhancedSentimentService _instance = EnhancedSentimentService._internal();

  // Factory constructor
  factory EnhancedSentimentService() => _instance;

  // Private constructor
  EnhancedSentimentService._internal();

  /// Check if cache is valid
  Future<bool> isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdate = prefs.getInt(_lastUpdateCacheKey);

      if (lastUpdate == null) {
        return false;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      return (now - lastUpdate) < _cacheDurationMillis;
    } catch (e) {
      debugPrint('Error checking cache validity: $e');
      return false;
    }
  }

  /// Calculate the market sentiment indicator using stable analytics
  Future<double> calculateMarketSentiment() async {
    try {
      debugPrint('Calculating market sentiment using stable analytics...');

      // Проверяем кэш (10 минут)
      if (await isCacheValid()) {
        final history = await getHistoricalData();
        if (history.entries.isNotEmpty) {
          final lastValue = history.entries.first.value;
          debugPrint('Using cached sentiment value: $lastValue');
          return lastValue;
        }
      }

      // Получаем стабильные метрики
      final stableMetrics = await StableMarketAnalytics.fetchStableMetrics();
      final indicator = StableMarketAnalytics.calculateStableIndicator(stableMetrics);

      debugPrint('Calculated stable sentiment indicator: $indicator');

      // Сохраняем в историю
      await saveToHistory(indicator, stableMetrics);

      // Обновляем временную метку последнего обновления
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastUpdateCacheKey, DateTime.now().millisecondsSinceEpoch);

      return indicator;
    } catch (e) {
      debugPrint('Error calculating market sentiment: $e');
      
      // В случае ошибки возвращаем нейтральное значение
      return 50.0;
    }
  }

  /// Save current sentiment data to history
  Future<void> saveToHistory(double indicatorValue, Map<String, double> metrics) async {
    try {
      // Create a new history entry
      final entry = SentimentHistoryEntry(
        date: DateTime.now(),
        value: indicatorValue,
        metrics: metrics,
      );

      // Get current history
      final history = await getHistoricalData();

      // Add entry
      history.addEntry(entry);

      // Save updated history
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_historicalDataKey, history.toJson());

      debugPrint('Saved to history: ${entry.date} - ${entry.value}');
    } catch (e) {
      debugPrint('Error saving to history: $e');
    }
  }

  /// Get historical data
  Future<SentimentHistory> getHistoricalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_historicalDataKey);

      if (historyJson != null) {
        return SentimentHistory.fromJson(historyJson);
      }

      return SentimentHistory(entries: []);
    } catch (e) {
      debugPrint('Error getting historical data: $e');
      return SentimentHistory(entries: []);
    }
  }

  /// Predict future values using stable prediction engine
  Future<List<SentimentHistoryEntry>> predictFutureValues(int daysAhead) async {
    try {
      debugPrint('Generating stable predictions for $daysAhead days...');

      // Получаем текущее значение индикатора
      final currentValue = await calculateMarketSentiment();
      debugPrint('Current indicator value for prediction: $currentValue');

      // Получаем историю для анализа тренда
      final history = await getHistoricalData();
      final historicalValues = history.entries.map((e) => e.value).toList();

      // Создаем стабильные предсказания
      final predictions = <SentimentHistoryEntry>[];
      final today = DateTime.now();
      final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;

      // Детерминированные параметры модели
      final trendDamping = 0.88; // Затухание тренда
      final cyclePeriod = 7.2; // Период цикла в днях
      final meanReversion = 0.18; // Возврат к среднему

      // Рассчитываем базовый тренд
      double baseTrend = 0.0;
      if (historicalValues.length >= 2) {
        baseTrend = (currentValue - historicalValues.last) * 0.5;
      }

      // Генерируем предсказания
      double lastValue = currentValue;
      for (int i = 0; i < daysAhead; i++) {
        final futureDate = today.add(Duration(days: i + 1));
        final dayIndex = i + 1;

        // Циклическая компонента (детерминированная)
        final cyclicalComponent = math.sin((dayOfYear + dayIndex) * 2 * math.pi / cyclePeriod) * 2.5;

        // Тренд с затуханием
        final trendComponent = baseTrend * math.pow(trendDamping, dayIndex);

        // Возврат к среднему (50)
        final meanReversionComponent = (50.0 - lastValue) * meanReversion;

        // Детерминированная волатильность на основе дня
        final volatilityFactor = math.sin((dayOfYear + dayIndex) * 0.5) * 1.5;

        // Рассчитываем предсказанное значение
        final predictedValue = (lastValue + 
                               trendComponent + 
                               cyclicalComponent + 
                               meanReversionComponent + 
                               volatilityFactor).clamp(15.0, 85.0);

        // Создаем запись предсказания
        predictions.add(SentimentHistoryEntry(
          date: futureDate,
          value: predictedValue,
          metrics: {
            'prediction': 1.0,
            'confidence': (0.9 - (dayIndex * 0.05)).clamp(0.5, 0.9),
            'trend_component': trendComponent,
            'cyclical_component': cyclicalComponent,
            'mean_reversion': meanReversionComponent,
            'volatility': volatilityFactor.abs(),
          },
        ));

        lastValue = predictedValue;
      }

      debugPrint('Generated stable predictions: ${predictions.map((e) => e.value.toStringAsFixed(1)).toList()}');
      return predictions;
    } catch (e) {
      debugPrint('Error predicting future values: $e');
      return _generateStableFallbackPredictions(daysAhead);
    }
  }

  /// Генерирует стабильные резервные предсказания
  List<SentimentHistoryEntry> _generateStableFallbackPredictions(int daysAhead) {
    final predictions = <SentimentHistoryEntry>[];
    final today = DateTime.now();
    final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;

    debugPrint('Generating stable fallback predictions...');

    // Базовое значение на основе дня года
    double baseValue = 50.0 + math.sin(dayOfYear * 2 * math.pi / 365) * 10.0;

    for (int i = 0; i < daysAhead; i++) {
      final futureDate = today.add(Duration(days: i + 1));
      final dayIndex = i + 1;

      // Простая детерминированная модель
      final cyclicalChange = math.sin((dayOfYear + dayIndex) * 0.3) * 3.0;
      final trendChange = math.cos((dayOfYear + dayIndex) * 0.1) * 1.5;
      
      final finalValue = (baseValue + cyclicalChange + trendChange).clamp(20.0, 80.0);

      predictions.add(SentimentHistoryEntry(
        date: futureDate,
        value: finalValue,
        metrics: {
          'prediction': 1.0,
          'confidence': 0.6,
          'fallback': 1.0,
        },
      ));

      baseValue = finalValue;
    }

    debugPrint('Fallback predictions: ${predictions.map((e) => e.value.toStringAsFixed(1)).toList()}');
    return predictions;
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastUpdateCacheKey);
      await prefs.remove(_predictionsKey);
      await prefs.remove(_predictionsTimestampKey);

      // Clear all metric caches
      final keys = prefs.getKeys();
      for (final key in keys) {
        if (key.endsWith('_timestamp')) {
          await prefs.remove(key);
          await prefs.remove(key.replaceAll('_timestamp', ''));
        }
      }

      debugPrint('All caches cleared');
    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }
}
