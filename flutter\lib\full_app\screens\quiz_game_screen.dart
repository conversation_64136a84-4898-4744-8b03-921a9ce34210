import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:async';
import 'dart:math' as math;
import '../models/quiz_question.dart';
import '../services/quiz_service.dart';
import 'quiz_results_screen.dart';

class QuizGameScreen extends StatefulWidget {
  const QuizGameScreen({Key? key}) : super(key: key);

  @override
  State<QuizGameScreen> createState() => _QuizGameScreenState();
}

class _QuizGameScreenState extends State<QuizGameScreen> 
    with TickerProviderStateMixin {
  late AnimationController _questionAnimationController;
  late AnimationController _timerAnimationController;
  late AnimationController _feedbackAnimationController;
  
  late Animation<double> _questionFadeAnimation;
  late Animation<Offset> _questionSlideAnimation;
  late Animation<double> _timerAnimation;
  late Animation<double> _feedbackScaleAnimation;
  late Animation<double> _feedbackOpacityAnimation;

  Timer? _questionTimer;
  int _timeLeft = 30;
  bool _isAnswered = false;
  String? _selectedAnswer;
  bool? _isCorrect;
  bool _showExplanation = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startQuestionTimer();
    _loadQuestion();
  }

  void _initializeAnimations() {
    _questionAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _timerAnimationController = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    );
    
    _feedbackAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _questionFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _questionAnimationController,
      curve: Curves.easeOut,
    ));

    _questionSlideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _questionAnimationController,
      curve: Curves.easeOut,
    ));

    _timerAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _timerAnimationController,
      curve: Curves.linear,
    ));

    _feedbackScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _feedbackAnimationController,
      curve: Curves.elasticOut,
    ));

    _feedbackOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _feedbackAnimationController,
      curve: Curves.easeOut,
    ));
  }

  void _loadQuestion() {
    setState(() {
      _isAnswered = false;
      _selectedAnswer = null;
      _isCorrect = null;
      _showExplanation = false;
      _timeLeft = 30;
    });

    _questionAnimationController.reset();
    _timerAnimationController.reset();
    _feedbackAnimationController.reset();

    _questionAnimationController.forward();
    _startQuestionTimer();
  }

  void _startQuestionTimer() {
    _timerAnimationController.forward();
    
    _questionTimer?.cancel();
    _questionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _timeLeft--;
        });

        if (_timeLeft <= 0 || _isAnswered) {
          timer.cancel();
          if (!_isAnswered) {
            _handleTimeUp();
          }
        }
      }
    });
  }

  void _handleTimeUp() {
    setState(() {
      _isAnswered = true;
      _isCorrect = false;
    });
    _showFeedback();
  }

  void _handleAnswer(String answer) {
    if (_isAnswered) return;

    setState(() {
      _isAnswered = true;
      _selectedAnswer = answer;
      _isCorrect = QuizService.instance.answerCurrentQuestion(answer);
    });

    _questionTimer?.cancel();
    _showFeedback();
  }

  void _showFeedback() {
    _feedbackAnimationController.forward();
    
    Timer(const Duration(milliseconds: 600), () {
      if (mounted) {
        setState(() {
          _showExplanation = true;
        });
      }
    });

    Timer(const Duration(seconds: 2), () {
      if (mounted) {
        _nextQuestion();
      }
    });
  }

  void _nextQuestion() {
    if (QuizService.instance.hasNextQuestion()) {
      QuizService.instance.nextQuestion();
      _loadQuestion();
    } else {
      _finishQuiz();
    }
  }

  void _finishQuiz() {
    Navigator.of(context).pushReplacement(
      CupertinoPageRoute(
        builder: (context) => const QuizResultsScreen(),
      ),
    );
  }

  @override
  void dispose() {
    _questionTimer?.cancel();
    _questionAnimationController.dispose();
    _timerAnimationController.dispose();
    _feedbackAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = QuizService.instance.currentQuestion;
    if (currentQuestion == null) {
      return const Scaffold(
        backgroundColor: Color(0xFF0A0A0A),
        body: Center(
          child: CupertinoActivityIndicator(color: Color(0xFF007AFF)),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildProgressSection(),
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    _buildQuestionCard(currentQuestion),
                    const SizedBox(height: 20),
                    _buildAnswerOptions(currentQuestion),
                    if (_showExplanation) ...[
                      const SizedBox(height: 16),
                      _buildExplanation(currentQuestion),
                    ],
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.08),
              borderRadius: BorderRadius.circular(10),
            ),
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () => _showExitDialog(),
              child: const Icon(
                CupertinoIcons.back,
                color: Color(0xFF007AFF),
                size: 20,
              ),
            ),
          ),
          const Spacer(),
          _buildTimer(),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.08),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              '${QuizService.instance.currentQuestionIndex + 1}/${QuizService.instance.sessionQuestionsCount}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    final progress = (QuizService.instance.currentQuestionIndex + 1) / 
                    QuizService.instance.sessionQuestionsCount;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.white.withOpacity(0.1),
          valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF007AFF)),
          minHeight: 4,
        ),
      ),
    );
  }

  Widget _buildTimer() {
    return AnimatedBuilder(
      animation: _timerAnimation,
      builder: (context, child) {
        final isUrgent = _timeLeft <= 10;
        final isVeryUrgent = _timeLeft <= 5;
        
        return Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isVeryUrgent 
                ? Colors.red.withOpacity(0.1) 
                : isUrgent 
                    ? Colors.orange.withOpacity(0.1)
                    : Colors.white.withOpacity(0.05),
            border: Border.all(
              color: isVeryUrgent 
                  ? Colors.red 
                  : isUrgent 
                      ? Colors.orange
                      : const Color(0xFF007AFF),
              width: 3,
            ),
            boxShadow: isUrgent ? [
              BoxShadow(
                color: (isVeryUrgent ? Colors.red : Colors.orange).withOpacity(0.3),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ] : null,
          ),
          child: Stack(
            children: [
              Positioned.fill(
                child: AnimatedBuilder(
                  animation: _timerAnimationController,
                  builder: (context, child) {
                    return CustomPaint(
                      painter: CircularTimerPainter(
                        progress: _timerAnimation.value,
                        color: isVeryUrgent 
                            ? Colors.red 
                            : isUrgent 
                                ? Colors.orange
                                : const Color(0xFF007AFF),
                        strokeWidth: 3,
                      ),
                    );
                  },
                ),
              ),
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '$_timeLeft',
                      style: TextStyle(
                        color: isVeryUrgent 
                            ? Colors.red 
                            : isUrgent 
                                ? Colors.orange
                                : Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                    Text(
                      'сек',
                      style: TextStyle(
                        color: isVeryUrgent 
                            ? Colors.red.withOpacity(0.7) 
                            : isUrgent 
                                ? Colors.orange.withOpacity(0.7)
                                : Colors.white.withOpacity(0.6),
                        fontSize: 8,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuestionCard(QuizQuestion question) {
    return FadeTransition(
      opacity: _questionFadeAnimation,
      child: SlideTransition(
        position: _questionSlideAnimation,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF007AFF).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${question.category.icon} ${question.category.displayName}',
                  style: const TextStyle(
                    color: Color(0xFF007AFF),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                question.question,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnswerOptions(QuizQuestion question) {
    return Column(
      children: question.options.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedAnswer == option;
        final isCorrect = question.correctAnswer == option;
        
        Color backgroundColor;
        Color borderColor;
        Color textColor = Colors.white;

        if (!_isAnswered) {
          backgroundColor = Colors.white.withOpacity(0.05);
          borderColor = Colors.white.withOpacity(0.15);
        } else {
          if (isCorrect) {
            backgroundColor = Colors.green.withOpacity(0.15);
            borderColor = Colors.green;
            textColor = Colors.green;
          } else if (isSelected && !isCorrect) {
            backgroundColor = Colors.red.withOpacity(0.15);
            borderColor = Colors.red;
            textColor = Colors.red;
          } else {
            backgroundColor = Colors.white.withOpacity(0.03);
            borderColor = Colors.white.withOpacity(0.08);
            textColor = Colors.white.withOpacity(0.6);
          }
        }

        return Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 10),
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: _isAnswered ? null : () => _handleAnswer(option),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: borderColor,
                  width: isSelected || (isCorrect && _isAnswered) ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: borderColor.withOpacity(0.2),
                      border: Border.all(color: borderColor),
                    ),
                    child: Center(
                      child: Text(
                        String.fromCharCode(65 + index), // A, B, C, D
                        style: TextStyle(
                          color: textColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      option,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (_isAnswered && isCorrect)
                    const Icon(
                      CupertinoIcons.checkmark_circle_fill,
                      color: Colors.green,
                      size: 20,
                    ),
                  if (_isAnswered && isSelected && !isCorrect)
                    const Icon(
                      CupertinoIcons.xmark_circle_fill,
                      color: Colors.red,
                      size: 20,
                    ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildExplanation(QuizQuestion question) {
    return FadeTransition(
      opacity: _feedbackOpacityAnimation,
      child: ScaleTransition(
        scale: _feedbackScaleAnimation,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: (_isCorrect == true ? Colors.green : Colors.orange).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _isCorrect == true ? Colors.green : Colors.orange,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _isCorrect == true 
                        ? CupertinoIcons.checkmark_circle 
                        : CupertinoIcons.info_circle,
                    color: _isCorrect == true ? Colors.green : Colors.orange,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isCorrect == true ? 'Правильно!' : 'Объяснение',
                    style: TextStyle(
                      color: _isCorrect == true ? Colors.green : Colors.orange,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                question.explanation,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 13,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showExitDialog() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Завершить викторину?'),
        content: const Text('Весь прогресс будет потерян.'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Отмена'),
            onPressed: () => Navigator.pop(context),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('Завершить'),
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}

class CircularTimerPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  CircularTimerPainter({
    required this.progress,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Фоновый круг
    final backgroundPaint = Paint()
      ..color = color.withOpacity(0.2)
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Прогресс
    final progressPaint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    const startAngle = -1.5708; // -90 degrees (top)
    final sweepAngle = 6.28319 * progress; // 2 * PI * progress

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(CircularTimerPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.color != color ||
           oldDelegate.strokeWidth != strokeWidth;
  }
} 