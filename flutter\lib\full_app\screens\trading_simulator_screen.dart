import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/trading_simulator_models.dart' as models;
import '../providers/trading_simulator_provider.dart';
import '../widgets/simple_candlestick_chart.dart';

class TradingSimulatorScreen extends StatelessWidget {
  const TradingSimulatorScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.white),
        title: Text('Crypto Trading Simulator', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Container(
          color: Colors.black,
          child: Consumer<TradingSimulatorProvider>(
            builder: (context, provider, _) {
              return Column(
                children: [
                  // Панель баланса и статистики
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Balance', style: TextStyle(color: Colors.white54)),
                            Text('${provider.balance.toStringAsFixed(2)}', style: TextStyle(color: Colors.white, fontSize: 22, fontWeight: FontWeight.bold)),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text('Rounds', style: TextStyle(color: Colors.white54)),
                            Text('${provider.roundsPlayed}', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text('Win Rate', style: TextStyle(color: Colors.white54)),
                            Text('${provider.winRate.toStringAsFixed(1)}%', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text('Deaths', style: TextStyle(color: Colors.white54)),
                            Text('${provider.deaths}', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // График
                  if (provider.currentCandles.isEmpty)
                    Container(
                      height: 400,
                      alignment: Alignment.center,
                      child: CircularProgressIndicator(color: Colors.white),
                    )
                  else
                    SimpleCandlestickChart(
                      candles: provider.currentCandles,
                      height: 400,
                      entryPrice: provider.entryPrice,
                    ),
                  // Прибыль/убыток после трейда
                  if (provider.lastTradeProfit != null)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12.0),
                      child: AnimatedOpacity(
                        opacity: 1.0,
                        duration: Duration(milliseconds: 600),
                        child: Text(
                          provider.lastTradeProfit! >= 0
                            ? '+${provider.lastTradeProfit!.toStringAsFixed(2)}'
                            : provider.lastTradeProfit!.toStringAsFixed(2),
                          style: TextStyle(
                            color: provider.lastTradeProfit! >= 0 ? Color(0xFF4ADE80) : Color(0xFFF87171),
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            shadows: [Shadow(color: Colors.black, blurRadius: 8)],
                          ),
                        ),
                      ),
                    ),
                  // Кнопки трейда
                  Padding(
                    padding: const EdgeInsets.only(bottom: 24.0, top: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFF4ADE80),
                            foregroundColor: Colors.black,
                            padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          ),
                          onPressed: () {
                            provider.makeTrade(models.TradeAction.buy);
                          },
                          child: Text('Вверх', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                        ),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFFF87171),
                            foregroundColor: Colors.black,
                            padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          ),
                          onPressed: () {
                            provider.makeTrade(models.TradeAction.sell);
                          },
                          child: Text('Вниз', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
