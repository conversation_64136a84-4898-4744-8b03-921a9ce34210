import 'package:flutter/material.dart';
import 'simplified_trading_simulator.dart';

/// Экран настроек для упрощенного симулятора торговли
class SimplifiedTradingSettings extends StatefulWidget {
  final String mode; // 'custom' или 'infinite'

  const SimplifiedTradingSettings({
    super.key,
    required this.mode,
  });

  @override
  State<SimplifiedTradingSettings> createState() => _SimplifiedTradingSettingsState();
}

class _SimplifiedTradingSettingsState extends State<SimplifiedTradingSettings> {
  // Настройки для Custom Mode
  String selectedSymbol = 'BTCUSDT';
  String selectedTimeframe = '1h';
  
  // Настройки для обоих режимов
  int selectedLeverage = 10;
  
  // Доступные опции
  final List<String> symbols = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOGEUSDT', 'XRPUSDT',
    'SOLUSDT', 'DOTUSDT', 'AVAXUSDT', 'MATICUSDT'
  ];
  
  final List<String> timeframes = [
    '15m', '30m', '1h', '4h', '1d'
  ];
  
  final List<int> leverages = [
    1, 2, 5, 10, 20, 50, 100, 200, 500, 1000
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.mode == 'custom' ? 'Custom Mode Settings' : 'Infinite Mode Settings'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Заголовок
            Text(
              widget.mode == 'custom' ? 'Configure Your Trading Experience' : 'Select Leverage',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            
            // Настройки для Custom Mode
            if (widget.mode == 'custom') ...[
              // Выбор криптовалюты
              _buildSectionTitle('Cryptocurrency'),
              const SizedBox(height: 8),
              _buildSymbolSelector(),
              const SizedBox(height: 24),
              
              // Выбор таймфрейма
              _buildSectionTitle('Timeframe'),
              const SizedBox(height: 8),
              _buildTimeframeSelector(),
              const SizedBox(height: 24),
            ],
            
            // Выбор кредитного плеча (для обоих режимов)
            _buildSectionTitle('Leverage'),
            const SizedBox(height: 8),
            _buildLeverageSelector(),
            const SizedBox(height: 40),
            
            // Кнопка Start Trading
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => SimplifiedTradingSimulator(
                        mode: widget.mode,
                        symbol: widget.mode == 'custom' ? selectedSymbol : null,
                        timeframe: widget.mode == 'custom' ? selectedTimeframe : null,
                        leverage: selectedLeverage,
                      ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Start Trading',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Строит заголовок секции
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  /// Строит селектор криптовалюты
  Widget _buildSymbolSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[800]!),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedSymbol,
          isExpanded: true,
          icon: const Icon(Icons.arrow_drop_down),
          iconSize: 24,
          elevation: 16,
          style: const TextStyle(color: Colors.white),
          dropdownColor: Colors.grey[850],
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                selectedSymbol = newValue;
              });
            }
          },
          items: symbols.map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(value),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Строит селектор таймфрейма
  Widget _buildTimeframeSelector() {
    return Row(
      children: timeframes.map((timeframe) {
        final isSelected = timeframe == selectedTimeframe;
        return Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                selectedTimeframe = timeframe;
              });
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue : Colors.grey[900],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? Colors.blue.withOpacity(0.5) : Colors.grey[800]!,
                ),
              ),
              child: Text(
                timeframe,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey[400],
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Строит селектор кредитного плеча
  Widget _buildLeverageSelector() {
    return Column(
      children: [
        // Слайдер
        Slider(
          value: selectedLeverage.toDouble(),
          min: 1,
          max: 1000,
          divisions: 999,
          label: '${selectedLeverage}x',
          onChanged: (double value) {
            setState(() {
              selectedLeverage = value.round();
            });
          },
        ),
        
        // Текущее значение
        Text(
          '${selectedLeverage}x',
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.amber,
          ),
        ),
        
        // Быстрый выбор
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: leverages.map((leverage) {
            final isSelected = leverage == selectedLeverage;
            return GestureDetector(
              onTap: () {
                setState(() {
                  selectedLeverage = leverage;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.amber : Colors.grey[900],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? Colors.amber.withOpacity(0.5) : Colors.grey[800]!,
                  ),
                ),
                child: Text(
                  '${leverage}x',
                  style: TextStyle(
                    color: isSelected ? Colors.black : Colors.grey[400],
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
