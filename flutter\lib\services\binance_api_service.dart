import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:http/http.dart' as http;
import 'package:web_socket_channel/web_socket_channel.dart';
import '../models/trading_simulator_models.dart';

/// Service for interfacing with the Binance API
class BinanceApiService {
  static const String _restBaseUrl = 'https://api.binance.com/api/v3';
  static const String _wsBaseUrl = 'wss://stream.binance.com:9443/ws';
  
  // API rate limits
  static const int _maxRequestsPerMinute = 1200;
  static const Duration _cacheDuration = Duration(minutes: 5);
  
  // In-memory cache for responses
  final Map<String, _CachedResponse> _cache = {};
  
  // Request counter for rate limiting
  int _requestCount = 0;
  DateTime _requestCountResetTime = DateTime.now();
  
  /// Fetch historical candle data from Binance
  Future<List<CandleData>> fetchHistoricalCandles({
    required String symbol,
    required TimeFrame timeFrame,
    int limit = 250,
  }) async {
    // Check cache first
    final cacheKey = 'candles-$symbol-${timeFrame.apiValue}-$limit';
    final cachedData = _getCachedData(cacheKey);
    if (cachedData != null) {
      return cachedData.cast<CandleData>();
    }
    
    // Handle rate limiting
    if (!_checkRateLimit()) {
      return _generateSyntheticCandles(symbol, timeFrame, limit);
    }
    
    try {
      final url = '$_restBaseUrl/klines?symbol=${symbol}USDT&interval=${timeFrame.apiValue}&limit=$limit';
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        final candles = data.map((candle) => CandleData.fromBinanceData(candle)).toList();
        
        // Cache the response
        _cacheResponse(cacheKey, candles);
        
        return candles;
      } else {
        throw Exception('Failed to load candles: ${response.statusCode}');
      }
    } catch (e) {
      // If API call fails, generate synthetic data
      return _generateSyntheticCandles(symbol, timeFrame, limit);
    }
  }
  
  /// Fetch historical candle data from Binance with a specific end time
  Future<List<CandleData>> fetchHistoricalCandlesWithEndTime({
    required String symbol,
    required TimeFrame timeFrame,
    required DateTime endTime,
    int limit = 250,
  }) async {
    // Check cache first
    final endTimeMs = endTime.millisecondsSinceEpoch;
    final cacheKey = 'candles-$symbol-${timeFrame.apiValue}-$limit-$endTimeMs';
    final cachedData = _getCachedData(cacheKey);
    if (cachedData != null) {
      return cachedData.cast<CandleData>();
    }
    
    // Handle rate limiting
    if (!_checkRateLimit()) {
      return _generateSyntheticCandles(symbol, timeFrame, limit, endTime: endTime);
    }
    
    try {
      final url = '$_restBaseUrl/klines?symbol=${symbol}USDT&interval=${timeFrame.apiValue}&limit=$limit&endTime=$endTimeMs';
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        final candles = data.map((candle) => CandleData.fromBinanceData(candle)).toList();
        
        // Cache the response
        _cacheResponse(cacheKey, candles);
        
        return candles;
      } else {
        throw Exception('Failed to load candles: ${response.statusCode}');
      }
    } catch (e) {
      // If API call fails, generate synthetic data
      return _generateSyntheticCandles(symbol, timeFrame, limit, endTime: endTime);
    }
  }
  
  /// Fetch order book data from Binance
  Future<Map<String, dynamic>> fetchOrderBook({
    required String symbol,
    int limit = 20,
  }) async {
    // Check cache first
    final cacheKey = 'depth-$symbol-$limit';
    final cachedData = _getCachedData(cacheKey);
    if (cachedData != null) {
      return cachedData as Map<String, dynamic>;
    }
    
    // Handle rate limiting
    if (!_checkRateLimit()) {
      return _generateSyntheticOrderBook(symbol, limit);
    }
    
    try {
      final url = '$_restBaseUrl/depth?symbol=${symbol}USDT&limit=$limit';
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        
        // Cache the response
        _cacheResponse(cacheKey, data);
        
        return data;
      } else {
        throw Exception('Failed to load order book: ${response.statusCode}');
      }
    } catch (e) {
      // If API call fails, generate synthetic data
      return _generateSyntheticOrderBook(symbol, limit);
    }
  }
  
  /// Open a WebSocket connection for real-time price updates
  WebSocketChannel? connectToTickerStream(String symbol, Function(Map<String, dynamic>) onData) {
    try {
      final channel = WebSocketChannel.connect(
        Uri.parse('$_wsBaseUrl/${symbol.toLowerCase()}usdt@trade'),
      );
      
      channel.stream.listen(
        (dynamic message) {
          final data = jsonDecode(message as String) as Map<String, dynamic>;
          onData(data);
        },
        onError: (error) {
          print('WebSocket error: $error');
        },
        onDone: () {
          print('WebSocket connection closed');
        },
      );
      
      return channel;
    } catch (e) {
      print('Failed to connect to WebSocket: $e');
      return null;
    }
  }
  
  /// Open a WebSocket connection for real-time order book updates
  WebSocketChannel? connectToDepthStream(String symbol, Function(Map<String, dynamic>) onData) {
    try {
      final channel = WebSocketChannel.connect(
        Uri.parse('$_wsBaseUrl/${symbol.toLowerCase()}usdt@depth20@100ms'),
      );
      
      channel.stream.listen(
        (dynamic message) {
          final data = jsonDecode(message as String) as Map<String, dynamic>;
          onData(data);
        },
        onError: (error) {
          print('WebSocket error: $error');
        },
        onDone: () {
          print('WebSocket connection closed');
        },
      );
      
      return channel;
    } catch (e) {
      print('Failed to connect to WebSocket: $e');
      return null;
    }
  }
  
  /// Generate synthetic candle data (optionally with a specific end time)
  List<CandleData> _generateSyntheticCandles(
    String symbol,
    TimeFrame timeFrame,
    int limit, {
    DateTime? endTime,
  }) {
    final List<CandleData> candles = [];
    final now = endTime ?? DateTime.now();
    
    // Use symbol hash to generate consistent base price
    final symbolHash = symbol.hashCode;
    final basePrice = 1000.0 + (symbolHash % 60000);
    
    // Generate first candle
    final firstCandleTime = now.subtract(timeFrame.duration * limit);
    final firstCandle = CandleData(
      time: firstCandleTime,
      open: basePrice,
      high: basePrice * 1.01,
      low: basePrice * 0.99,
      close: basePrice * (1 + (symbolHash % 10) / 1000),
      volume: basePrice * 100,
    );
    
    candles.add(firstCandle);
    
    // Generate remaining candles with market phases
    int phaseLength = limit ~/ 4; // Divide chart into 4 phases
    
    for (int i = 1; i < limit; i++) {
      final previousCandle = candles[i - 1];
      
      // Determine current market phase
      int phase = i ~/ phaseLength;
      bool? forceTrend;
      double volatilityFactor = 1.0;
      
      switch (phase) {
        case 0: // Accumulation phase - sideways with low volatility
          forceTrend = null;
          volatilityFactor = 0.5;
          break;
        case 1: // Markup phase - uptrend with increasing volatility
          forceTrend = true;
          volatilityFactor = 1.0 + (i % phaseLength) / phaseLength;
          break;
        case 2: // Distribution phase - sideways with high volatility
          forceTrend = null;
          volatilityFactor = 1.5;
          break;
        case 3: // Markdown phase - downtrend with decreasing volatility
          forceTrend = false;
          volatilityFactor = 1.0 + ((phaseLength - (i % phaseLength)) / phaseLength);
          break;
      }
      
      // Generate next candle
      final nextCandle = CandleData.generateRandom(
        previousCandle,
        volatilityFactor: volatilityFactor,
        forceTrend: forceTrend,
      );
      
      candles.add(nextCandle);
    }
    
    return candles;
  }
  
  /// Generate synthetic order book data
  Map<String, dynamic> _generateSyntheticOrderBook(String symbol, int limit) {
    final random = math.Random();
    final symbolHash = symbol.hashCode;
    final basePrice = 1000.0 + (symbolHash % 60000);
    
    // Generate bids (buy orders) below the base price
    final List<List<String>> bids = [];
    double bidPrice = basePrice * 0.99;
    
    for (int i = 0; i < limit; i++) {
      final double bidQuantity = random.nextDouble() * 10 + 0.1;
      bids.add([bidPrice.toStringAsFixed(2), bidQuantity.toStringAsFixed(6)]);
      bidPrice -= random.nextDouble() * 0.5;
    }
    
    // Generate asks (sell orders) above the base price
    final List<List<String>> asks = [];
    double askPrice = basePrice * 1.01;
    
    for (int i = 0; i < limit; i++) {
      final double askQuantity = random.nextDouble() * 10 + 0.1;
      asks.add([askPrice.toStringAsFixed(2), askQuantity.toStringAsFixed(6)]);
      askPrice += random.nextDouble() * 0.5;
    }
    
    return {
      'lastUpdateId': DateTime.now().millisecondsSinceEpoch,
      'bids': bids,
      'asks': asks,
    };
  }
  
  /// Check if we're within rate limits
  bool _checkRateLimit() {
    final now = DateTime.now();
    
    // Reset counter if a minute has passed
    if (now.difference(_requestCountResetTime).inMinutes >= 1) {
      _requestCount = 0;
      _requestCountResetTime = now;
    }
    
    // Check if we've exceeded the rate limit
    if (_requestCount >= _maxRequestsPerMinute) {
      return false;
    }
    
    // Increment the counter
    _requestCount++;
    return true;
  }
  
  /// Get cached data if it exists and is not expired
  dynamic _getCachedData(String key) {
    final cachedResponse = _cache[key];
    if (cachedResponse != null && !cachedResponse.isExpired()) {
      return cachedResponse.data;
    }
    return null;
  }
  
  /// Cache a response
  void _cacheResponse(String key, dynamic data) {
    _cache[key] = _CachedResponse(
      data: data,
      timestamp: DateTime.now(),
    );
  }
}

/// Helper class for caching API responses
class _CachedResponse {
  final dynamic data;
  final DateTime timestamp;
  
  _CachedResponse({
    required this.data,
    required this.timestamp,
  });
  
  bool isExpired() {
    return DateTime.now().difference(timestamp) > BinanceApiService._cacheDuration;
  }
} 