import 'package:flutter/material.dart';
import '../models/news_item.dart';
import '../utils/modal_utils.dart';

class RelatedNewsPanel extends StatelessWidget {
  final List<NewsItem> relatedNews;
  final Function(NewsItem)? onNewsTap;
  final bool isScrollable;
  final double? maxHeight;

  const RelatedNewsPanel({
    Key? key,
    required this.relatedNews,
    this.onNewsTap,
    this.isScrollable = true,
    this.maxHeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final content = isScrollable
        ? SizedBox(
            height: maxHeight ?? 320,
            child: ListView.builder(
              itemCount: relatedNews.length,
              itemBuilder: (context, index) => _buildNewsItem(context, relatedNews[index]),
            ),
          )
        : Column(
            children: relatedNews.map((news) => _buildNewsItem(context, news)).toList(),
          );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Related News',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          content,
        ],
      ),
    );
  }

  Widget _buildNewsItem(BuildContext context, NewsItem news) {
    return GestureDetector(
      onTap: () {
        if (onNewsTap != null) {
          onNewsTap!(news);
        } else {
          showNewsDetailModal(
            context: context,
            newsItem: news,
          );
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.03),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              news.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Text(
                  news.source,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _formatDate(news.publishedAt),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return "${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}";
  }
} 