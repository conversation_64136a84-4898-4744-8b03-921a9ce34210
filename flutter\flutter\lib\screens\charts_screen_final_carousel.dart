import 'package:flutter/material.dart';
import '../models/crypto_currency.dart';
import '../widgets/crypto_carousel.dart';
import '../widgets/price_chart.dart';
import '../widgets/app_bottom_navigation.dart';

class ChartsScreenFinalCarousel extends StatefulWidget {
  const ChartsScreenFinalCarousel({super.key});

  @override
  State<ChartsScreenFinalCarousel> createState() => _ChartsScreenFinalCarouselState();
}

class _ChartsScreenFinalCarouselState extends State<ChartsScreenFinalCarousel> {
  final List<CryptoCurrency> _cryptos = CryptoCurrency.getMockItems();
  int _selectedCryptoIndex = 0;
  String _selectedTimeInterval = '24h';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Charts'),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Cryptocurrency carousel
          CryptoCarousel(
            cryptos: _cryptos,
            selectedIndex: _selectedCryptoIndex,
            onCryptoSelected: (index) {
              setState(() {
                _selectedCryptoIndex = index;
              });
            },
          ),

          // Price chart
          Expanded(
            child: PriceChart(
              crypto: _cryptos[_selectedCryptoIndex],
              timeInterval: _selectedTimeInterval,
            ),
          ),
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 1,
        onTap: (index) {
          if (index != 1) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 2:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/saved_analyses');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }
}
