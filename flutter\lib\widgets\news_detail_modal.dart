import 'dart:ui';
import 'package:flutter/material.dart';
import '../models/news_detail.dart';
import '../models/sentiment_types.dart';
import '../models/news_item.dart';
import '../services/ai_news_analysis_service.dart';
import 'package:url_launcher/url_launcher_string.dart';

class NewsDetailModal extends StatefulWidget {
  /// Global flag that preserves the preferred width (standard vs wide)
  static bool isWideMode = false;
  final NewsDetailData newsDetail;
  final VoidCallback onClose;

  const NewsDetailModal({
    Key? key,
    required this.newsDetail,
    required this.onClose,
  }) : super(key: key);

  @override
  State<NewsDetailModal> createState() => _NewsDetailModalState();
}

class _NewsDetailModalState extends State<NewsDetailModal>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  double _swipeProgress = 0.0;

  // Controller for internal content scroll to attach a beautiful scrollbar
  late ScrollController _contentController;

  // Состояние для анализа
  bool _isAnalyzing = false;
  FinancialAnalysisResult? _analysisResult;
  final AINewsAnalysisService _analysisService = AINewsAnalysisService();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _contentController = ScrollController();
    _animationController.forward();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 450),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.85, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOutQuart),
      ),
    );
  }

  Future<void> _closeModal() async {
    await _animationController.reverse();
    widget.onClose();
  }

  Future<void> _analyzeNews() async {
    if (_isAnalyzing) return;

    setState(() {
      _isAnalyzing = true;
      _analysisResult = null;
    });

    try {
      // Создаем NewsItem из NewsDetailData
      final newsItem = NewsItem(
        id: widget.newsDetail.url, // Используем URL как ID
        title: widget.newsDetail.title,
        description: widget.newsDetail.description,
        content: widget.newsDetail.fullContent.isNotEmpty
            ? widget.newsDetail.fullContent
            : widget.newsDetail.description,
        source: widget.newsDetail.source,
        publishedAt: widget.newsDetail.publishedAt,
        imageUrl: widget.newsDetail.imageUrl ?? 'https://via.placeholder.com/300x200?text=No+Image',
        url: widget.newsDetail.url,
        sentiment: widget.newsDetail.sentiment.sentiment,
        tags: widget.newsDetail.tags,
        category: NewsCategory.all, // Добавляем обязательный параметр
        fetchedAt: widget.newsDetail.fetchedAt, // Используем существующий fetchedAt
      );

      final result = await _analysisService.analyzeNewsFinancially(newsItem);

      setState(() {
        _analysisResult = result;
      });

      if (result == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to perform analysis. Please try again later.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      print('Analysis error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Analysis error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isAnalyzing = false;
      });
    }
  }

  @override
  void dispose() {
    _contentController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return _buildModalOverlay();
      },
    );
  }

  Widget _buildModalOverlay() {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GestureDetector(
        onTap: _closeModal,
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.2,
              colors: [
                Colors.black.withOpacity(0.3 * _fadeAnimation.value),
                Colors.black.withOpacity(0.6 * _fadeAnimation.value),
              ],
            ),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 15.0 * _fadeAnimation.value,
              sigmaY: 15.0 * _fadeAnimation.value,
            ),
            child: Center(
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Transform.translate(
                  offset: Offset(0, 20 * (1 - _fadeAnimation.value)),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: _buildModalContent(),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModalContent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final sentimentColor = _getSentimentColor(widget.newsDetail.sentiment.sentiment);
    final sentimentTint = sentimentColor.withOpacity(0.28);
    return GestureDetector(
      onTap: () {}, // Предотвращает закрытие при тапе на контент
      onPanUpdate: (details) {
        if (details.delta.dy > 0) {
          _swipeProgress += details.delta.dy / 300;
          setState(() {});
        }
      },
      onPanEnd: (details) {
        if (_swipeProgress > 0.3) {
          _closeModal();
        } else {
          setState(() => _swipeProgress = 0);
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 450),
        curve: Curves.easeOutCubic,
        width: screenWidth * (NewsDetailModal.isWideMode ? 0.70 : 0.92),
        constraints: BoxConstraints(
          maxWidth: screenWidth * (NewsDetailModal.isWideMode ? 0.70 : 0.92),
          maxHeight: screenHeight * 0.88,
        ),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        decoration: BoxDecoration(
          // Неоморфный дизайн с глубоким черным градиентом
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF0A0A0A).withOpacity(0.98),
              const Color(0xFF1A1A1A).withOpacity(0.95),
              const Color(0xFF000000).withOpacity(0.98),
            ],
          ),
          borderRadius: BorderRadius.circular(32),
          border: Border.all(
            color: Colors.white.withOpacity(0.08),
            width: 0.5,
          ),
          boxShadow: [
            // Внешняя тень
            BoxShadow(
              color: Colors.black.withOpacity(0.4),
              blurRadius: 40,
              offset: const Offset(0, 20),
              spreadRadius: -5,
            ),
            // Цветная тень от sentiment
            BoxShadow(
              color: sentimentColor.withOpacity(0.15),
              blurRadius: 30,
              offset: const Offset(0, 10),
              spreadRadius: -10,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(32),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 25, sigmaY: 25),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.white.withOpacity(0.08),
                    Colors.white.withOpacity(0.02),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // Статичный заголовок
                  _buildModernHeader(),
                  // Скроллируемый контент
                  Expanded(
                    child: RawScrollbar(
                      controller: _contentController,
                      thumbVisibility: true,
                      thickness: 4,
                      radius: const Radius.circular(8),
                      thumbColor: sentimentColor.withOpacity(0.6),
                      trackVisibility: false,
                      child: SingleChildScrollView(
                        controller: _contentController,
                        physics: const BouncingScrollPhysics(),
                        child: _buildAppleStyleContent(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernHeader() {
    final sentimentColor = _getSentimentColor(widget.newsDetail.sentiment.sentiment);

    return Container(
      padding: const EdgeInsets.fromLTRB(28, 12, 24, 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.05),
            Colors.white.withOpacity(0.01),
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withOpacity(0.08),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          const Spacer(),
          // Неоморфная кнопка переключения ширины
          _buildNeomorphicButton(
            onTap: () {
              setState(() => NewsDetailModal.isWideMode = !NewsDetailModal.isWideMode);
            },
            icon: NewsDetailModal.isWideMode
                ? Icons.fullscreen_exit_rounded
                : Icons.fullscreen_rounded,
            margin: const EdgeInsets.only(right: 12),
          ),
          // Неоморфная кнопка закрытия
          _buildNeomorphicButton(
            onTap: _closeModal,
            icon: Icons.close_rounded,
          ),
        ],
      ),
    );
  }

  int _calculateReadingTime(String content) {
    // More realistic reading time calculation
    final words = content.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
    // Base calculation: 200 words per minute
    double baseMinutes = words / 200.0;

    // Add complexity factors for more realistic estimation
    final sentences = content.split(RegExp(r'[.!?]+')).where((s) => s.trim().isNotEmpty).length;
    final avgWordsPerSentence = words / sentences;

    // Adjust for sentence complexity
    if (avgWordsPerSentence > 20) {
      baseMinutes *= 1.2; // Longer sentences take more time
    }

    // Add time for technical/financial content
    if (content.toLowerCase().contains(RegExp(r'\b(bitcoin|cryptocurrency|blockchain|defi|trading|market|analysis|financial)\b'))) {
      baseMinutes *= 1.15; // Technical content takes longer
    }

    // Round to realistic time ranges
    final minutes = baseMinutes.ceil();
    return minutes < 1 ? 1 : (minutes > 15 ? 15 : minutes);
  }

  Widget _buildReadingTime() {
    final content = widget.newsDetail.fullContent.isNotEmpty
        ? widget.newsDetail.fullContent
        : widget.newsDetail.description;
    final readingTime = _calculateReadingTime(content);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.schedule_rounded,
            size: 14,
            color: Colors.white.withOpacity(0.7),
          ),
          const SizedBox(width: 6),
          Text(
            '$readingTime min read',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
              fontWeight: FontWeight.w500,
              fontFamily: 'SF Pro Text',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSentimentIndicator() {
    final sentimentColor = _getSentimentColor(widget.newsDetail.sentiment.sentiment);
    final sentiment = widget.newsDetail.sentiment.sentiment;

    String sentimentText;
    IconData sentimentIcon;

    switch (sentiment) {
      case SentimentType.positive:
        sentimentText = 'BULLISH';
        sentimentIcon = Icons.trending_up_rounded;
        break;
      case SentimentType.negative:
        sentimentText = 'BEARISH';
        sentimentIcon = Icons.trending_down_rounded;
        break;
      default:
        sentimentText = 'NEUTRAL';
        sentimentIcon = Icons.trending_flat_rounded;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            sentimentColor.withOpacity(0.12),
            sentimentColor.withOpacity(0.06),
            Colors.transparent,
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: sentimentColor.withOpacity(0.25),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            sentimentIcon,
            color: sentimentColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            sentimentText,
            style: TextStyle(
              color: sentimentColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.8,
              fontFamily: 'SF Pro Display',
            ),
          ),
          const SizedBox(width: 4),
          Text(
            'Market Sentiment',
            style: TextStyle(
              color: Colors.white.withOpacity(0.5),
              fontSize: 12,
              fontWeight: FontWeight.w400,
              fontFamily: 'SF Pro Text',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNeomorphicButton({
    required VoidCallback onTap,
    required IconData icon,
    EdgeInsets? margin,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        margin: margin,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1A1A1A),
              Color(0xFF0F0F0F),
              Color(0xFF000000),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.08),
            width: 0.5,
          ),
          boxShadow: [
            // Внешняя тень
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(4, 4),
              spreadRadius: -2,
            ),
            // Внутренняя подсветка
            BoxShadow(
              color: Colors.white.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(-2, -2),
              spreadRadius: -3,
            ),
          ],
        ),
        child: Icon(
          icon,
          color: const Color(0xFF8E8E93),
          size: 20,
        ),
      ),
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final diff = now.difference(dateTime);
    if (diff.inMinutes < 1) {
      return 'Just now';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}h ago';
    } else if (diff.inDays == 1) {
      return 'Yesterday';
    } else {
      return '${diff.inDays}d ago';
    }
  }

  Color _getSentimentColor(SentimentType sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return const Color(0xFF30D158); // Apple Green
      case SentimentType.negative:
        return const Color(0xFFFF453A); // Apple Red
      default:
        return const Color(0xFF007AFF); // Apple Blue
    }
  }

  List<String> _splitToParagraphs(String text) {
    // 🧹 ОЧИСТКА ТЕКСТА: убираем лишние пробелы и нормализуем
    String cleanedText = text
        .replaceAll(RegExp(r'\s+'), ' ') // Заменяем множественные пробелы на одинарные
        .replaceAll(RegExp(r'\n\s*\n'), '\n\n') // Нормализуем переносы строк
        .trim();

    // Если есть явные разделители абзацев, используем их
    if (cleanedText.contains('\n\n')) {
      final naturalParagraphs = cleanedText
          .split('\n\n')
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .toList();

      // Если уже есть 3 или меньше абзацев, возвращаем как есть
      if (naturalParagraphs.length <= 3) {
        return naturalParagraphs;
      }

      // Если больше 3, объединяем в 3 логичных блока
      return _combineIntoThreeBlocks(naturalParagraphs);
    }

    // Разбиваем на предложения для создания 3 блоков
    final sentences = cleanedText.split(RegExp(r'(?<=\.)\s+(?=[A-Z])'));

    if (sentences.length <= 3) {
      // Если предложений мало, каждое в отдельный абзац
      return sentences.where((s) => s.trim().isNotEmpty).toList();
    }

    // Создаем 3 сбалансированных блока
    return _createThreeBalancedBlocks(sentences);
  }

  // Объединяем существующие абзацы в 3 логичных блока
  List<String> _combineIntoThreeBlocks(List<String> paragraphs) {
    if (paragraphs.length <= 3) return paragraphs;

    final totalLength = paragraphs.fold(0, (sum, p) => sum + p.length);
    final targetLength = totalLength ~/ 3;

    List<String> result = [];
    String currentBlock = '';

    for (int i = 0; i < paragraphs.length; i++) {
      if (currentBlock.isEmpty) {
        currentBlock = paragraphs[i];
      } else {
        currentBlock += '\n\n' + paragraphs[i];
      }

      // Добавляем блок если:
      // 1. Достигли целевой длины и это не последний абзац
      // 2. Уже есть 2 блока (остальное идет в третий)
      // 3. Это последний абзац
      if ((currentBlock.length >= targetLength && result.length < 2 && i < paragraphs.length - 1) ||
          result.length == 2 ||
          i == paragraphs.length - 1) {
        result.add(currentBlock.trim());
        currentBlock = '';

        if (result.length == 3) break;
      }
    }

    return result;
  }

  // Создаем 3 сбалансированных блока из предложений
  List<String> _createThreeBalancedBlocks(List<String> sentences) {
    final validSentences = sentences.where((s) => s.trim().isNotEmpty).toList();

    if (validSentences.length <= 3) {
      return validSentences;
    }

    // Делим предложения на 3 примерно равные части
    final third = validSentences.length ~/ 3;
    final remainder = validSentences.length % 3;

    List<String> blocks = [];
    int start = 0;

    for (int i = 0; i < 3; i++) {
      int blockSize = third + (i < remainder ? 1 : 0);
      int end = start + blockSize;

      if (end > validSentences.length) end = validSentences.length;

      final blockSentences = validSentences.sublist(start, end);
      blocks.add(blockSentences.join(' ').trim());

      start = end;
      if (start >= validSentences.length) break;
    }

    return blocks.where((block) => block.isNotEmpty).toList();
  }

  // Получаем цвет для блока
  Color _getBlockColor(int index) {
    switch (index) {
      case 0:
        return Colors.blue;
      case 1:
        return Colors.orange;
      case 2:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  // Получаем иконку для блока
  IconData _getBlockIcon(int index) {
    switch (index) {
      case 0:
        return Icons.article_outlined;
      case 1:
        return Icons.info_outline;
      case 2:
        return Icons.trending_up;
      default:
        return Icons.circle;
    }
  }

  // Получаем лейбл для блока
  String _getBlockLabel(int index) {
    switch (index) {
      case 0:
        return 'Context';
      case 1:
        return 'Key Facts';
      case 2:
        return 'Impact';
      default:
        return 'Section ${index + 1}';
    }
  }

  Widget _buildAppleStyleContent() {
    final newsDetail = widget.newsDetail;
    final sentimentColor = _getSentimentColor(newsDetail.sentiment.sentiment);

    // 🔍 ОТЛАДОЧНАЯ ИНФОРМАЦИЯ
    print('🔍 MODAL DEBUG:');
    print('   rewrittenContent: ${newsDetail.rewrittenContent?.length ?? 0} chars');
    print('   summary: ${newsDetail.summary?.length ?? 0} chars');
    print('   fullContent: ${newsDetail.fullContent.length} chars');
    print('   description: ${newsDetail.description.length} chars');

    // ИСПРАВЛЕННАЯ ЛОГИКА: Приоритет ПОЛНОГО контента: rewrittenContent (AI полный) > fullContent (оригинал) > content > description
    final agentSummary = (newsDetail.rewrittenContent != null && newsDetail.rewrittenContent!.isNotEmpty)
        ? newsDetail.rewrittenContent! // AI переписанный ПОЛНЫЙ контент - высший приоритет
        : (newsDetail.fullContent.isNotEmpty)
            ? newsDetail.fullContent // Оригинальный полный контент
            : newsDetail.description; // Описание как fallback

    // summary НЕ используется для основного контента - это только краткая выжимка!

    print('   🎯 Using: ${agentSummary.length} chars from ${
      (newsDetail.rewrittenContent != null && newsDetail.rewrittenContent!.isNotEmpty) ? 'rewrittenContent (AI FULL)' :
      (newsDetail.fullContent.isNotEmpty) ? 'fullContent (ORIGINAL)' : 'description (FALLBACK)'
    }');

    final paragraphs = _splitToParagraphs(agentSummary);
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
      child: RawScrollbar(
        controller: _contentController,
        thumbVisibility: true,
        thickness: 6,
        radius: const Radius.circular(6),
        thumbColor: sentimentColor.withOpacity(0.8),
        trackVisibility: true,
        trackColor: Colors.white.withOpacity(0.05),
        trackBorderColor: Colors.transparent,
        child: SingleChildScrollView(
          controller: _contentController,
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Заголовок статьи
              Text(
                newsDetail.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.w800,
                  height: 1.15,
                  letterSpacing: -0.8,
                  fontFamily: 'SF Pro Display',
                ),
              ),
              const SizedBox(height: 20),
              // Строка с временем публикации, временем чтения, кнопкой анализа и sentiment
              Row(
                children: [
                  buildFetchTime(newsDetail.cachedAt ?? newsDetail.publishedAt),
                  const SizedBox(width: 12),
                  _buildReadingTime(),
                  const SizedBox(width: 12),
                  buildAnalyzeButton(_analyzeNews),
                  const Spacer(),
                  _buildSentimentIndicator(),
                ],
              ),
              const SizedBox(height: 24),
              // 📖 СТРУКТУРИРОВАННОЕ ОТОБРАЖЕНИЕ В 3 БЛОКА
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (int i = 0; i < paragraphs.length && i < 3; i++)
                    Container(
                      margin: EdgeInsets.only(
                        bottom: i == paragraphs.length - 1 ? 0 : 32, // Больше пространства между блоками
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Небольшой индикатор блока
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            margin: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              color: _getBlockColor(i).withOpacity(0.15),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: _getBlockColor(i).withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getBlockIcon(i),
                                  color: _getBlockColor(i),
                                  size: 16,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  _getBlockLabel(i),
                                  style: TextStyle(
                                    color: _getBlockColor(i),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Содержимое блока
                          Text(
                            paragraphs[i],
                            style: const TextStyle(
                              color: Color(0xFFE5E5E7),
                              fontSize: 17,
                              fontWeight: FontWeight.w400,
                              height: 1.75,
                              letterSpacing: 0.2,
                              fontFamily: 'SF Pro Text',
                            ),
                            textAlign: TextAlign.justify,
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 24),
              // Sentiment анализ и теги — без изменений
              if (newsDetail.sentiment.reasoning.isNotEmpty) ...[
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        sentimentColor.withOpacity(0.08),
                        sentimentColor.withOpacity(0.04),
                        Colors.transparent,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(18),
                    border: Border.all(
                      color: sentimentColor.withOpacity(0.2),
                      width: 0.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: sentimentColor.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                        spreadRadius: -5,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.psychology_rounded,
                            color: sentimentColor,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Market Analysis',
                            style: TextStyle(
                              color: sentimentColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        newsDetail.sentiment.reasoning,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
              ],
              if (newsDetail.tags.isNotEmpty) ...[
                Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  children: newsDetail.tags.map((tag) => Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xFF1A1A1A),
                          Color(0xFF0F0F0F),
                          Color(0xFF000000),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.06),
                        width: 0.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          offset: const Offset(2, 2),
                          spreadRadius: -2,
                        ),
                        BoxShadow(
                          color: Colors.white.withOpacity(0.05),
                          blurRadius: 8,
                          offset: const Offset(-1, -1),
                          spreadRadius: -3,
                        ),
                      ],
                    ),
                    child: Text(
                      tag,
                      style: const TextStyle(
                        color: Color(0xFFAEAEB2),
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.1,
                      ),
                    ),
                  )).toList(),
                ),
                const SizedBox(height: 24),
              ],
              // Результаты финансового анализа
              if (_analysisResult != null) ...[
                _buildAnalysisResult(_analysisResult!),
                const SizedBox(height: 24),
              ],
              if (newsDetail.url.isNotEmpty) ...[
                Container(
                  width: double.infinity,
                  height: 56,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFF007AFF),
                        Color(0xFF5AC8FA),
                        Color(0xFF007AFF),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(28),
                    boxShadow: [
                      // Основная тень
                      BoxShadow(
                        color: const Color(0xFF007AFF).withOpacity(0.4),
                        blurRadius: 25,
                        offset: const Offset(0, 12),
                        spreadRadius: -5,
                      ),
                      // Внутренняя подсветка
                      BoxShadow(
                        color: Colors.white.withOpacity(0.2),
                        blurRadius: 15,
                        offset: const Offset(-3, -3),
                        spreadRadius: -8,
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(25),
                      onTap: () => launchUrlString(newsDetail.url),
                      child: const Center(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.open_in_new_rounded,
                              color: Colors.white,
                              size: 18,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Read Full Article',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Генерация выжимки агента (250-300 слов)
  String _generateAgentSummary(NewsDetailData newsDetail) {
    // ИСПРАВЛЕННАЯ ЛОГИКА: Если есть готовая выжимка от AI, используем её
    if (newsDetail.summary != null && newsDetail.summary!.isNotEmpty) {
      return newsDetail.summary!;
    }

    // Иначе создаем выжимку на основе ПОЛНОГО контента (приоритет: rewrittenContent > fullContent > description)
    final sentiment = newsDetail.sentiment.sentiment;
    final sentimentText = sentiment == SentimentType.positive
        ? "positive market sentiment"
        : sentiment == SentimentType.negative
            ? "negative market implications"
            : "neutral market outlook";

    // ПРАВИЛЬНЫЙ приоритет для создания выжимки
    final content = (newsDetail.rewrittenContent != null && newsDetail.rewrittenContent!.isNotEmpty)
        ? newsDetail.rewrittenContent! // AI переписанный ПОЛНЫЙ контент
        : (newsDetail.fullContent.isNotEmpty)
            ? newsDetail.fullContent // Оригинальный полный контент
            : newsDetail.description; // Fallback

    // Берем первые 250-300 слов и добавляем анализ
    final words = content.split(' ');
    final truncatedWords = words.take(200).toList();
    final baseText = truncatedWords.join(' ');

    final analysisText = """

Our AI analysis indicates ${sentimentText} based on the following factors:

• Market Impact: ${newsDetail.sentiment.marketImpact.isNotEmpty ? newsDetail.sentiment.marketImpact : 'Standard market reaction expected'}
• Confidence Level: ${(newsDetail.sentiment.confidence * 100).toStringAsFixed(0)}%
• Key Implications: ${newsDetail.sentiment.reasoning.isNotEmpty ? newsDetail.sentiment.reasoning.split('.').first : 'Market participants should monitor developments closely'}

This analysis considers current market conditions, historical patterns, and sentiment indicators to provide actionable insights for informed decision-making.""";

    final fullSummary = baseText + analysisText;

    // Ограничиваем до примерно 300 слов
    final summaryWords = fullSummary.split(' ');
    if (summaryWords.length > 300) {
      return summaryWords.take(300).join(' ') + '...';
    }

    return fullSummary;
  }

  Widget buildFetchTime(DateTime publishedAt) {
    final now = DateTime.now();
    final diff = now.difference(publishedAt);
    String timeText;
    if (diff.inMinutes < 1) {
      timeText = 'Just now';
    } else if (diff.inMinutes < 60) {
      timeText = '${diff.inMinutes}m ago';
    } else if (diff.inHours < 24) {
      timeText = '${diff.inHours}h ago';
    } else if (diff.inDays == 1) {
      timeText = 'Yesterday';
    } else if (diff.inDays < 7) {
      timeText = '${diff.inDays}d ago';
    } else {
      // For older news, show the date
      timeText = '${publishedAt.day}.${publishedAt.month.toString().padLeft(2, '0')}.${publishedAt.year}';
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.access_time_rounded,
            size: 14,
            color: Colors.white.withOpacity(0.7),
          ),
          const SizedBox(width: 6),
          Text(
            timeText,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
              fontWeight: FontWeight.w500,
              fontFamily: 'SF Pro Text',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisResult(FinancialAnalysisResult result) {
    final sentimentColor = _getSentimentColor(widget.newsDetail.sentiment.sentiment);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1E3A8A).withOpacity(0.3),
            Color(0xFF3B82F6).withOpacity(0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: Colors.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Financial Analysis',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Краткое резюме
          Text(
            result.summary,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 16),

          // Влияние на рынок
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Market Impact',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildImpactChip('Strength', result.marketImpact.strength),
                    const SizedBox(width: 8),
                    _buildImpactChip('Direction', result.marketImpact.direction),
                    const SizedBox(width: 8),
                    _buildImpactChip('Horizon', result.marketImpact.timeHorizon),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  result.marketImpact.rationale,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Затронутые активы
          if (result.marketImpact.affectedAssets.isNotEmpty) ...[
            Text(
              'Affected Assets',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: result.marketImpact.affectedAssets.take(6).map((asset) =>
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: asset.type == 'crypto'
                        ? Colors.orange.withOpacity(0.2)
                        : Colors.green.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: asset.type == 'crypto'
                          ? Colors.orange.withOpacity(0.4)
                          : Colors.green.withOpacity(0.4),
                      width: 0.5,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        asset.ticker,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${asset.impactMagnitude}/10',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),
              ).toList(),
            ),
            const SizedBox(height: 12),
          ],

          // Интересный факт
          if (result.interestingFact.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.purple.withOpacity(0.3),
                  width: 0.5,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: Colors.purple,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      result.interestingFact,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildImpactChip(String label, String value) {
    Color chipColor;
    switch (value.toLowerCase()) {
      case 'high':
      case 'positive':
      case 'высокое':
      case 'позитивное':
        chipColor = Colors.green;
        break;
      case 'negative':
      case 'негативное':
        chipColor = Colors.red;
        break;
      case 'moderate':
      case 'medium-term':
      case 'умеренное':
      case 'среднесрочное':
        chipColor = Colors.orange;
        break;
      default:
        chipColor = Colors.blue;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: chipColor.withOpacity(0.4),
          width: 0.5,
        ),
      ),
      child: Text(
        '$label: $value',
        style: TextStyle(
          color: chipColor,
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget buildAnalyzeButton(VoidCallback onTap) {
    return GestureDetector(
      onTap: _isAnalyzing ? null : onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: _isAnalyzing
                ? [
                    const Color(0xFF1A1A1A),
                    const Color(0xFF0F0F0F),
                    const Color(0xFF000000),
                  ]
                : [
                    const Color(0xFF5E5CE6),
                    const Color(0xFF007AFF),
                    const Color(0xFF5AC8FA),
                  ],
          ),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: Colors.white.withOpacity(_isAnalyzing ? 0.04 : 0.12),
            width: 0.5,
          ),
          boxShadow: [
            // Основная тень
            BoxShadow(
              color: _isAnalyzing
                  ? Colors.black.withOpacity(0.2)
                  : const Color(0xFF007AFF).withOpacity(0.3),
              blurRadius: _isAnalyzing ? 6 : 12,
              offset: Offset(0, _isAnalyzing ? 1 : 4),
              spreadRadius: _isAnalyzing ? -1 : -2,
            ),
            // Внутренняя подсветка
            BoxShadow(
              color: Colors.white.withOpacity(_isAnalyzing ? 0.02 : 0.15),
              blurRadius: 6,
              offset: const Offset(-1, -1),
              spreadRadius: -3,
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_isAnalyzing)
              SizedBox(
                width: 14,
                height: 14,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white.withOpacity(0.7)),
                ),
              )
            else
              Icon(Icons.auto_awesome_rounded, color: Colors.white, size: 14),
            const SizedBox(width: 6),
            Text(
              _isAnalyzing ? 'Analyzing...' : 'AI Analysis',
              style: TextStyle(
                color: Colors.white.withOpacity(_isAnalyzing ? 0.6 : 0.95),
                fontWeight: FontWeight.w600,
                fontSize: 12,
                fontFamily: 'SF Pro Display',
                letterSpacing: 0.1,
              ),
            ),
          ],
        ),
      ),
    );
  }
}