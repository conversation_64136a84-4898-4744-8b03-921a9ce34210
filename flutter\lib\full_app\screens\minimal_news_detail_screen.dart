import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/news_item.dart';
import '../widgets/app_bottom_navigation.dart';
import '../utils/device_type.dart';
import '../models/sentiment_types.dart';

class MinimalNewsDetailScreen extends StatelessWidget {
  const MinimalNewsDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final newsItem = ModalRoute.of(context)!.settings.arguments as NewsItem;
    final dateFormat = DateFormat('MMM d, yyyy • h:mm a');
    final isDesktop = DeviceUtils.isDesktop(context);

    return Scaffold(
      backgroundColor: Colors.black,
      body: isDesktop
          ? _buildDesktopLayout(context, newsItem, dateFormat)
          : _buildMobileLayout(context, newsItem, dateFormat),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 0,
        onTap: (index) {
          if (index != 0) {
            Navigator.pop(context); // First pop the detail screen
            switch (index) {
              case 1:
                Navigator.pushReplacementNamed(context, '/crypto_markets');
                break;
              case 2:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/saved_analyses');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          } else {
            Navigator.pop(context); // Just go back to news list
          }
        },
      ),
    );
  }

  // Desktop layout with two columns
  Widget _buildDesktopLayout(BuildContext context, NewsItem newsItem, DateFormat dateFormat) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main content (left side)
        Expanded(
          flex: 3,
          child: CustomScrollView(
            slivers: [
              // App bar with title
              SliverAppBar(
                expandedHeight: 120.0,
                floating: false,
                pinned: true,
                backgroundColor: Colors.black,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    'Article Details',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                    ),
                  ),
                  centerTitle: false,
                ),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () => Navigator.pop(context),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.bookmark_border),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Article saved')),
                      );
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Share functionality coming soon')),
                      );
                    },
                  ),
                  const SizedBox(width: 8),
                ],
              ),

              // News content
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        newsItem.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          height: 1.2,
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Source, date and sentiment in a row
                      Row(
                        children: [
                          // Source
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.blue.withAlpha(50),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              newsItem.source,
                              style: const TextStyle(
                                color: Colors.blue,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),

                          // Date
                          Text(
                            dateFormat.format(newsItem.publishedAt),
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 24),

                          // Sentiment indicator
                          _buildSentimentIndicator(newsItem.sentiment),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Tags
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: newsItem.tags.map((tag) {
                          return Chip(
                            label: Text(
                              '#$tag',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                            backgroundColor: Colors.grey[800],
                            padding: EdgeInsets.zero,
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 32),

                      // Main image
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(
                          newsItem.imageUrl,
                          height: 400,
                          width: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              height: 400,
                              width: double.infinity,
                              color: Colors.grey[900],
                              child: const Center(
                                child: Icon(Icons.image_not_supported, size: 80, color: Colors.white),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Description (short content)
                      Text(
                        newsItem.description,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          height: 1.5,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Full article content (mock)
                      Text(
                        _generateMockArticleContent(newsItem),
                        style: TextStyle(
                          color: Colors.grey[300],
                          fontSize: 16,
                          height: 1.8,
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Action buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () => _launchURL(newsItem.url),
                            icon: const Icon(Icons.language),
                            label: const Text('Read Original Article'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                            ),
                          ),
                          const SizedBox(width: 16),
                          OutlinedButton.icon(
                            onPressed: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Article saved to your reading list')),
                              );
                            },
                            icon: const Icon(Icons.bookmark_add),
                            label: const Text('Save for Later'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.white,
                              side: const BorderSide(color: Colors.white),
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 80), // Extra space for bottom navigation
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // Sidebar (right side)
        Expanded(
          flex: 1,
          child: Container(
            color: Colors.grey[900],
            height: MediaQuery.of(context).size.height,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Related News',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Related news items (mock)
                  _buildRelatedNewsItem(
                    'Bitcoin Mining Difficulty Reaches New All-Time High',
                    'https://via.placeholder.com/100?text=BTC+Mining',
                    'CryptoNews',
                    DateTime.now().subtract(const Duration(hours: 4)),
                  ),
                  const Divider(color: Colors.grey),
                  _buildRelatedNewsItem(
                    'Top 5 Cryptocurrencies to Watch This Week',
                    'https://via.placeholder.com/100?text=Top+5',
                    'CoinDesk',
                    DateTime.now().subtract(const Duration(hours: 6)),
                  ),
                  const Divider(color: Colors.grey),
                  _buildRelatedNewsItem(
                    'How Blockchain Technology is Transforming Supply Chains',
                    'https://via.placeholder.com/100?text=Blockchain',
                    'TechCrypto',
                    DateTime.now().subtract(const Duration(hours: 12)),
                  ),
                  const Divider(color: Colors.grey),
                  _buildRelatedNewsItem(
                    'NFT Market Shows Signs of Recovery After Months of Decline',
                    'https://via.placeholder.com/100?text=NFT+Market',
                    'NFT Insider',
                    DateTime.now().subtract(const Duration(hours: 18)),
                  ),
                  const Divider(color: Colors.grey),
                  _buildRelatedNewsItem(
                    'Central Banks Exploring Digital Currencies: What You Need to Know',
                    'https://via.placeholder.com/100?text=CBDC',
                    'Financial Times',
                    DateTime.now().subtract(const Duration(hours: 24)),
                  ),
                  const Divider(color: Colors.grey),
                  _buildRelatedNewsItem(
                    'DeFi Protocols See Surge in Total Value Locked',
                    'https://via.placeholder.com/100?text=DeFi',
                    'DeFi Pulse',
                    DateTime.now().subtract(const Duration(hours: 36)),
                  ),

                  const SizedBox(height: 32),

                  // Popular tags section
                  const Text(
                    'Popular Tags',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      'Bitcoin', 'Ethereum', 'DeFi', 'NFT', 'Regulation',
                      'Altcoins', 'Mining', 'Trading', 'Adoption', 'Technology'
                    ].map((tag) {
                      return Chip(
                        label: Text(
                          '#$tag',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                        backgroundColor: Colors.grey[800],
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Mobile layout with stacked content
  Widget _buildMobileLayout(BuildContext context, NewsItem newsItem, DateFormat dateFormat) {
    return CustomScrollView(
      slivers: [
        // App bar with image
        SliverAppBar(
          expandedHeight: 240.0,
          floating: false,
          pinned: true,
          backgroundColor: Colors.black,
          flexibleSpace: FlexibleSpaceBar(
            background: Stack(
              fit: StackFit.expand,
              children: [
                // News image
                Image.network(
                  newsItem.imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[900],
                      child: const Center(
                        child: Icon(Icons.image_not_supported, size: 50, color: Colors.white),
                      ),
                    );
                  },
                ),
                // Gradient overlay for better text visibility
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withAlpha(179), // ~0.7 opacity
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          leading: IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(128), // ~0.5 opacity
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.arrow_back),
            ),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(128), // ~0.5 opacity
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.bookmark_border),
              ),
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Article saved')),
                );
              },
            ),
            IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(128), // ~0.5 opacity
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.share),
              ),
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Share functionality coming soon')),
                );
              },
            ),
            const SizedBox(width: 8),
          ],
        ),

        // News content
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  newsItem.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                // Source and date
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(51), // ~0.2 opacity
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        newsItem.source,
                        style: const TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      dateFormat.format(newsItem.publishedAt),
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Tags
                Wrap(
                  spacing: 8,
                  children: newsItem.tags.map((tag) {
                    return Chip(
                      label: Text(
                        '#$tag',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                      backgroundColor: Colors.grey[800],
                      padding: EdgeInsets.zero,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),

                // Sentiment indicator
                _buildSentimentIndicator(newsItem.sentiment),
                const SizedBox(height: 24),

                // Description (short content)
                Text(
                  newsItem.description,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 16),

                // Full article content (mock)
                Text(
                  _generateMockArticleContent(newsItem),
                  style: TextStyle(
                    color: Colors.grey[300],
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 32),

                // Read original article button
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () => _launchURL(newsItem.url),
                    icon: const Icon(Icons.language),
                    label: const Text('Read Original Article'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(height: 32),

                // Related news section
                const Text(
                  'Related News',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                // Related news items (mock)
                _buildRelatedNewsItem(
                  'Bitcoin Mining Difficulty Reaches New All-Time High',
                  'https://via.placeholder.com/100?text=BTC+Mining',
                  'CryptoNews',
                  DateTime.now().subtract(const Duration(hours: 4)),
                ),
                const Divider(color: Colors.grey),
                _buildRelatedNewsItem(
                  'Top 5 Cryptocurrencies to Watch This Week',
                  'https://via.placeholder.com/100?text=Top+5',
                  'CoinDesk',
                  DateTime.now().subtract(const Duration(hours: 6)),
                ),
                const Divider(color: Colors.grey),
                _buildRelatedNewsItem(
                  'How Blockchain Technology is Transforming Supply Chains',
                  'https://via.placeholder.com/100?text=Blockchain',
                  'TechCrypto',
                  DateTime.now().subtract(const Duration(hours: 12)),
                ),
                const SizedBox(height: 80), // Extra space for bottom navigation
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSentimentIndicator(SentimentType sentiment) {
    IconData icon;
    Color color;
    String text;

    switch (sentiment) {
      case SentimentType.positive:
        icon = Icons.trending_up;
        color = Colors.green;
        text = 'Bullish';
        break;
      case SentimentType.negative:
        icon = Icons.trending_down;
        color = Colors.red;
        text = 'Bearish';
        break;
      case SentimentType.neutral:
        icon = Icons.trending_flat;
        color = Colors.amber;
        text = 'Neutral';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withAlpha(25), // ~0.1 opacity
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(76)), // ~0.3 opacity
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Text(
            'Market Sentiment: $text',
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRelatedNewsItem(String title, String imageUrl, String source, DateTime date) {
    final dateFormat = DateFormat('MMM d');

    return InkWell(
      onTap: () {
        // Navigate to related news (mock)
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            // Thumbnail
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                imageUrl,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey[800],
                    child: const Icon(Icons.image_not_supported, color: Colors.white),
                  );
                },
              ),
            ),
            const SizedBox(width: 16),

            // Title and metadata
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$source • ${dateFormat.format(date)}',
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _generateMockArticleContent(NewsItem newsItem) {
    // This is a mock function to generate longer article content
    // In a real app, this would come from the API
    return '''
${newsItem.description}

In recent developments, market analysts have noted significant changes in investor sentiment regarding ${newsItem.tags.join(', ')}. The implications of these movements could have far-reaching effects on the broader market.

Several key factors are contributing to this trend:

1. Increased institutional adoption across major financial centers
2. Regulatory developments in key markets
3. Technological advancements improving scalability and security
4. Growing retail investor interest driven by mainstream media coverage

Experts suggest that these developments could signal a new phase in the market cycle, though caution is advised as volatility remains high.

"We're seeing unprecedented interest from traditional financial institutions," says Jane Smith, Chief Analyst at Capital Research. "This represents a significant shift in how these assets are perceived by the mainstream financial community."

As markets continue to evolve, staying informed about these developments will be crucial for investors looking to navigate this complex landscape.
''';
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }
}

