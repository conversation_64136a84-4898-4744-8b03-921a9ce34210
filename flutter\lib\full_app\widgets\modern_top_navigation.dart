import 'package:flutter/material.dart';

/// Современная верхняя навигационная панель с анимациями и улучшенным дизайном
class ModernTopNavigation extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;

  const ModernTopNavigation({
    Key? key,
    required this.selectedIndex,
    required this.onItemSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.grey[100],
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.0,
            offset: const Offset(0, 2.0),
          ),
        ],
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Проверяем ширину экрана и адаптируем навигацию
          final bool isNarrow = constraints.maxWidth < 400;

          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNavItem(context, 0, 'All', Icons.apps),
              _buildNavItem(context, 1, 'Crypto', Icons.currency_bitcoin),
              _buildNavItem(context, 2, 'Stocks', Icons.trending_up),
              if (!isNarrow) _buildNavItem(context, 3, 'Whales', Icons.water),
            ],
          );
        },
      ),
    );
  }

  Widget _buildNavItem(BuildContext context, int index, String title, IconData icon) {
    final bool isSelected = selectedIndex == index;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Expanded(
      child: InkWell(
        onTap: () => onItemSelected(index),
        borderRadius: BorderRadius.circular(12.0),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          margin: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          decoration: BoxDecoration(
            color: isSelected 
                ? (isDarkMode 
                    ? Colors.blue.withOpacity(0.2) 
                    : Colors.blue.withOpacity(0.1))
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8.0),
            border: isSelected
                ? Border.all(
                    color: Colors.blue,
                    width: 1.0,
                  )
                : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected 
                    ? Colors.blue 
                    : isDarkMode ? Colors.grey[400] : Colors.grey[700],
                size: 18,
              ),
              const SizedBox(width: 6),
              Text(
                title,
                style: TextStyle(
                  color: isSelected 
                      ? Colors.blue 
                      : isDarkMode ? Colors.grey[400] : Colors.grey[700],
                  fontSize: 13,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
