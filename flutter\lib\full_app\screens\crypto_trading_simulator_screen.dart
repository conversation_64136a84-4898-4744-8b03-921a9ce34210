import 'dart:convert';
import 'dart:math';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:candlesticks/candlesticks.dart';
import '../models/trading_simulator_models.dart' as models;
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import '../providers/trading_simulator_provider.dart';
import '../widgets/simulator_candlestick_chart.dart';
import '../widgets/trading_view_chart.dart';
import 'dart:math' as math;
import '../widgets/simple_candlestick_chart.dart';

// Класс для отрисовки точки входа на графике
class EntryPointMarker extends CustomPainter {
  final double entryPrice;
  final double minPrice;
  final double maxPrice;
  final bool isUpPrediction;

  EntryPointMarker({
    required this.entryPrice,
    required this.minPrice,
    required this.maxPrice,
    required this.isUpPrediction,
  });

  // Метод для рисования пунктирной линии
  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    final dashWidth = 4.0;
    final dashSpace = 4.0;
    final startX = start.dx;
    final startY = start.dy;
    final endX = end.dx;
    final endY = end.dy;

    // Определяем направление линии
    final isHorizontal = startY == endY;

    if (isHorizontal) {
      // Рисуем горизонтальную пунктирную линию
      double distance = endX - startX;
      double drawn = 0;
      while (drawn < distance) {
        final dashLength = min(dashWidth, distance - drawn);
        canvas.drawLine(
          Offset(startX + drawn, startY),
          Offset(startX + drawn + dashLength, startY),
          paint,
        );
        drawn += dashLength + dashSpace;
      }
    } else {
      // Рисуем вертикальную пунктирную линию
      double distance = endY - startY;
      double drawn = 0;
      while (drawn < distance) {
        final dashLength = min(dashWidth, distance - drawn);
        canvas.drawLine(
          Offset(startX, startY + drawn),
          Offset(startX, startY + drawn + dashLength),
          paint,
        );
        drawn += dashLength + dashSpace;
      }
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    // Определяем цвет линии в зависимости от предсказания (UP/DOWN)
    final lineColor = isUpPrediction ? Colors.green : Colors.red;

    // Создаем кисть для линий точки входа
    final linePaint = Paint()
      ..color = lineColor
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // Вычисляем Y-координату для горизонтальной линии (уровень цены)
    final priceRange = maxPrice - minPrice;
    final y = size.height - ((entryPrice - minPrice) / priceRange) * size.height;

    // Рисуем горизонтальную пунктирную линию на уровне цены входа
    _drawDashedLine(
      canvas,
      Offset(0, y),
      Offset(size.width, y),
      linePaint,
    );

    // Определяем X-координату для вертикальной линии (80% ширины графика)
    // Это позиционирует вертикальную линию на последней свече перед нажатием кнопки
    final x = size.width * 0.8;

    // Рисуем вертикальную пунктирную линию
    _drawDashedLine(
      canvas,
      Offset(x, 0),
      Offset(x, size.height),
      linePaint,
    );

    // Создаем фон для текста
    final bgRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(8, y - 22, 100, 18),
      Radius.circular(4),
    );

    final bgPaint = Paint()
      ..color = Colors.black.withAlpha(178) // 0.7 * 255 = 178
      ..style = PaintingStyle.fill;

    canvas.drawRRect(bgRect, bgPaint);

    // Добавляем текст с ценой входа
    final textStyle = TextStyle(
      color: lineColor,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );

    final textSpan = TextSpan(
      text: 'Entry: ${entryPrice.toStringAsFixed(2)}',
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // Рисуем текст в левой части графика
    textPainter.paint(canvas, Offset(12, y - 20));

    // Рисуем точку пересечения горизонтальной и вертикальной линий
    final dotPaint = Paint()
      ..color = lineColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(Offset(x, y), 4.0, dotPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is EntryPointMarker) {
      return oldDelegate.entryPrice != entryPrice ||
             oldDelegate.minPrice != minPrice ||
             oldDelegate.maxPrice != maxPrice ||
             oldDelegate.isUpPrediction != isUpPrediction;
    }
    return true;
  }
}

class CryptoTradingSimulatorScreen extends StatefulWidget {
  final models.SimulatorMode mode;
  final double leverage;
  final double initialBalance;
  final String symbol;
  final String timeframe;
  final String difficulty;

  const CryptoTradingSimulatorScreen({
    Key? key,
    required this.mode,
    this.leverage = 10.0,
    this.initialBalance = 1000.0,
    this.symbol = 'BTCUSDT',
    this.timeframe = '1h',
    this.difficulty = 'Medium',
  }) : super(key: key);

  @override
  State<CryptoTradingSimulatorScreen> createState() => _CryptoTradingSimulatorScreenState();
}

class _CryptoTradingSimulatorScreenState extends State<CryptoTradingSimulatorScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  double _zoomFactor = 1.0;
  bool _isLoading = false;
  double? _previousProfit; // Для хранения результата предыдущего раунда

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    // Инициализируем провайдер с параметрами из виджета
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<TradingSimulatorProvider>(context, listen: false);
      provider.setInitialParameters(
        mode: widget.mode,
        leverage: widget.leverage,
        balance: widget.initialBalance,
        symbol: widget.symbol,
        timeframe: widget.timeframe,
        difficulty: widget.difficulty,
      );
      
      // Вывод информации для отладки
      debugPrint('Simulator initialized with: Mode=${widget.mode}, Leverage=${widget.leverage}, Symbol=${widget.symbol}, Timeframe=${widget.timeframe}, Difficulty=${widget.difficulty}');
      
      // Добавляем задержку перед проверкой баланса, чтобы данные успели загрузиться
      Future.delayed(const Duration(milliseconds: 500), () {
        // Проверяем баланс после инициализации
        if (provider.balance <= 0) {
          _showGameOverDialog();
        }
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTradeAction(models.TradeAction action) async {
    _animationController.forward().then((_) => _animationController.reverse());
    
    final provider = context.read<TradingSimulatorProvider>();
    final success = provider.makeTrade(action);
    
    // Применяем зум при совершении сделки
    setState(() {
      _zoomFactor = 1.5; // Зум 150%
    });
    
    // Проверяем баланс после сделки
    if (provider.balance <= 0) {
      // Если баланс равен или меньше нуля, показываем диалог "Вы проиграли"
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showGameOverDialog();
      });
    }
  }

  Future<void> _startNewRound() async {
    final provider = context.read<TradingSimulatorProvider>();
    
    // Сохраняем результат текущего раунда перед началом нового
    _previousProfit = provider.lastTradeProfit;
    
    setState(() {
      _isLoading = true;
      _zoomFactor = 1.0;
    });
    
    await provider.startNewRound();
    
    setState(() {
      _isLoading = false;
    });
    
    // Проверяем баланс после загрузки нового раунда
    if (provider.balance <= 0) {
      // Если баланс равен или меньше нуля, показываем диалог "Вы проиграли"
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showGameOverDialog();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = true; // Всегда темный режим
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(CupertinoIcons.back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Crypto Trading Simulator',
          style: TextStyle(
            color: Colors.white,
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Consumer<TradingSimulatorProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Column(
              children: [
                // Stats Panel (обновлен под стиль Profile)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 0),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1C1C1E),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildProfileStatItem('Balance', '\$${provider.balance.toStringAsFixed(2)}', CupertinoIcons.money_dollar_circle),
                      // Заменяем Rounds на Last Result (отображаем прибыль/убыток предыдущего раунда)
                      _previousProfit != null
                          ? _buildProfileStatItem(
                              'Last Result',
                              '${_previousProfit! >= 0 ? "+\$" : "-\$"}${_previousProfit!.abs().toStringAsFixed(2)}',
                              _previousProfit! >= 0 ? CupertinoIcons.arrow_up_circle_fill : CupertinoIcons.arrow_down_circle_fill,
                              valueColor: _previousProfit! >= 0 ? Colors.green : Colors.red,
                            )
                          : _buildProfileStatItem('Last Result', 'N/A', CupertinoIcons.clock_fill),
                      _buildProfileStatItem('Win Rate', '${provider.winRate.toStringAsFixed(1)}%', CupertinoIcons.chart_bar_alt_fill),
                      _buildProfileStatItem('Deaths', provider.deaths.toString(), CupertinoIcons.xmark_octagon_fill),
                    ],
                  ),
                ),
                
                // Chart (с более компактными отступами)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  height: 350, // Уменьшенная высота для предотвращения переполнения
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: const Color(0xFF1C1C1E), width: 1),
                  ),
                  child: _isLoading
                      ? const Center(child: CircularProgressIndicator(color: Colors.white))
                      : provider.isLoadingError
                          ? _buildErrorDisplay()
                          : ClipRRect(
                              borderRadius: BorderRadius.circular(16),
                              child: SimpleCandlestickChart(
                                candles: provider.currentCandles,
                                height: 350,
                                entryPrice: provider.isTradeActive ? provider.entryPrice : null,
                                entryIndex: provider.isTradeActive ? provider.currentCandles.length - 7 : null,
                                upColor: Colors.green,
                                downColor: Colors.red,
                                emptyBars: 35,
                                backgroundColor: Colors.black,
                                gridColor: Colors.grey[800]!,
                                textColor: Colors.white,
                                zoomFactor: _zoomFactor,
                              ),
                            ),
                ),
                
                // Profit/Loss Display (в стиле Profile)
                if (provider.isTradeActive && provider.lastTradeProfit != null)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1C1C1E),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: provider.lastTradeProfit! >= 0 ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          provider.lastTradeProfit! >= 0 ? CupertinoIcons.arrow_up_circle_fill : CupertinoIcons.arrow_down_circle_fill,
                          color: provider.lastTradeProfit! >= 0 ? Colors.green : Colors.red,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          '${provider.lastTradeProfit! >= 0 ? "Profit" : "Loss"}: \$${provider.lastTradeProfit!.abs().toStringAsFixed(2)}',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                
                // Trade Buttons or Next Round Button
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  child: !provider.isTradeActive
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildProfileButton(
                            models.TradeAction.buy,
                            Colors.green,
                            CupertinoIcons.arrow_up,
                            label: 'UP',
                          ),
                          _buildProfileButton(
                            models.TradeAction.sell,
                            Colors.red,
                            CupertinoIcons.arrow_down,
                            label: 'DOWN',
                          ),
                        ],
                      )
                    : _buildFuturisticNextRoundButton(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Футуристическая кнопка Next Round
  Widget _buildFuturisticNextRoundButton() {
    return GestureDetector(
      onTap: _isLoading ? null : _startNewRound,
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: _isLoading 
                ? [Colors.grey[800]!, Colors.grey[700]!] 
                : [const Color(0xFF0A84FF), const Color(0xFF6A5AE0)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: _isLoading
              ? []
              : [
                  BoxShadow(
                    color: const Color(0xFF0A84FF).withOpacity(0.5),
                    blurRadius: 12,
                    spreadRadius: -2,
                    offset: const Offset(0, 4),
                  ),
                ],
        ),
        child: Stack(
          children: [
            // Блик
            if (!_isLoading) 
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 20,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0.05),
                        Colors.white.withOpacity(0.2),
                        Colors.white.withOpacity(0.05),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                ),
              ),
            
            // Содержимое кнопки
            Center(
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.refresh_circled_solid,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'Next Round',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 17,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  // Стилизованные виджеты в стиле Profile page
  Widget _buildProfileStatItem(String label, String value, IconData icon, {Color? valueColor}) {
    return Column(
      children: [
        Icon(
          icon,
          color: valueColor ?? Colors.white.withOpacity(0.8),
          size: 22,
        ),
        const SizedBox(height: 6),
        Text(
          value,
          style: TextStyle(
            color: valueColor ?? Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  // Новые кнопки в стиле Profile страницы
  Widget _buildProfileButton(models.TradeAction action, Color color, IconData icon, {required String label}) {
    return GestureDetector(
      onTap: () => _handleTradeAction(action),
      child: Container(
        width: 160,
        height: 56,
        decoration: BoxDecoration(
          color: const Color(0xFF1C1C1E),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(width: 10),
            Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Диалоговое окно "Вы проиграли"
  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false, // Запрещаем закрывать диалог по клику вне него
      builder: (context) {
        return WillPopScope(
          onWillPop: () async => false, // Запрещаем закрывать диалог по кнопке Back
          child: Dialog(
            backgroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(
                color: Colors.red.withOpacity(0.5),
                width: 2,
              ),
            ),
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Иконка поражения
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Center(
                      child: Icon(
                        CupertinoIcons.xmark_circle,
                        color: Colors.red,
                        size: 50,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Заголовок
                  const Text(
                    'Вы проиграли',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Описание
                  Text(
                    'Ваш баланс достиг 0\$. Выберите действие:',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  
                  // Кнопка "Играть заново"
                  _buildGameOverButton(
                    'Играть заново',
                    CupertinoIcons.refresh,
                    Colors.blue,
                    () {
                      Navigator.of(context).pop(); // Закрываем диалог
                      _restartGame();
                    },
                  ),
                  const SizedBox(height: 12),
                  
                  // Кнопка "Top Up"
                  _buildGameOverButton(
                    'Top Up',
                    CupertinoIcons.money_dollar_circle,
                    Colors.green,
                    () {
                      Navigator.of(context).pop(); // Закрываем диалог
                      _topUpBalance();
                    },
                  ),
                  const SizedBox(height: 12),
                  
                  // Кнопка "Exit"
                  _buildGameOverButton(
                    'Exit',
                    CupertinoIcons.xmark_circle,
                    Colors.red,
                    () {
                      Navigator.of(context).pop(); // Закрываем диалог
                      _exitToModeSelection();
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
  
  // Стилизованная кнопка для диалога "Вы проиграли"
  Widget _buildGameOverButton(String text, IconData icon, Color color, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.15),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.5),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              text,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Перезапуск игры
  void _restartGame() {
    final provider = context.read<TradingSimulatorProvider>();
    
    // Сбрасываем состояние игры и устанавливаем новый начальный баланс
    provider.resetGame(initialBalance: widget.initialBalance);
    
    setState(() {
      _isLoading = false;
      _zoomFactor = 1.0;
      _previousProfit = null;
    });
  }
  
  // Пополнение баланса
  void _topUpBalance() {
    final provider = context.read<TradingSimulatorProvider>();
    
    // Пополняем баланс и увеличиваем счетчик смертей
    provider.topUpBalance(widget.initialBalance);
    
    setState(() {
      _isLoading = false;
      _zoomFactor = 1.0;
      _previousProfit = null;
    });
  }
  
  // Выход на страницу выбора режима
  void _exitToModeSelection() {
    // Переходим на экран выбора режима
    debugPrint('Exiting to mode selection screen');
    
    // Переходим на экран выбора режима, очищая стек до этого экрана
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/crypto_simulator_mode_selection',
      (route) => false,
    );
  }

  Widget _buildErrorDisplay() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          CupertinoIcons.exclamationmark_circle,
          color: Colors.red,
          size: 50,
        ),
        const SizedBox(height: 16),
        Text(
          'Ошибка загрузки данных',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            'Не удалось загрузить исторические данные для графика. Пожалуйста, проверьте подключение к интернету и попробуйте снова.',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 24),
        GestureDetector(
          onTap: () {
            setState(() {
              _isLoading = true;
            });
            final provider = context.read<TradingSimulatorProvider>();
            provider.retryLoading().then((_) {
              setState(() {
                _isLoading = false;
              });
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [const Color(0xFF0A84FF), const Color(0xFF6A5AE0)],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF0A84FF).withOpacity(0.5),
                  blurRadius: 12,
                  spreadRadius: -2,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  CupertinoIcons.refresh,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'Повторить',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
