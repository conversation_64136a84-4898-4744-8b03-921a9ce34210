import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/news_item.dart';
import '../models/sentiment_data.dart';
import '../config/env_config.dart';
import 'package:crypto/crypto.dart' as crypto;
import 'package:finance_ai/models/sentiment_types.dart';

class NewsService {
  // Base URL for the News API
  static const String _baseUrl = 'https://newsapi.org/v2';
  // Base URL for the CryptoCompare API
  static const String _cryptoCompareBaseUrl = 'https://min-api.cryptocompare.com/data';
  // Base URL for our backend
  static const String _backendBaseUrl = kDebugMode
    ? 'http://localhost:4000'
    : 'https://your-domain.com/api'; // Замените на ваш продакшн URL

  String get _newsApiKey => EnvConfig.newsApiKey;
  String get _cryptoCompareApiKey => EnvConfig.cryptoCompareApiKey;

  /// Получить новости с нашего бэкенда (с AI анализом настроения)
  Future<List<NewsItem>> getNewsFromBackend({
    int page = 1,
    int pageSize = 100, // Увеличиваем до 100 новостей по умолчанию
    String? sentiment,
    String? tags,
    String? search,
    String? category, // Добавляем фильтр по категории
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'pageSize': pageSize.toString(),
      };

      if (sentiment != null && sentiment.isNotEmpty) {
        queryParams['sentiment'] = sentiment;
      }
      if (tags != null && tags.isNotEmpty) {
        queryParams['tags'] = tags;
      }
      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (category != null && category.isNotEmpty) {
        queryParams['category'] = category;
      }

      final Uri uri = Uri.parse('$_backendBaseUrl/news').replace(queryParameters: queryParams);

      debugPrint('🌐 [NewsService] Отправляем запрос: $uri');
      debugPrint('📋 [NewsService] Параметры: $queryParams');

      final response = await http.get(uri);

      debugPrint('📡 [NewsService] Ответ сервера: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> articles = data['news'] ?? [];

        debugPrint('📦 [NewsService] Получено статей: ${articles.length}');
        debugPrint('📊 [NewsService] Общее количество на сервере: ${data['total'] ?? 'неизвестно'}');
        debugPrint('🔍 [NewsService] Полный ответ сервера: ${response.body.substring(0, 500)}...');

        if (articles.isEmpty) {
          debugPrint('⚠️ [NewsService] ВНИМАНИЕ: Массив articles пуст!');
          debugPrint('🔍 [NewsService] Структура ответа: ${data.keys.toList()}');
        }

        return articles.where((article) {
          // Фильтруем новости без заголовков
          final title = article['title'] ?? '';
          final aiTitle = article['aiGeneratedTitle'] ?? '';

          if (title.isEmpty && aiTitle.isEmpty) {
            debugPrint('⚠️ [NewsService] ВНИМАНИЕ: Новость без заголовка! Пропускаем...');
            return false;
          }
          return true;
        }).map<NewsItem>((article) {
          // 🔍 ОТЛАДОЧНАЯ ИНФОРМАЦИЯ
          final title = article['title'] ?? '';
          final aiTitle = article['aiGeneratedTitle'] ?? '';

          debugPrint('🔍 [NewsService] Обрабатываем новость:');
          debugPrint('   title: "${title.substring(0, title.length > 50 ? 50 : title.length)}..."');
          debugPrint('   aiGeneratedTitle: "${aiTitle.substring(0, aiTitle.length > 50 ? 50 : aiTitle.length)}..."');
          debugPrint('   rewrittenContent: ${article['rewrittenContent']?.length ?? 0} chars');
          debugPrint('   summary: ${article['summary']?.length ?? 0} chars');
          debugPrint('   content: ${article['content']?.length ?? 0} chars');
          debugPrint('   sentiment: ${article['sentiment']}');

          return NewsItem(
            id: article['id'] ?? '',
            title: title.isNotEmpty ? title : aiTitle,
            aiGeneratedTitle: aiTitle,
            description: article['description'] ?? '',
            imageUrl: article['imageUrl'] ?? 'https://via.placeholder.com/300x200?text=No+Image',
            publishedAt: DateTime.parse(article['publishedAt'] ?? DateTime.now().toIso8601String()),
            source: article['source'] ?? '',
            url: article['url'] ?? '',
            sentiment: _parseSentimentFromBackend(article['sentiment']),
            tags: List<String>.from(article['tags'] ?? []),
            category: _determineCategoryFromTags(List<String>.from(article['tags'] ?? [])),
            content: article['content'] ?? article['summary'] ?? '',
            summary: article['summary'],
            sentimentData: _parseSentimentDataFromBackend(article),
            rewrittenContent: article['rewrittenContent'],
            fetchedAt: DateTime.now(),
            // Новые поля классификации
            newsCategory: article['category'],
            categoryConfidence: article['categoryConfidence']?.toDouble(),
            categoryScores: article['categoryScores'] != null
                ? Map<String, double>.from(article['categoryScores'].map((k, v) => MapEntry(k, v.toDouble())))
                : null,
            classificationTags: article['classificationTags'] != null
                ? List<String>.from(article['classificationTags'])
                : null,
          );
        }).toList();
      } else {
        debugPrint('❌ [NewsService] Ошибка сервера: ${response.statusCode}');
        debugPrint('📄 [NewsService] Тело ответа: ${response.body}');
        throw Exception('Backend returned status: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ [NewsService] Исключение при загрузке новостей: $e');
      throw Exception('Failed to fetch news from backend: $e');
    }
  }

  /// Парсинг настроения из ответа бэкенда
  SentimentType _parseSentimentFromBackend(dynamic sentiment) {
    if (sentiment == null) return SentimentType.neutral;

    // Если sentiment - это объект с полем sentiment
    String sentimentStr;
    if (sentiment is Map && sentiment['sentiment'] != null) {
      sentimentStr = sentiment['sentiment'].toString().toLowerCase();
    } else {
      sentimentStr = sentiment.toString().toLowerCase();
    }

    debugPrint('🔍 [NewsService] Парсинг sentiment: $sentiment -> $sentimentStr');

    switch (sentimentStr) {
      case 'positive':
        return SentimentType.positive;
      case 'negative':
        return SentimentType.negative;
      default:
        return SentimentType.neutral;
    }
  }

  /// Парсинг данных настроения из ответа бэкенда
  SentimentData? _parseSentimentDataFromBackend(Map<String, dynamic> article) {
    try {
      if (article['sentimentData'] != null) {
        return SentimentData.fromJson(article['sentimentData']);
      }
    } catch (e) {
      debugPrint('Error parsing sentiment data: $e');
    }
    return null;
  }

  /// Определение категории на основе тегов
  NewsCategory _determineCategoryFromTags(List<String> tags) {
    final tagsLower = tags.map((tag) => tag.toLowerCase()).toList();

    if (tagsLower.any((tag) => ['bitcoin', 'ethereum', 'crypto', 'blockchain', 'btc', 'eth'].contains(tag))) {
      return NewsCategory.crypto;
    } else if (tagsLower.any((tag) => ['stocks', 'market', 'nasdaq', 'dow', 'finance'].contains(tag))) {
      return NewsCategory.stocks;
    } else if (tagsLower.any((tag) => ['ai', 'artificial intelligence', 'machine learning'].contains(tag))) {
      return NewsCategory.ai;
    } else if (tagsLower.any((tag) => ['politics', 'government', 'regulation'].contains(tag))) {
      return NewsCategory.politics;
    } else if (tagsLower.any((tag) => ['whale', 'institutional'].contains(tag))) {
      return NewsCategory.whales;
    }

    return NewsCategory.all;
  }

  // Get top headlines
  Future<List<NewsItem>> getTopHeadlines({
    String category = '',
    String query = '',
    int pageSize = 20,
    int page = 1,
  }) async {
    try {
      // Make a real API call to NewsAPI
      final Map<String, String> queryParams = {
        'apiKey': _newsApiKey,
        'pageSize': pageSize.toString(),
        'page': page.toString(),
        'country': 'us', // Используем новости из США для большего количества результатов
        'category': 'business', // Категория бизнес для финансовых новостей
      };

      // Добавляем категорию и запрос, если они указаны
      if (category.isNotEmpty) {
        queryParams['category'] = category;
      }

      if (query.isNotEmpty) {
        queryParams['q'] = query;
      }

      final Uri uri = Uri.parse('$_baseUrl/top-headlines').replace(queryParameters: queryParams);

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        final List<dynamic> articles = data['articles'] ?? [];

        if (articles.isEmpty) {
          return [];
        }

        return articles.map<NewsItem>((article) {
          // Map the API response to our NewsItem model
          final newsItem = NewsItem(
            id: article['url'] ?? '',
            title: article['title'] ?? '',
            aiGeneratedTitle: article['aiGeneratedTitle'],
            description: article['description'] ?? '',
            imageUrl: article['urlToImage'] ?? 'https://via.placeholder.com/300x200?text=No+Image',
            publishedAt: DateTime.parse(article['publishedAt'] ?? DateTime.now().toIso8601String()),
            source: article['source']['name'] ?? '',
            url: article['url'] ?? '',
            sentiment: _determineSentiment(article['title'] ?? ''),
            tags: _extractTags(article['title'] ?? '', article['description'] ?? ''),
            category: _determineCategory(article['title'] ?? '', article['description'] ?? ''),
            content: article['content'] ?? article['description'] ?? '',
            summary: article['summary'],
            rewrittenContent: article['rewrittenContent'],
            fetchedAt: DateTime.now(),
          );
          return newsItem;
        }).toList();
      } else {
        throw Exception('Failed to load news: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching news: $e');
    }
  }

  // Get cryptocurrency news from CryptoCompare
  Future<List<NewsItem>> getCryptoNews({
    int limit = 20,
    List<String> excludeTitles = const [],
  }) async {
    try {
      // Make API call to CryptoCompare
      final Map<String, String> queryParams = {
        'api_key': _cryptoCompareApiKey,
        'feeds': 'cryptocompare,cointelegraph,coindesk',
        'extraParams': 'NewsApp',
        'lang': 'EN',
        'sortOrder': 'latest',
        'limit': limit.toString(),
      };

      final Uri uri = Uri.parse('$_cryptoCompareBaseUrl/v2/news/').replace(queryParameters: queryParams);

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['Data'] == null || data['Data'].isEmpty) {
          return [];
        }

        final List<dynamic> articles = data['Data'];

        // Отфильтровываем дубликаты на основе исключенных заголовков
        final filteredArticles = articles.where((article) {
          final title = article['title'] ?? '';
          // Проверяем, не содержится ли заголовок в списке исключений
          return !excludeTitles.any((excludeTitle) =>
              _isSimilarTitle(title, excludeTitle));
        }).toList();

        return filteredArticles.map<NewsItem>((article) {
          // Convert the API response to our NewsItem model
          final title = article['title'] ?? '';
          final body = article['body'] ?? '';
          final imageUrl = article['imageurl'] ?? 'https://via.placeholder.com/300x200?text=No+Image';
          final source = article['source'] ?? 'CryptoCompare';
          
          final newsItem = NewsItem(
            id: _generateIdFromTitle(title),
            title: title,
            aiGeneratedTitle: article['aiGeneratedTitle'],
            description: body,
            imageUrl: imageUrl,
            publishedAt: DateTime.fromMillisecondsSinceEpoch((article['published_on'] ?? 0) * 1000),
            source: source,
            url: article['url'] ?? '',
            sentiment: _determineSentiment(title),
            tags: _extractTags(title, body),
            category: NewsCategory.crypto, // Все новости из CryptoCompare относятся к категории crypto
            content: body,
            summary: article['summary'],
            rewrittenContent: article['rewrittenContent'],
            fetchedAt: DateTime.now(),
          );
          return newsItem;
        }).toList();
      } else {
        throw Exception('Failed to load crypto news: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching crypto news: $e');
    }
  }

  // Get combined news (both from NewsAPI and CryptoCompare)
  Future<List<NewsItem>> getCombinedNews({
    String category = '',
    String query = '',
    int pageSize = 20,
    int page = 1,
  }) async {
    // Получаем сначала новости из NewsAPI
    final newsApiItems = await getTopHeadlines(
      category: category,
      query: query,
      pageSize: pageSize,
      page: page,
    );
    
    // Извлекаем заголовки для фильтрации дубликатов
    final newsApiTitles = newsApiItems.map((item) => item.title).toList();
    
    // Получаем новости из CryptoCompare только если текущая категория - crypto или all
    List<NewsItem> cryptoNewsItems = [];
    if (category.isEmpty || category == 'crypto' || category == 'business') {
      cryptoNewsItems = await getCryptoNews(
        limit: pageSize,
        excludeTitles: newsApiTitles, // Передаем заголовки для исключения дубликатов
      );
    }
    
    // Объединяем результаты
    final combined = [...newsApiItems, ...cryptoNewsItems];
    
    // Сортируем по дате (сначала новые)
    combined.sort((a, b) => b.publishedAt.compareTo(a.publishedAt));
    
    return combined;
  }

  // Метод для сравнения заголовков на подобие (чтобы избежать дубликатов)
  bool _isSimilarTitle(String title1, String title2) {
    if (title1.isEmpty || title2.isEmpty) return false;
    
    // Простая проверка на точное совпадение
    if (title1 == title2) return true;
    
    // Проверка на частичное совпадение (одна строка содержится в другой)
    title1 = title1.toLowerCase();
    title2 = title2.toLowerCase();
    
    return title1.contains(title2) || title2.contains(title1) ||
           _calculateSimilarity(title1, title2) > 0.7; // Если подобие более 70%
  }
  
  // Расчет степени подобия двух строк (простая метрика)
  double _calculateSimilarity(String s1, String s2) {
    // Простая реализация - подсчитываем общие слова
    final words1 = s1.split(' ');
    final words2 = s2.split(' ');
    
    final commonWords = words1.where((word) => words2.contains(word)).length;
    final totalUniqueWords = words1.toSet().union(words2.toSet()).length;
    
    return totalUniqueWords > 0 ? commonWords / totalUniqueWords : 0.0;
  }
  
  // Генерация уникального ID на основе заголовка
  String _generateIdFromTitle(String title) {
    return crypto.md5.convert(utf8.encode(title)).toString();
  }
  
  // Get everything (search)
  Future<List<NewsItem>> searchNews({
    required String query,
    String sortBy = 'publishedAt',
    int pageSize = 20,
    int page = 1,
  }) async {
    try {
      // Make a real API call to NewsAPI
      final Map<String, String> queryParams = {
        'apiKey': _newsApiKey,
        'q': query,
        'sortBy': sortBy,
        'pageSize': pageSize.toString(),
        'page': page.toString(),
        'language': 'en', // Используем английский язык для большего количества результатов
      };

      final Uri uri = Uri.parse('$_baseUrl/everything').replace(queryParameters: queryParams);

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        final List<dynamic> articles = data['articles'] ?? [];

        if (articles.isEmpty) {
          return [];
        }

        return articles.map<NewsItem>((article) {
          // Map the API response to our NewsItem model
          final newsItem = NewsItem(
            id: article['url'] ?? '',
            title: article['title'] ?? '',
            aiGeneratedTitle: article['aiGeneratedTitle'],
            description: article['description'] ?? '',
            imageUrl: article['urlToImage'] ?? 'https://via.placeholder.com/300x200?text=No+Image',
            publishedAt: DateTime.parse(article['publishedAt'] ?? DateTime.now().toIso8601String()),
            source: article['source']['name'] ?? '',
            url: article['url'] ?? '',
            sentiment: _determineSentiment(article['title'] ?? ''),
            tags: _extractTags(article['title'] ?? '', article['description'] ?? ''),
            category: _determineCategory(article['title'] ?? '', article['description'] ?? ''),
            content: article['content'] ?? article['description'] ?? '',
            summary: article['summary'],
            rewrittenContent: article['rewrittenContent'],
            fetchedAt: DateTime.now(),
          );
          return newsItem;
        }).toList();
      } else {
        throw Exception('Failed to search news: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error searching news: $e');
    }
  }

  // Helper method to determine sentiment (would use NLP in a real app)
  SentimentType _determineSentiment(String title) {
    final lowerTitle = title.toLowerCase();

    // Very basic sentiment analysis
    if (lowerTitle.contains('surge') ||
        lowerTitle.contains('rise') ||
        lowerTitle.contains('gain') ||
        lowerTitle.contains('bull') ||
        lowerTitle.contains('high')) {
      return SentimentType.positive;
    } else if (lowerTitle.contains('drop') ||
               lowerTitle.contains('fall') ||
               lowerTitle.contains('crash') ||
               lowerTitle.contains('bear') ||
               lowerTitle.contains('low')) {
      return SentimentType.negative;
    } else {
      return SentimentType.neutral;
    }
  }

  // Теги полностью отключены
  List<String> _extractTags(String title, String description) {
    return [];
  }

  // Helper method to determine category
  NewsCategory _determineCategory(String title, String description) {
    final combinedText = '$title $description'.toLowerCase();

    if (combinedText.contains('bitcoin') ||
        combinedText.contains('ethereum') ||
        combinedText.contains('crypto') ||
        combinedText.contains('blockchain') ||
        combinedText.contains('token') ||
        combinedText.contains('coin') ||
        combinedText.contains('defi')) {
      return NewsCategory.crypto;
    } else if (combinedText.contains('stock') ||
               combinedText.contains('market') ||
               combinedText.contains('nasdaq') ||
               combinedText.contains('dow') ||
               combinedText.contains('s&p') ||
               combinedText.contains('investor') ||
               combinedText.contains('investment')) {
      return NewsCategory.stocks;
    } else if (combinedText.contains('whale') ||
               combinedText.contains('large holder') ||
               combinedText.contains('institutional') ||
               combinedText.contains('big investor') ||
               combinedText.contains('large transaction')) {
      return NewsCategory.whales;
    } else if (combinedText.contains('ai') ||
               combinedText.contains('artificial intelligence') ||
               combinedText.contains('machine learning') ||
               combinedText.contains('neural') ||
               combinedText.contains('deep learning') ||
               combinedText.contains('algorithm') ||
               combinedText.contains('robot')) {
      return NewsCategory.ai;
    } else if (combinedText.contains('politic') ||
               combinedText.contains('government') ||
               combinedText.contains('president') ||
               combinedText.contains('congress') ||
               combinedText.contains('senate') ||
               combinedText.contains('election') ||
               combinedText.contains('vote') ||
               combinedText.contains('law') ||
               combinedText.contains('regulation')) {
      return NewsCategory.politics;
    } else {
      return NewsCategory.all;
    }
  }

  // Получить новости с CryptoPanic
  Future<List<NewsItem>> getCryptoPanicNews({int limit = 20}) async {
    final apiKey = EnvConfig.cryptoPanicApiKey;
    final url = Uri.parse('https://cryptopanic.com/api/v1/posts/?auth_token=$apiKey&public=true&filter=all&kind=news&currencies=BTC,ETH&limit=$limit');
    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> results = data['results'] ?? [];
        return results.map<NewsItem>((item) {
          return NewsItem(
            id: item['id'].toString(),
            title: item['title'] ?? '',
            aiGeneratedTitle: item['aiGeneratedTitle'],
            description: item['description'] ?? item['title'] ?? '',
            imageUrl: item['metadata']?['image'] ?? 'https://via.placeholder.com/300x200?text=CryptoPanic',
            publishedAt: DateTime.parse(item['published_at'] ?? DateTime.now().toIso8601String()),
            source: item['source']?['title'] ?? 'CryptoPanic',
            url: item['url'] ?? '',
            sentiment: SentimentType.neutral, // Можно доработать
            tags: (item['currencies'] as List?)?.map((c) => c['code'] as String).toList() ?? [],
            category: NewsCategory.crypto,
            content: item['description'] ?? item['title'] ?? '',
            summary: item['summary'] ?? '',
            rewrittenContent: item['rewrittenContent'],
            fetchedAt: DateTime.now(),
          );
        }).toList();
      } else {
        throw Exception('CryptoPanic error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('CryptoPanic exception: $e');
    }
  }

  // Получить новости с Polygon.io
  Future<List<NewsItem>> getPolygonNews({int limit = 20}) async {
    final apiKey = EnvConfig.polygonApiKey;
    final url = Uri.parse('https://api.polygon.io/v2/reference/news?limit=$limit&apiKey=$apiKey');
    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> results = data['results'] ?? [];
        return results.map<NewsItem>((item) {
          return NewsItem(
            id: item['id'] ?? item['url'] ?? '',
            title: item['title'] ?? '',
            aiGeneratedTitle: item['aiGeneratedTitle'],
            description: item['description'] ?? item['title'] ?? '',
            imageUrl: (item['image_url'] ?? 'https://via.placeholder.com/300x200?text=Polygon'),
            publishedAt: DateTime.parse(item['published_utc'] ?? DateTime.now().toIso8601String()),
            source: item['publisher']?['name'] ?? 'Polygon',
            url: item['article_url'] ?? item['url'] ?? '',
            sentiment: SentimentType.neutral, // Можно доработать
            tags: (item['tickers'] as List?)?.map((t) => t as String).toList() ?? [],
            category: NewsCategory.stocks,
            content: item['description'] ?? item['title'] ?? '',
            summary: item['summary'] ?? '',
            rewrittenContent: item['rewrittenContent'],
            fetchedAt: DateTime.now(),
          );
        }).toList();
      } else {
        throw Exception('Polygon error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Polygon exception: $e');
    }
  }

  // Получить общий список новостей из всех источников
  Future<List<NewsItem>> getAllNews() async {
    try {
      final response = await http.get(Uri.parse('http://localhost:4000/news'));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> newsList = data['news'] ?? [];

        debugPrint('[NewsService] Загружено [1m${newsList.length}[0m новостей из бэкенда');

        return newsList.map<NewsItem>((item) {
          // Правильно парсим sentiment из бэкенда
          final sentiment = _parseSentimentFromBackend(item['sentiment']);
          final sentimentData = _parseSentimentDataFromBackend(item);
          final category = _determineCategoryFromTags(List<String>.from(item['tags'] ?? []));

          debugPrint('[NewsService] Новость: ${item['title']} - Sentiment: ${sentiment.toString()}');

          return NewsItem(
            id: item['id'] ?? '',
            title: item['title'] ?? '',
            aiGeneratedTitle: item['aiGeneratedTitle'],
            description: item['description'] ?? '',
            imageUrl: item['imageUrl'] ?? 'https://via.placeholder.com/300x200?text=No+Image',
            publishedAt: DateTime.tryParse(item['publishedAt'] ?? '') ?? DateTime.now(),
            source: item['source'] ?? '',
            url: item['url'] ?? '',
            sentiment: sentiment, // Используем правильно распарсенный sentiment
            tags: (item['tags'] as List?)?.map((e) => e.toString()).toList() ?? [],
            category: category, // Используем правильно определенную категорию
            content: item['content'] ?? '',
            summary: item['summary'] ?? '', // AI summary
            sentimentData: sentimentData, // Добавляем полные данные sentiment
            rewrittenContent: item['rewrittenContent'],
            fetchedAt: DateTime.now(),
          );
        }).toList();
      } else {
        throw Exception('Failed to load news from backend: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error loading news from backend: $e');
      // Возвращаем пустой список вместо исключения, чтобы приложение не крашилось
      return [];
    }
  }
}
