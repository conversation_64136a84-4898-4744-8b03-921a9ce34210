import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/candle_data.dart';

class BinanceApiService {
  final String baseUrl = 'https://api.binance.com/api/v3';

  Future<List<CandleData>> fetchCandles(String symbol, String interval, int limit) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/klines?symbol=$symbol&interval=$interval&limit=$limit'),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((candle) => CandleData.fromJson(candle)).toList();
      } else {
        print('Failed to load candles: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('Error fetching candles: $e');
      return [];
    }
  }

  Future<List<String>> fetchAvailableSymbols() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exchangeInfo'),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> symbols = data['symbols'];

        // Filter for USDT pairs only
        return symbols
            .where((symbol) =>
                symbol['symbol'].toString().endsWith('USDT') &&
                symbol['status'] == 'TRADING')
            .map<String>((symbol) => symbol['symbol'].toString())
            .toList();
      } else {
        print('Failed to load symbols: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('Error fetching symbols: $e');
      return [];
    }
  }
}
