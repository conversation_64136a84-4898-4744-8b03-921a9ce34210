{"version": "2.0.0", "tasks": [{"label": "🧹 Flutter Clean", "type": "shell", "command": "flutter", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "📦 Flutter Pub Get", "type": "shell", "command": "flutter", "args": ["pub", "get"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "🔨 Build Full APK", "type": "shell", "command": "flutter", "args": ["build", "apk", "lib/main.dart", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "📱 Build Lite APK", "type": "shell", "command": "flutter", "args": ["build", "apk", "lib/main_lite.dart", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "🌐 Build Web", "type": "shell", "command": "flutter", "args": ["build", "web", "lib/main.dart"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "🧪 Run Tests", "type": "shell", "command": "flutter", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "🔍 Flutter Analyze", "type": "shell", "command": "flutter", "args": ["analyze"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "✨ Format Code", "type": "shell", "command": "dart", "args": ["format", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "📋 Flutter Doctor", "type": "shell", "command": "flutter", "args": ["doctor", "-v"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "📱 List Devices", "type": "shell", "command": "flutter", "args": ["devices"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}