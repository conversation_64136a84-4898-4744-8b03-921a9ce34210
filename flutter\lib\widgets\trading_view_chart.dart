import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'dart:html' as html;
import 'dart:ui_web' as ui_web;
import 'package:webview_flutter/webview_flutter.dart';
import '../models/trading_simulator_models.dart';

class TradingViewChart extends StatefulWidget {
  final List<CandleData> candles;
  final double initialZoom;
  final bool showEntryPoint;
  final double? entryPrice;
  final DateTime? entryTime;
  final bool isDarkMode;
  final Function(double) onZoomChanged;
  final Function(double) onScrollChanged;

  const TradingViewChart({
    Key? key,
    required this.candles,
    this.initialZoom = 1.0,
    this.showEntryPoint = false,
    this.entryPrice,
    this.entryTime,
    this.isDarkMode = false,
    required this.onZoomChanged,
    required this.onScrollChanged,
  }) : super(key: key);

  @override
  State<TradingViewChart> createState() => _TradingViewChartState();
}

class _TradingViewChartState extends State<TradingViewChart> {
  // For web
  late String _viewType;
  late html.IFrameElement _iframeElement;
  bool _isLoaded = false;
  String _htmlContent = '';

  // For mobile/desktop
  late WebViewController _controller;
  bool _isWebViewReady = false;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _initWebChart();
    } else {
      _initMobileChart();
    }
  }

  void _initWebChart() async {
    _viewType = 'tradingview-chart-${DateTime.now().millisecondsSinceEpoch}';
    _htmlContent = await rootBundle.loadString('assets/tradingview_chart.html');
    html.window.onMessage.listen(_handleMessage);
    _iframeElement = html.IFrameElement()
      ..style.border = 'none'
      ..style.height = '100%'
      ..style.width = '100%'
      ..srcdoc = _htmlContent;
    ui_web.platformViewRegistry.registerViewFactory(
      _viewType,
      (int viewId) => _iframeElement,
    );
        setState(() {
      _isLoaded = true;
    });
    Future.delayed(const Duration(milliseconds: 500), _sendCandlesToIframe);
  }

  void _handleMessage(html.MessageEvent event) {
    // Можно реализовать обработку сообщений от iframe, если нужно
  }

  void _sendCandlesToIframe() {
    if (!_isLoaded || widget.candles.isEmpty) return;
    final List<Map<String, dynamic>> candlesData = widget.candles.map((candle) => {
      'time': (candle.time.millisecondsSinceEpoch ~/ 1000),
      'open': candle.open,
      'high': candle.high,
      'low': candle.low,
      'close': candle.close,
    }).toList();
    print('Отправляем свечи в iframe: count = [32m${candlesData.length}[0m, first = [33m${candlesData.isNotEmpty ? candlesData.first : 'empty'}[0m');
    final message = jsonEncode({
      'action': 'loadCandles',
      'data': candlesData,
    });
    Future.delayed(const Duration(milliseconds: 500), () {
      _iframeElement.contentWindow?.postMessage(message, '*');
    });
  }

  void _initMobileChart() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.transparent)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            _isWebViewReady = true;
            _updateChartData();
          },
        ),
      )
      ..loadHtmlString(_getChartHtml());
  }

  void _updateChartData() {
    final candleData = widget.candles.map((candle) => {
      'time': candle.time.millisecondsSinceEpoch ~/ 1000,
      'open': candle.open,
      'high': candle.high,
      'low': candle.low,
      'close': candle.close,
    }).toList();
    _controller.runJavaScript('''
      updateChartData(${jsonEncode(candleData)});
      ${widget.showEntryPoint && widget.entryPrice != null && widget.entryTime != null ? '''
        addEntryPoint(${widget.entryPrice}, ${widget.entryTime!.millisecondsSinceEpoch ~/ 1000});
      ''' : ''}
    ''');
  }

  String _getChartHtml() {
    return '''
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
          <style>
            body { margin: 0; background: transparent; }
            #chart { width: 100%; height: 100%; }
          </style>
        </head>
        <body>
          <div id="chart"></div>
          <script>
            const chart = LightweightCharts.createChart(document.getElementById('chart'), {
              layout: {
                background: { color: 'transparent' },
                textColor: '${widget.isDarkMode ? '#ffffff' : '#000000'}',
              },
              grid: {
                vertLines: { color: '${widget.isDarkMode ? '#2B2B43' : '#E6E6E6'}' },
                horzLines: { color: '${widget.isDarkMode ? '#2B2B43' : '#E6E6E6'}' },
              },
              width: window.innerWidth,
              height: window.innerHeight,
            });
            const candlestickSeries = chart.addCandlestickSeries({
              upColor: '#26a69a',
              downColor: '#ef5350',
              borderVisible: false,
              wickUpColor: '#26a69a',
              wickDownColor: '#ef5350',
            });
            let entryPointMarker = null;
            function updateChartData(data) {
              candlestickSeries.setData(data);
              chart.timeScale().fitContent();
            }
            function addEntryPoint(price, time) {
              if (entryPointMarker) {
                candlestickSeries.removePriceLine(entryPointMarker);
              }
              entryPointMarker = candlestickSeries.createPriceLine({
                price: price,
                color: '#2962FF',
                lineWidth: 2,
                lineStyle: 2,
                axisLabelVisible: true,
                title: 'Entry',
              });
            }
          </script>
        </body>
      </html>
    ''';
  }

  @override
  void didUpdateWidget(TradingViewChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (kIsWeb) {
      _sendCandlesToIframe();
    } else {
      if (_isWebViewReady) {
        _updateChartData();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      if (!_isLoaded) {
        return const Center(child: CircularProgressIndicator());
      }
      return HtmlElementView(viewType: _viewType);
    } else {
      return WebViewWidget(controller: _controller);
    }
  }
}
