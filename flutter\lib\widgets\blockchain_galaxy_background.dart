import 'dart:async';
import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';

/// Animated background that mimics a "cosmic blockchain map":
/// faint stars (nodes) connected with blurred lines that slowly flicker and
/// new nodes appear over time. Animation is intentionally slow and subtle so
/// as not to distract users while reading content.
class BlockchainGalaxyBackground extends StatefulWidget {
  const BlockchainGalaxyBackground({Key? key}) : super(key: key);

  @override
  State<BlockchainGalaxyBackground> createState() => _BlockchainGalaxyBackgroundState();
}

class _Node {
  _Node(this.dx, this.dy, this.phase);
  double dx; // relative 0..1
  double dy; // relative 0..1
  double phase; // 0..1, for flicker offset
}

class _BlockchainGalaxyBackgroundState extends State<BlockchainGalaxyBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Timer _addNodeTimer;
  final Random _rng = Random();
  final List<_Node> _nodes = [];

  static const int _maxNodes = 90;

  @override
  void initState() {
    super.initState();

    // Populate initial nodes
    for (int i = 0; i < 60; i++) {
      _nodes.add(_randomNode());
    }

    _controller = AnimationController(vsync: this, duration: const Duration(seconds: 20))
      ..addListener(() => setState(() {}))
      ..repeat();

    // Periodically add/replace nodes to emulate network growth.
    _addNodeTimer = Timer.periodic(const Duration(seconds: 6), (_) {
      setState(() {
        if (_nodes.length >= _maxNodes) {
          // replace a random node to keep count stable
          _nodes[_rng.nextInt(_nodes.length)] = _randomNode();
        } else {
          _nodes.add(_randomNode());
        }
      });
    });
  }

  _Node _randomNode() {
    return _Node(_rng.nextDouble(), _rng.nextDouble(), _rng.nextDouble());
  }

  @override
  void dispose() {
    _controller.dispose();
    _addNodeTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size.infinite,
      painter: _GalaxyPainter(
        nodes: _nodes,
        progress: _controller.value,
      ),
    );
  }
}

class _GalaxyPainter extends CustomPainter {
  _GalaxyPainter({required this.nodes, required this.progress});

  final List<_Node> nodes;
  final double progress; // 0..1

  @override
  void paint(Canvas canvas, Size size) {
    // Base dark gradient sky
    final Rect rect = Offset.zero & size;
    const Color topColor = Color(0xFF0A0B0D);
    const Color bottomColor = Color(0xFF14161A);
    final Paint bgPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [topColor, bottomColor],
      ).createShader(rect);
    canvas.drawRect(rect, bgPaint);

    // Prepare paints
    final Paint nodePaint = Paint()
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    final Paint linePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.8
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);

    // Flicker intensity varies with sin wave based on global progress and each node phase
    double flicker(int index) {
      final _Node n = nodes[index];
      return 0.6 + 0.4 * sin(2 * pi * (progress + n.phase));
    }

    // Draw lines first (so glow from nodes overlays them slightly)
    const double maxConnDist = 160; // px
    for (int i = 0; i < nodes.length; i++) {
      final _Node a = nodes[i];
      final Offset pa = Offset(a.dx * size.width, a.dy * size.height);
      for (int j = i + 1; j < nodes.length; j++) {
        final _Node b = nodes[j];
        final Offset pb = Offset(b.dx * size.width, b.dy * size.height);
        final double d2 = (pa - pb).distanceSquared;
        if (d2 < maxConnDist * maxConnDist) {
          final double d = sqrt(d2);
          final double opacity = (1 - (d / maxConnDist)) * 0.1;
          linePaint.color = Colors.white.withOpacity(opacity * 0.7);
          canvas.drawLine(pa, pb, linePaint);
        }
      }
    }

    // Draw nodes (stars)
    for (int i = 0; i < nodes.length; i++) {
      final _Node n = nodes[i];
      final Offset p = Offset(n.dx * size.width, n.dy * size.height);
      final double brightness = flicker(i);
      nodePaint.color = Colors.white.withOpacity(brightness);
      canvas.drawCircle(p, 1.5 + 1.5 * brightness, nodePaint);
    }
  }

  @override
  bool shouldRepaint(covariant _GalaxyPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.nodes != nodes;
  }
} 