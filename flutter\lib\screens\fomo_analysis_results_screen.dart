import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/fomo_psychological_model.dart';
import '../models/anti_fomo_simulator_models.dart';
import '../widgets/gradient_background.dart';
import '../screens/anti_fomo_simulator_screen.dart';

class FomoAnalysisResultsScreen extends StatelessWidget {
  final FomoAnalysisResult result;
  final TraderRole role;
  final double finalBalance;
  final double initialBalance;
  final Widget? chartWidget;
  
  const FomoAnalysisResultsScreen({
    super.key,
    required this.result,
    required this.role,
    required this.finalBalance,
    required this.initialBalance,
    this.chartWidget,
  });
  
  @override
  Widget build(BuildContext context) {
    final totalProfit = finalBalance - initialBalance;
    final profitPercentage = (totalProfit / initialBalance) * 100;
    final isProfitable = totalProfit >= 0;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trading Session Analysis'),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: GradientBackground(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Карточки анализа по центру
                Wrap(
                  spacing: 32,
                  runSpacing: 24,
                  alignment: WrapAlignment.center,
                  children: [
                    _buildAnalysisCard(_buildHeaderSection(
                      finalBalance - initialBalance >= 0,
                      finalBalance - initialBalance,
                      ((finalBalance - initialBalance) / initialBalance) * 100,
                    )),
                    _buildAnalysisCard(_buildFomoScoreSection(context)),
                    _buildAnalysisCard(_buildDetailedMetricsSection()),
                    if (result.cognitiveDistortions.isNotEmpty)
                      _buildAnalysisCard(_buildCognitiveDistortionsSection()),
                    if (result.recommendations.isNotEmpty)
                      _buildAnalysisCard(_buildRecommendationsSection()),
                  ],
                ),
                const SizedBox(height: 32),
                // Кнопки ниже, по центру
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _CupertinoButton(
                      text: 'Play Again',
                      color: const Color(0xFF34C759),
                      onPressed: () => Navigator.pop(context, 'playAgain'),
                    ),
                    const SizedBox(width: 16),
                    _CupertinoButton(
                      text: 'Exit',
                      color: Colors.grey[800]!,
                      textColor: Colors.white70,
                      onPressed: () {
                        Navigator.of(context).pushAndRemoveUntil(
                          MaterialPageRoute(builder: (context) => AntiFOMOSimulatorScreen()),
                          (route) => false,
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildHeaderSection(bool isProfitable, double totalProfit, double profitPercentage) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isProfitable ? Icons.trending_up : Icons.trending_down,
                color: isProfitable ? Colors.green : Colors.red,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                isProfitable ? 'Profitable Session' : 'Unprofitable Session',
                style: TextStyle(
                  color: isProfitable ? Colors.green : Colors.red,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStatCard(
                'Initial Capital',
                '\$${initialBalance.toStringAsFixed(2)}',
                Colors.blue,
              ),
              _buildStatCard(
                'Final Balance',
                '\$${finalBalance.toStringAsFixed(2)}',
                isProfitable ? Colors.green : Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildStatCard(
            'Net Profit/Loss',
            '${isProfitable ? '+' : ''}\$${totalProfit.toStringAsFixed(2)} (${isProfitable ? '+' : ''}${profitPercentage.toStringAsFixed(2)}%)',
            isProfitable ? Colors.green : Colors.red,
            fullWidth: true,
          ),
        ],
      ),
    );
  }
  
  Widget _buildFomoScoreSection(BuildContext context) {
    final scoreColor = _getScoreColor(result.overallScore);
    final barPadding = 18.0; // padding to keep the circle inside
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'FOMO Susceptibility Score',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'How influenced you are by market hype and fear of missing out',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 80,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  margin: EdgeInsets.symmetric(horizontal: barPadding),
                  height: 16,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Colors.green, Colors.yellow, Colors.orange, Colors.red],
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                // Числовой индикатор поверх градиента
                LayoutBuilder(
                  builder: (context, constraints) {
                    final barWidth = constraints.maxWidth - 2 * barPadding;
                    final indicatorSize = 24.0;
                    final left = barPadding + (result.overallScore / 100) * (barWidth - indicatorSize).clamp(0, barWidth - indicatorSize);
                    return Positioned(
                      left: left,
                      top: 28,
                      child: Container(
                        width: indicatorSize,
                        height: indicatorSize,
                        decoration: BoxDecoration(
                          color: scoreColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 4,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            result.overallScore.round().toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Low FOMO',
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 14,
                ),
              ),
              const Text(
                'High FOMO',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildDetailedMetricsSection() {
    // Define the metrics we want to display, in the order we want them to appear
    final metricNames = [
      'Impulsivity',
      'Emotional Reactivity',
      'Risk Tolerance',
      'Fatalism',
    ];
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Psychological Metrics',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Ensure metrics appear in the correct order and all are shown
          ...metricNames.map((metricName) {
            final value = result.detailedMetrics[metricName] ?? 50.0; // Default to 50 if missing
            return _buildMetricBar(
              metricName, 
              value, 
              _getScoreColor(value),
            );
          }).toList(),
        ],
      ),
    );
  }
  
  Widget _buildMetricBar(String label, double value, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 15,
                ),
              ),
              Text(
                value.round().toString(),
                style: TextStyle(
                  color: color,
                  fontSize: 15,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LayoutBuilder(
            builder: (context, constraints) {
              final maxWidth = constraints.maxWidth;
              return Stack(
                children: [
                  // Background bar
                  Container(
                    height: 12,
                    width: maxWidth,
                    decoration: BoxDecoration(
                      color: Colors.grey[900],
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  // Value bar
                  Container(
                    height: 12,
                    width: (value / 100) * maxWidth,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
  
  Widget _buildCognitiveDistortionsSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Identified Thinking Patterns',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'Cognitive biases detected in your trading decisions',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 12),
          ...result.cognitiveDistortions.map((distortion) => 
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.psychology,
                    color: Colors.orange,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      distortion,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ).toList(),
        ],
      ),
    );
  }
  
  Widget _buildRecommendationsSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Recommendations',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'Suggestions to improve your trading psychology',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 12),
          ...result.recommendations.map((recommendation) => 
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.lightbulb_outline,
                    color: Colors.yellow,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      recommendation,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ).toList(),
        ],
      ),
    );
  }
  
  Widget _buildStatCard(String label, String value, Color valueColor, {bool fullWidth = false}) {
    return Container(
      width: fullWidth ? double.infinity : null,
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[800]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 13,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              color: valueColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Color _getScoreColor(double score) {
    if (score < 30) {
      return Colors.green;
    } else if (score < 60) {
      return Colors.yellow;
    } else if (score < 80) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
  
  Widget _buildAnalysisCard(Widget child) {
    return Container(
      constraints: const BoxConstraints(minWidth: 210, maxWidth: 320, minHeight: 240, maxHeight: 420),
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 10),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }
}

class _CupertinoButton extends StatelessWidget {
  final String text;
  final Color color;
  final Color? textColor;
  final VoidCallback onPressed;
  const _CupertinoButton({
    required this.text,
    required this.color,
    this.textColor,
    required this.onPressed,
  });
  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 120),
      curve: Curves.easeOut,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.18),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onPressed,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
            child: Text(
              text,
              style: TextStyle(
                color: textColor ?? Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 15,
                letterSpacing: 0.1,
              ),
            ),
          ),
        ),
      ),
    );
  }
} 