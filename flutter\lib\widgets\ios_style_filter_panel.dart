import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../models/news_item.dart';
import 'package:finance_ai/models/sentiment_types.dart';

class IOSStyleFilterPanel extends StatelessWidget {
  final List<String> selectedTags;
  final SentimentType? selectedSentiment;
  final Function(String) onTagToggle;
  final Function(SentimentType?) onSentimentChanged;

  const IOSStyleFilterPanel({
    super.key,
    required this.selectedTags,
    required this.selectedSentiment,
    required this.onTagToggle,
    required this.onSentimentChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1C1C1E) : Colors.white,
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.0,
            offset: const Offset(0, 2.0),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tags filter
            Row(
              children: [
                Icon(
                  CupertinoIcons.tag,
                  size: 18,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
                const SizedBox(width: 8.0),
                Text(
                  'Filter by tags',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12.0),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                _buildTagChip('Macro', context),
                _buildTagChip('AI', context),
                _buildTagChip('BTC', context),
                _buildTagChip('Ethereum', context),
                _buildTagChip('Memes', context),
                _buildTagChip('DeFi', context),
                _buildTagChip('RWA', context),
                _buildTagChip('NFT', context),
                _buildTagChip('SEC', context),
                _buildTagChip('AirDrop', context),
              ],
            ),
            
            const SizedBox(height: 20.0),
            
            // Sentiment filter
            Row(
              children: [
                Icon(
                  CupertinoIcons.chart_bar,
                  size: 18,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
                const SizedBox(width: 8.0),
                Text(
                  'Filter by sentiment',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12.0),
            Row(
              children: [
                _buildSentimentButton(null, 'All', context),
                const SizedBox(width: 8.0),
                _buildSentimentButton(SentimentType.positive, 'Positive', context),
                const SizedBox(width: 8.0),
                _buildSentimentButton(SentimentType.neutral, 'Neutral', context),
                const SizedBox(width: 8.0),
                _buildSentimentButton(SentimentType.negative, 'Negative', context),
              ],
            ),
            
            const SizedBox(height: 16.0),
            
            // Apply and Clear buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  child: Text(
                    'Clear All',
                    style: TextStyle(
                      color: CupertinoColors.systemBlue,
                      fontSize: 14,
                    ),
                  ),
                  onPressed: () {
                    // Clear all filters
                    onSentimentChanged(null);
                    for (final tag in List<String>.from(selectedTags)) {
                      onTagToggle(tag);
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTagChip(String tag, BuildContext context) {
    final isSelected = selectedTags.contains(tag);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: () => onTagToggle(tag),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
        decoration: BoxDecoration(
          color: isSelected 
              ? CupertinoColors.systemBlue.withOpacity(isDarkMode ? 0.3 : 0.2)
              : isDarkMode ? const Color(0xFF2C2C2E) : const Color(0xFFF2F2F7),
          borderRadius: BorderRadius.circular(16.0),
          border: Border.all(
            color: isSelected 
                ? CupertinoColors.systemBlue
                : isDarkMode ? const Color(0xFF3A3A3C) : const Color(0xFFD1D1D6),
            width: 1.0,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isSelected) ...[
              Icon(
                CupertinoIcons.checkmark,
                size: 12.0,
                color: CupertinoColors.systemBlue,
              ),
              const SizedBox(width: 4.0),
            ],
            Text(
              tag,
              style: TextStyle(
                color: isSelected 
                    ? CupertinoColors.systemBlue
                    : isDarkMode ? Colors.grey[300] : Colors.grey[800],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 13.0,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSentimentButton(SentimentType? sentiment, String label, BuildContext context) {
    final isSelected = selectedSentiment == sentiment;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    Color backgroundColor;
    Color textColor;
    Color borderColor;
    IconData icon;
    
    switch (sentiment) {
      case SentimentType.positive:
        backgroundColor = isSelected 
            ? CupertinoColors.systemGreen.withOpacity(isDarkMode ? 0.3 : 0.2)
            : isDarkMode ? const Color(0xFF2C2C2E) : const Color(0xFFF2F2F7);
        textColor = isSelected 
            ? CupertinoColors.systemGreen
            : isDarkMode ? Colors.grey[300]! : Colors.grey[800]!;
        borderColor = isSelected 
            ? CupertinoColors.systemGreen
            : isDarkMode ? const Color(0xFF3A3A3C) : const Color(0xFFD1D1D6);
        icon = CupertinoIcons.arrow_up;
        break;
      case SentimentType.negative:
        backgroundColor = isSelected 
            ? CupertinoColors.systemRed.withOpacity(isDarkMode ? 0.3 : 0.2)
            : isDarkMode ? const Color(0xFF2C2C2E) : const Color(0xFFF2F2F7);
        textColor = isSelected 
            ? CupertinoColors.systemRed
            : isDarkMode ? Colors.grey[300]! : Colors.grey[800]!;
        borderColor = isSelected 
            ? CupertinoColors.systemRed
            : isDarkMode ? const Color(0xFF3A3A3C) : const Color(0xFFD1D1D6);
        icon = CupertinoIcons.arrow_down;
        break;
      case SentimentType.neutral:
        backgroundColor = isSelected 
            ? CupertinoColors.systemGrey.withOpacity(isDarkMode ? 0.3 : 0.2)
            : isDarkMode ? const Color(0xFF2C2C2E) : const Color(0xFFF2F2F7);
        textColor = isSelected 
            ? CupertinoColors.systemGrey
            : isDarkMode ? Colors.grey[300]! : Colors.grey[800]!;
        borderColor = isSelected 
            ? CupertinoColors.systemGrey
            : isDarkMode ? const Color(0xFF3A3A3C) : const Color(0xFFD1D1D6);
        icon = CupertinoIcons.minus;
        break;
      default:
        backgroundColor = isSelected 
            ? CupertinoColors.systemBlue.withOpacity(isDarkMode ? 0.3 : 0.2)
            : isDarkMode ? const Color(0xFF2C2C2E) : const Color(0xFFF2F2F7);
        textColor = isSelected 
            ? CupertinoColors.systemBlue
            : isDarkMode ? Colors.grey[300]! : Colors.grey[800]!;
        borderColor = isSelected 
            ? CupertinoColors.systemBlue
            : isDarkMode ? const Color(0xFF3A3A3C) : const Color(0xFFD1D1D6);
        icon = CupertinoIcons.circle_grid_3x3;
        break;
    }
    
    return GestureDetector(
      onTap: () => onSentimentChanged(sentiment),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16.0),
          border: Border.all(
            color: borderColor,
            width: 1.0,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 12.0,
              color: textColor,
            ),
            const SizedBox(width: 4.0),
            Text(
              label,
              style: TextStyle(
                color: textColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 13.0,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
