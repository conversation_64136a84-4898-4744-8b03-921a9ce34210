import 'package:flutter/material.dart';

class SilkImplementation extends StatelessWidget {
  const SilkImplementation({super.key});

  @override
  Widget build(BuildContext context) {
    // Заглушка для мобильных платформ
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1C1C1C).withOpacity(0.3),
            const Color(0xFF2C2C2C).withOpacity(0.1),
          ],
        ),
      ),
    );
  }
} 