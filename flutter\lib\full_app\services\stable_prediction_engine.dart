import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentiment_history_model.dart';
import 'stable_market_analytics.dart';

/// Стабильный движок прогнозирования без случайных элементов
/// Использует детерминированные математические модели и технический анализ
class StablePredictionEngine {
  static const String _cacheKey = 'stable_predictions';
  static const String _cacheDateKey = 'stable_predictions_date';
  
  // Константы для стабильных расчетов
  static const double _baseVolatility = 4.2;
  static const double _trendDamping = 0.88;
  static const double _cyclePeriod = 7.2;
  static const double _meanReversion = 0.18;
  static const double _seasonalityFactor = 0.12;
  static const double _momentumWeight = 0.25;

  /// Генерирует стабильные прогнозы для Regular режима
  static Future<List<SentimentHistoryEntry>> generateRegularPredictions(
    double currentValue,
    Map<String, double> metrics,
    int daysAhead,
  ) async {
    debugPrint('=== STABLE PREDICTION ENGINE (REGULAR) ===');
    
    try {
      // Проверяем кэш
      final cachedPredictions = await _getCachedPredictions('regular');
      if (cachedPredictions != null && cachedPredictions.length >= daysAhead) {
        debugPrint('Using cached regular predictions');
        return cachedPredictions.take(daysAhead).toList();
      }

      // Генерируем новые прогнозы
      final predictions = await _generateStablePredictions(
        currentValue,
        metrics,
        daysAhead,
        isEnhanced: false,
      );

      // Кэшируем до конца дня
      await _cachePredictions(predictions, 'regular');

      debugPrint('Generated ${predictions.length} regular predictions');
      return predictions;

    } catch (e) {
      debugPrint('Error generating regular predictions: $e');
      return _generateFallbackPredictions(currentValue, daysAhead, false);
    }
  }

  /// Генерирует стабильные прогнозы для Enhanced режима
  static Future<List<SentimentHistoryEntry>> generateEnhancedPredictions(
    double currentValue,
    Map<String, double> metrics,
    int daysAhead,
  ) async {
    debugPrint('=== STABLE PREDICTION ENGINE (ENHANCED) ===');
    
    try {
      // Проверяем кэш
      final cachedPredictions = await _getCachedPredictions('enhanced');
      if (cachedPredictions != null && cachedPredictions.length >= daysAhead) {
        debugPrint('Using cached enhanced predictions');
        return cachedPredictions.take(daysAhead).toList();
      }

      // Генерируем новые прогнозы
      final predictions = await _generateStablePredictions(
        currentValue,
        metrics,
        daysAhead,
        isEnhanced: true,
      );

      // Кэшируем до конца дня
      await _cachePredictions(predictions, 'enhanced');

      debugPrint('Generated ${predictions.length} enhanced predictions');
      return predictions;

    } catch (e) {
      debugPrint('Error generating enhanced predictions: $e');
      return _generateFallbackPredictions(currentValue, daysAhead, true);
    }
  }

  /// Основной метод генерации стабильных прогнозов
  static Future<List<SentimentHistoryEntry>> _generateStablePredictions(
    double currentValue,
    Map<String, double> metrics,
    int daysAhead,
    {required bool isEnhanced}
  ) async {
    final predictions = <SentimentHistoryEntry>[];
    final today = DateTime.now();
    final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;

    // Получаем технические индикаторы
    final technicalIndicators = StableMarketAnalytics.getEnhancedTechnicalIndicators(currentValue, metrics);
    
    // Базовые параметры для прогнозирования
    final volatility = technicalIndicators['volatility']!;
    final trendStrength = technicalIndicators['trend_strength']!;
    final momentum = technicalIndicators['momentum']!;
    final rsi = technicalIndicators['rsi']!;
    final macd = technicalIndicators['macd']!;
    
    // Множители для Enhanced режима
    final volatilityMultiplier = isEnhanced ? 1.4 : 1.0;
    final trendMultiplier = isEnhanced ? 1.3 : 1.0;
    final seasonalityMultiplier = isEnhanced ? 1.5 : 1.0;

    debugPrint('Base parameters: value=$currentValue, volatility=$volatility, trend=$trendStrength, momentum=$momentum');
    debugPrint('Enhanced mode: $isEnhanced (multipliers: vol=${volatilityMultiplier}x, trend=${trendMultiplier}x)');

    for (int i = 1; i <= daysAhead; i++) {
      final futureDate = today.add(Duration(days: i));
      
      // 1. Базовое значение
      double predictedValue = currentValue;
      
      // 2. Трендовый компонент с затуханием
      final trendDecay = math.pow(_trendDamping, i);
      final trendEffect = trendStrength * trendDecay * trendMultiplier * i * 0.5;
      predictedValue += trendEffect;
      
      // 3. Циклический компонент (рыночные циклы)
      final cyclicalPhase = 2 * math.pi * i / _cyclePeriod;
      final cyclicalEffect = math.sin(cyclicalPhase + dayOfYear * 0.1) * volatility * volatilityMultiplier * 0.3;
      predictedValue += cyclicalEffect;
      
      // 4. Импульс (momentum)
      final momentumDecay = math.exp(-i * 0.15);
      final momentumEffect = momentum * _momentumWeight * momentumDecay;
      predictedValue += momentumEffect;
      
      // 5. Сезонность
      final yearPhase = 2 * math.pi * (dayOfYear + i) / 365;
      final seasonalEffect = math.sin(yearPhase) * _seasonalityFactor * seasonalityMultiplier * 10.0;
      predictedValue += seasonalEffect;
      
      // 6. Возврат к среднему (mean reversion)
      final meanReversionEffect = (50.0 - predictedValue) * _meanReversion * math.sqrt(i);
      predictedValue += meanReversionEffect;
      
      // 7. Техническая коррекция на основе RSI и MACD
      if (isEnhanced) {
        // RSI коррекция
        if (rsi > 70) {
          predictedValue -= (rsi - 70) * 0.1 * i; // Перекупленность
        } else if (rsi < 30) {
          predictedValue += (30 - rsi) * 0.1 * i; // Перепроданность
        }
        
        // MACD коррекция
        predictedValue += macd * 2.0 * math.exp(-i * 0.1);
      }
      
      // 8. Ограничение экстремальных значений
      if (predictedValue > 85.0) {
        predictedValue = 85.0 - (predictedValue - 85.0) * 0.5;
      } else if (predictedValue < 15.0) {
        predictedValue = 15.0 + (15.0 - predictedValue) * 0.5;
      }
      
      // Финальное ограничение
      predictedValue = predictedValue.clamp(10.0, 90.0);
      
      // Рассчитываем метрики прогноза
      final predictionMetrics = _calculatePredictionMetrics(
        i,
        predictedValue,
        currentValue,
        technicalIndicators,
        isEnhanced,
      );

      predictions.add(SentimentHistoryEntry(
        date: futureDate,
        value: predictedValue,
        metrics: predictionMetrics,
      ));

      debugPrint('${isEnhanced ? 'Enhanced' : 'Regular'} prediction day $i: ${predictedValue.toStringAsFixed(2)} (confidence: ${predictionMetrics['confidence']?.toStringAsFixed(1)}%)');
    }

    return predictions;
  }

  /// Рассчитывает метрики для прогноза
  static Map<String, double> _calculatePredictionMetrics(
    int dayOffset,
    double predictedValue,
    double currentValue,
    Map<String, double> technicalIndicators,
    bool isEnhanced,
  ) {
    final volatility = technicalIndicators['volatility']!;
    final trendStrength = technicalIndicators['trend_strength']!;
    final accuracy = technicalIndicators['accuracy']!;
    final stability = technicalIndicators['stability']!;
    
    // Базовая уверенность снижается с расстоянием
    double confidence = isEnhanced ? 95.0 : 85.0;
    confidence -= (dayOffset - 1) * (isEnhanced ? 6.0 : 8.0);
    confidence -= volatility * 0.8;
    confidence += math.min(trendStrength.abs() * 3.0, 10.0);
    confidence = confidence.clamp(isEnhanced ? 45.0 : 40.0, isEnhanced ? 95.0 : 85.0);
    
    // Дополнительные метрики для Enhanced режима
    final metrics = <String, double>{
      'confidence': confidence,
      'day_offset': dayOffset.toDouble(),
      'prediction_type': isEnhanced ? 2.0 : 1.0,
      'base_value': currentValue,
      'predicted_change': predictedValue - currentValue,
      'volatility_index': volatility,
    };
    
    if (isEnhanced) {
      metrics.addAll({
        'accuracy': accuracy,
        'stability': stability,
        'trend_strength': trendStrength,
        'momentum_score': technicalIndicators['momentum']!,
        'market_efficiency': technicalIndicators['market_efficiency']!,
        'rsi': technicalIndicators['rsi']!,
        'macd': technicalIndicators['macd']!,
        'support_level': technicalIndicators['support_level']!,
        'resistance_level': technicalIndicators['resistance_level']!,
      });
    }
    
    return metrics;
  }

  /// Генерирует резервные прогнозы
  static List<SentimentHistoryEntry> _generateFallbackPredictions(
    double currentValue,
    int daysAhead,
    bool isEnhanced,
  ) {
    final predictions = <SentimentHistoryEntry>[];
    final today = DateTime.now();
    
    for (int i = 1; i <= daysAhead; i++) {
      final futureDate = today.add(Duration(days: i));
      
      // Простой линейный тренд с циклическими колебаниями
      final trendEffect = (currentValue > 50.0 ? 0.5 : -0.5) * i;
      final cyclicalEffect = math.sin(i * math.pi / 7) * 2.0;
      final value = (currentValue + trendEffect + cyclicalEffect).clamp(10.0, 90.0);
      
      predictions.add(SentimentHistoryEntry(
        date: futureDate,
        value: value,
        metrics: {
          'confidence': (80.0 - i * 5.0).clamp(40.0, 80.0),
          'fallback_prediction': 1.0,
          'prediction_type': isEnhanced ? 2.0 : 1.0,
        },
      ));
    }
    
    return predictions;
  }

  /// Кэширует прогнозы до конца дня
  static Future<void> _cachePredictions(List<SentimentHistoryEntry> predictions, String mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final dateKey = '${today.year}-${today.month}-${today.day}';
      
      final jsonList = predictions.map((entry) => entry.toJson()).toList();
      
      await prefs.setString('${_cacheKey}_$mode', jsonEncode(jsonList));
      await prefs.setString('${_cacheDateKey}_$mode', dateKey);
      
      debugPrint('Cached ${predictions.length} $mode predictions for date: $dateKey');
    } catch (e) {
      debugPrint('Error caching $mode predictions: $e');
    }
  }

  /// Получает кэшированные прогнозы
  static Future<List<SentimentHistoryEntry>?> _getCachedPredictions(String mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final predictionsJson = prefs.getString('${_cacheKey}_$mode');
      final cachedDate = prefs.getString('${_cacheDateKey}_$mode');
      
      if (predictionsJson == null || cachedDate == null) return null;
      
      final today = DateTime.now();
      final todayKey = '${today.year}-${today.month}-${today.day}';
      
      if (cachedDate != todayKey) {
        debugPrint('$mode predictions cache expired (cached: $cachedDate, today: $todayKey)');
        return null;
      }
      
      final jsonList = jsonDecode(predictionsJson) as List;
      final predictions = jsonList
          .map((json) => SentimentHistoryEntry.fromJson(json))
          .toList();
      
      debugPrint('Loaded ${predictions.length} cached $mode predictions for date: $cachedDate');
      return predictions;
      
    } catch (e) {
      debugPrint('Error loading cached $mode predictions: $e');
      return null;
    }
  }

  /// Очищает кэш (для тестирования)
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('${_cacheKey}_regular');
      await prefs.remove('${_cacheDateKey}_regular');
      await prefs.remove('${_cacheKey}_enhanced');
      await prefs.remove('${_cacheDateKey}_enhanced');
      debugPrint('Stable predictions cache cleared');
    } catch (e) {
      debugPrint('Error clearing stable predictions cache: $e');
    }
  }
} 