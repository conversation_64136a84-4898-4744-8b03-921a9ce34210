import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentiment_history_model.dart';
import '../utils/linear_regression.dart';
import 'optimized_sentiment_service.dart';

/// A service for predicting future market sentiment based on historical data
class OptimizedPredictionService {
  final OptimizedSentimentService _sentimentService = OptimizedSentimentService();
  final LinearRegression _regression = LinearRegression();

  // Keys for storing prediction model parameters
  static const String _modelParamsKey = 'prediction_model_params';
  static const String _modelTimestampKey = 'prediction_model_timestamp';
  static const String _predictionsKey = 'cached_predictions';
  static const String _predictionsTimestampKey = 'predictions_timestamp';

  // Duration for which the model and predictions are considered valid
  static const Duration _modelValidDuration = Duration(hours: 24);
  static const Duration _predictionsValidDuration = Duration(hours: 6);

  // Singleton instance
  static final OptimizedPredictionService _instance = OptimizedPredictionService._internal();

  // Factory constructor
  factory OptimizedPredictionService() => _instance;

  // Private constructor
  OptimizedPredictionService._internal() {
    // Load model parameters if available
    _loadModelParameters();
  }

  /// Get historical data
  Future<SentimentHistory> getHistoricalData() async {
    return await _sentimentService.getHistoricalData();
  }

  /// Get yesterday's sentiment value
  Future<SentimentHistoryEntry?> getYesterdaySentiment() async {
    final history = await getHistoricalData();

    if (history.entries.isEmpty) {
      debugPrint('No historical data available for yesterday');
      return null;
    }

    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    final yesterdayDate = DateTime(yesterday.year, yesterday.month, yesterday.day);

    // Find entry for yesterday
    for (var entry in history.entries) {
      final entryDate = DateTime(entry.date.year, entry.date.month, entry.date.day);
      if (entryDate.isAtSameMomentAs(yesterdayDate)) {
        debugPrint('Found yesterday\'s entry: ${entry.value}');
        return entry;
      }
    }

    debugPrint('No entry found for yesterday');
    return null;
  }

  /// Get last week's sentiment value
  Future<SentimentHistoryEntry?> getLastWeekSentiment() async {
    final history = await getHistoricalData();

    if (history.entries.isEmpty) {
      debugPrint('No historical data available for last week');
      return null;
    }

    final lastWeek = DateTime.now().subtract(const Duration(days: 7));
    final lastWeekDate = DateTime(lastWeek.year, lastWeek.month, lastWeek.day);

    // Find entry for last week
    for (var entry in history.entries) {
      final entryDate = DateTime(entry.date.year, entry.date.month, entry.date.day);
      if (entryDate.isAtSameMomentAs(lastWeekDate)) {
        debugPrint('Found last week\'s entry: ${entry.value}');
        return entry;
      }
    }

    debugPrint('No entry found for last week');
    return null;
  }

  /// Predict future sentiment values
  Future<List<SentimentHistoryEntry>> predictFutureSentiment(int daysAhead) async {
    debugPrint('Predicting sentiment for the next $daysAhead days');

    // Try to use cached predictions first
    final cachedPredictions = await _getCachedPredictions();
    if (cachedPredictions != null && cachedPredictions.length >= daysAhead) {
      debugPrint('Using cached predictions');
      return cachedPredictions.take(daysAhead).toList();
    }

    debugPrint('Generating new predictions...');
    final history = await getHistoricalData();
    final entries = history.entries;

    // If we don't have enough data, return default predictions
    if (entries.length < 3) {
      debugPrint('Not enough historical data for prediction (need at least 3, got ${entries.length})');
      final defaultPredictions = _generateDefaultPredictions(daysAhead);
      await _cachePredictions(defaultPredictions);
      return defaultPredictions;
    }

    try {
      // Prepare data for regression
      final x = <double>[];
      final y = <double>[];

      // Sort entries by date (oldest first)
      final sortedEntries = List<SentimentHistoryEntry>.from(entries);
      sortedEntries.sort((a, b) => a.date.compareTo(b.date));

      // Log the data points we're using
      debugPrint('Using ${sortedEntries.length} data points for prediction:');
      for (var i = 0; i < sortedEntries.length; i++) {
        final entry = sortedEntries[i];
        debugPrint('  Day $i: ${entry.date} - ${entry.value}');

        // Add to regression data
        x.add(i.toDouble());
        y.add(entry.value);
      }

      // If all values are the same, use a simple trend
      if (y.toSet().length == 1) {
        debugPrint('All historical values are the same (${y.first}), using simple trend');
        final trendPredictions = _generateTrendPredictions(daysAhead, y.first);
        await _cachePredictions(trendPredictions);
        return trendPredictions;
      }

      // Train the regression model
      _regression.train(x, y);

      // Save the model parameters for future use
      await _saveModelParameters();

      // Calculate R-squared to check prediction quality
      final rSquared = _regression.calculateRSquared(x, y);
      debugPrint('Linear regression R-squared: $rSquared');

      // If R-squared is too low, the model is not reliable
      if (rSquared < 0.3) {
        debugPrint('R-squared too low, using average of last 3 days');
        final lastThreeValues = sortedEntries.reversed.take(3).map((e) => e.value).toList();
        final average = lastThreeValues.reduce((a, b) => a + b) / lastThreeValues.length;
        final trendPredictions = _generateTrendPredictions(daysAhead, average);
        await _cachePredictions(trendPredictions);
        return trendPredictions;
      }

      // Generate predictions
      final predictions = <SentimentHistoryEntry>[];
      final today = DateTime.now();
      final lastX = x.last;

      for (int i = 1; i <= daysAhead; i++) {
        final futureDate = today.add(Duration(days: i));
        final futureX = lastX + i;

        // Predict the value
        double predictedValue = _regression.predict(futureX);

        // Clamp the value to the valid range
        predictedValue = predictedValue.clamp(0.0, 100.0);

        debugPrint('Prediction for day $i ($futureDate): $predictedValue');

        // Create a prediction entry
        predictions.add(SentimentHistoryEntry(
          date: futureDate,
          value: predictedValue,
          metrics: {}, // Empty metrics for predictions
        ));
      }

      // Cache the predictions for future use
      await _cachePredictions(predictions);

      return predictions;
    } catch (e) {
      debugPrint('Error predicting future sentiment: $e');
      final defaultPredictions = _generateDefaultPredictions(daysAhead);
      await _cachePredictions(defaultPredictions);
      return defaultPredictions;
    }
  }

  /// Generate predictions based on a simple trend
  List<SentimentHistoryEntry> _generateTrendPredictions(int daysAhead, double baseValue) {
    final predictions = <SentimentHistoryEntry>[];
    final today = DateTime.now();

    // Use a fixed trend direction based on the day of the month
    // This ensures the trend is consistent for the same day
    final dayOfMonth = today.day;
    final trendDirection = (dayOfMonth % 2 == 0) ? 1.0 : -1.0;
    final maxChange = 3.0; // Maximum 3% change (reduced from 5%)

    for (int i = 1; i <= daysAhead; i++) {
      // Calculate a value with a slight trend
      final changePercent = (i / daysAhead) * maxChange * trendDirection;
      final value = baseValue * (1 + changePercent / 100);

      predictions.add(SentimentHistoryEntry(
        date: today.add(Duration(days: i)),
        value: value.clamp(0.0, 100.0),
        metrics: {}, // Empty metrics for predictions
      ));
    }

    return predictions;
  }

  /// Generate default predictions
  List<SentimentHistoryEntry> _generateDefaultPredictions(int daysAhead) {
    final predictions = <SentimentHistoryEntry>[];
    final today = DateTime.now();

    // Use a fixed value of 50 for all predictions instead of random values
    for (int i = 1; i <= daysAhead; i++) {
      predictions.add(SentimentHistoryEntry(
        date: today.add(Duration(days: i)),
        value: 50.0, // Fixed neutral value
        metrics: {}, // Empty metrics for predictions
      ));
    }

    return predictions;
  }

  /// Load model parameters from persistent storage
  Future<void> _loadModelParameters() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if we have saved model parameters
      if (prefs.containsKey(_modelParamsKey) && prefs.containsKey(_modelTimestampKey)) {
        final paramsJson = prefs.getString(_modelParamsKey);
        final timestampStr = prefs.getString(_modelTimestampKey);

        if (paramsJson != null && timestampStr != null) {
          final timestamp = DateTime.parse(timestampStr);
          final now = DateTime.now();

          // Check if the model is still valid
          if (now.difference(timestamp) < _modelValidDuration) {
            final params = jsonDecode(paramsJson);

            // Set regression model parameters
            _regression.setParameters(
              params['slope'] as double,
              params['intercept'] as double
            );

            debugPrint('Loaded saved model parameters: ${params['slope']}, ${params['intercept']}');
            return;
          } else {
            debugPrint('Saved model parameters expired, will train new model');
          }
        }
      }

      debugPrint('No valid saved model parameters found');
    } catch (e) {
      debugPrint('Error loading model parameters: $e');
    }
  }

  /// Save model parameters to persistent storage
  Future<void> _saveModelParameters() async {
    try {
      if (!_regression.isTrained) {
        debugPrint('Cannot save model parameters: model not trained');
        return;
      }

      final prefs = await SharedPreferences.getInstance();

      // Create parameters map
      final params = {
        'slope': _regression.slope,
        'intercept': _regression.intercept,
      };

      // Save parameters and timestamp
      await prefs.setString(_modelParamsKey, jsonEncode(params));
      await prefs.setString(_modelTimestampKey, DateTime.now().toIso8601String());

      debugPrint('Saved model parameters: ${_regression.slope}, ${_regression.intercept}');
    } catch (e) {
      debugPrint('Error saving model parameters: $e');
    }
  }

  /// Get cached predictions if available
  Future<List<SentimentHistoryEntry>?> _getCachedPredictions() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if we have cached predictions
      if (prefs.containsKey(_predictionsKey) && prefs.containsKey(_predictionsTimestampKey)) {
        final predictionsJson = prefs.getString(_predictionsKey);
        final timestampStr = prefs.getString(_predictionsTimestampKey);

        if (predictionsJson != null && timestampStr != null) {
          final timestamp = DateTime.parse(timestampStr);
          final now = DateTime.now();

          // Check if the predictions are still valid
          if (now.difference(timestamp) < _predictionsValidDuration) {
            final jsonList = jsonDecode(predictionsJson) as List;
            final predictions = jsonList
                .map((json) => SentimentHistoryEntry.fromJson(json))
                .toList();

            debugPrint('Using cached predictions (${predictions.length} entries)');
            return predictions;
          } else {
            debugPrint('Cached predictions expired, will generate new ones');
          }
        }
      }

      debugPrint('No valid cached predictions found');
      return null;
    } catch (e) {
      debugPrint('Error getting cached predictions: $e');
      return null;
    }
  }

  /// Cache predictions for future use
  Future<void> _cachePredictions(List<SentimentHistoryEntry> predictions) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert predictions to JSON
      final jsonList = predictions.map((entry) => entry.toJson()).toList();

      // Save predictions and timestamp
      await prefs.setString(_predictionsKey, jsonEncode(jsonList));
      await prefs.setString(_predictionsTimestampKey, DateTime.now().toIso8601String());

      debugPrint('Cached ${predictions.length} predictions');
    } catch (e) {
      debugPrint('Error caching predictions: $e');
    }
  }
}
