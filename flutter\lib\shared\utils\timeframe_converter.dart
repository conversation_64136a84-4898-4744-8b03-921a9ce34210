import '../models/anti_fomo_candle.dart' as anti_fomo;
import '../models/trading_simulator_models.dart' as trading;

/// Helper class to convert between different TimeFrame types
class TimeFrameConverter {
  /// Convert from trading.TimeFrame to anti_fomo.TimeFrame
  static anti_fomo.TimeFrame fromTrading(trading.TimeFrame timeFrame) {
    switch (timeFrame) {
      case trading.TimeFrame.m30:
        return anti_fomo.TimeFrame.m30;
      case trading.TimeFrame.h1:
        return anti_fomo.TimeFrame.h1;
      case trading.TimeFrame.h4:
        return anti_fomo.TimeFrame.h4;
      case trading.TimeFrame.d1:
        return anti_fomo.TimeFrame.d1;
      default:
        return anti_fomo.TimeFrame.h1; // Default to 1h
    }
  }

  /// Convert from anti_fomo.TimeFrame to trading.TimeFrame
  static trading.TimeFrame toTrading(anti_fomo.TimeFrame timeFrame) {
    switch (timeFrame) {
      case anti_fomo.TimeFrame.m30:
        return trading.TimeFrame.m30;
      case anti_fomo.TimeFrame.h1:
        return trading.TimeFrame.h1;
      case anti_fomo.TimeFrame.h4:
        return trading.TimeFrame.h4;
      case anti_fomo.TimeFrame.d1:
        return trading.TimeFrame.d1;
      default:
        return trading.TimeFrame.h1; // Default to 1h
    }
  }
}
