# 🎛️ Руководство по тумблеру переключения режимов

## Обзор

Новый способ переключения между Full и Lite режимами прямо в приложении с помощью интерактивного тумблера!

## 🚀 Быстрый старт

### Запуск приложения с тумблером

**VS Code (F5):**
1. Нажмите `F5`
2. Выберите **"🎛️ TMM с тумблером (Рекомендуется)"**
3. Приложение запустится с встроенным переключателем

**Командная строка:**
```bash
flutter run lib/main_with_switcher.dart
```

## 🎯 Возможности тумблера

### 1. Плавающая кнопка (Debug режим)
- Появляется в правом верхнем углу
- Показывает текущий режим
- Быстрое переключение одним тапом

### 2. Встроенный переключатель
- В настройках приложения
- В боковом меню (drawer)
- В модальных окнах

### 3. Автоматическое сохранение
- Выбор пользователя сохраняется
- При следующем запуске загружается последний режим
- Работает через SharedPreferences

## 🎨 Виды переключателей

### Компактный тумблер
```dart
AppModeSwitcher()
```

### В боковом меню
```dart
AppModeSwitcher(showInDrawer: true)
```

### Плавающая кнопка
```dart
AppModeSwitcher(showAsFloatingButton: true)
```

### FAB с модальным окном
```dart
AppModeFAB()
```

## 🔧 Как это работает

### Архитектура
```
main_with_switcher.dart
├── AppModeProvider (State Management)
├── AppModeWrapper (UI Wrapper)
├── AppModeSwitcher (UI Component)
└── Conditional Rendering (Full/Lite)
```

### Провайдер состояния
- **AppModeProvider** управляет текущим режимом
- Сохраняет выбор в SharedPreferences
- Уведомляет виджеты об изменениях

### Условное отображение
- **AppModeBuilder** - виджет для условного рендеринга
- **AppModeMixin** - миксин для компонентов
- Автоматическое скрытие недоступных функций

## 📱 Пользовательский опыт

### Переключение режимов
1. **Тап на тумблер** → Диалог подтверждения
2. **Выбор режима** → Анимация перехода
3. **Автосохранение** → Запоминание выбора

### Визуальная обратная связь
- ✨ Плавные анимации переходов
- 📳 Тактильная обратная связь (вибрация)
- 🎨 Изменение цветовой схемы
- 🔄 Индикатор загрузки

### Информативность
- 📝 Описание каждого режима
- ⚠️ Предупреждения о недоступных функциях
- 💡 Подсказки по использованию

## 🛠️ Настройка и кастомизация

### Добавление в существующий экран
```dart
// В AppBar
AppBar(
  actions: [
    AppModeSwitcher(),
  ],
)

// В Drawer
Drawer(
  child: Column(
    children: [
      AppModeSwitcher(showInDrawer: true),
    ],
  ),
)

// Как FAB
floatingActionButton: AppModeFAB(),
```

### Условное отображение контента
```dart
// Показать только в Full режиме
AppModeBuilder(
  fullModeChild: AdvancedChart(),
  fallback: SimpleChart(),
)

// Показать только если функция доступна
AppModeBuilder(
  requiredFeature: 'trading_simulator',
  fullModeChild: TradingSimulator(),
  fallback: FeatureUnavailableWidget(),
)
```

### Проверка доступности функций
```dart
class MyWidget extends StatefulWidget with AppModeMixin {
  void _onAdvancedFeatureTap() {
    if (!isFeatureAvailable('advanced_charts')) {
      showFeatureUnavailableMessage('Расширенные чарты');
      return;
    }
    
    // Открыть расширенные чарты
  }
}
```

## 🎮 Режимы работы

### Full Mode (Полный режим)
- 🖥️ **Целевая платформа:** Desktop, Web
- 🎯 **Функции:** Все доступно
- 🎨 **UI:** Расширенный интерфейс
- 📊 **Чарты:** TradingView, расширенная аналитика
- 🎓 **Обучение:** Полные курсы и материалы

### Lite Mode (Лайт режим)
- 📱 **Целевая платформа:** Mobile
- 🎯 **Функции:** Базовые
- 🎨 **UI:** Упрощенный интерфейс
- 📊 **Чарты:** Простые графики
- 📰 **Контент:** Основные новости и цены

## 🔄 Автоматические переходы

### По размеру экрана (опционально)
```dart
// Автоматическое переключение на мобильных
if (MediaQuery.of(context).size.width < 768) {
  appMode.setModeTemporary(AppMode.lite);
}
```

### По платформе (опционально)
```dart
// Автоматический выбор по платформе
if (Platform.isAndroid || Platform.isIOS) {
  appMode.setModeTemporary(AppMode.lite);
}
```

## 🧪 Тестирование

### Быстрое переключение (Debug)
```dart
// Временное переключение без сохранения
appMode.toggleModeTemporary();
```

### Сброс настроек
```dart
// Сброс к режиму по умолчанию
appMode.resetToDefault();
```

## 📊 Аналитика использования

### Отслеживание переключений
```dart
// В AppModeProvider можно добавить аналитику
void switchMode(bool toLite) {
  // Отправить событие в аналитику
  Analytics.track('mode_switch', {
    'from': _currentMode.toString(),
    'to': toLite ? 'lite' : 'full',
  });
  
  // Переключить режим
  // ...
}
```

## 🎯 Преимущества тумблера

### Для разработчиков
✅ **Быстрое тестирование** обеих версий  
✅ **Один билд** для всех платформ  
✅ **Легкая отладка** переходов  
✅ **Автоматическое сохранение** настроек  

### Для пользователей
✅ **Гибкость выбора** интерфейса  
✅ **Адаптация** под устройство  
✅ **Плавные переходы** между режимами  
✅ **Интуитивное управление**  

### Для продукта
✅ **A/B тестирование** интерфейсов  
✅ **Постепенный rollout** функций  
✅ **Адаптивность** под аудиторию  
✅ **Единая кодовая база**  

## 🚀 Запуск в продакшене

### Скрытие тумблера
```dart
// Показывать только в debug режиме
if (kDebugMode)
  AppModeSwitcher(showAsFloatingButton: true),
```

### Принудительный режим
```dart
// Принудительно установить режим для продакшена
if (kReleaseMode) {
  appMode.setModeTemporary(AppMode.lite); // Для мобильных
}
```

## 🔧 Устранение проблем

### Тумблер не появляется
- Проверьте, что запущен debug режим
- Убедитесь, что AppModeProvider добавлен в providers
- Проверьте импорты виджетов

### Настройки не сохраняются
- Убедитесь, что shared_preferences добавлен в pubspec.yaml
- Проверьте права приложения на запись
- Выполните `flutter clean` и `flutter pub get`

### Анимации тормозят
- Используйте Profile режим для тестирования производительности
- Оптимизируйте переходы между экранами
- Рассмотрите использование const виджетов

## 🎉 Заключение

Тумблер переключения - это мощный инструмент для:
- 🔄 **Быстрого переключения** между режимами
- 🎯 **Тестирования** разных интерфейсов  
- 📱 **Адаптации** под устройства
- 🚀 **Ускорения разработки**

**Рекомендуемый способ запуска:**
```bash
# VS Code: F5 → "🎛️ TMM с тумблером (Рекомендуется)"
# CLI: flutter run lib/main_with_switcher.dart
```

Попробуйте прямо сейчас! 🎛️✨
