class CryptoCoin {
  final String id;
  final String symbol;
  final String name;
  final double price;
  final double priceChangePercentage24h;
  final List<double> sparklineData;
  final String imageUrl;
  final double marketCap;
  final double volume24h;
  final double circulatingSupply;
  final double? maxSupply;
  final int rank;

  CryptoCoin({
    required this.id,
    required this.symbol,
    required this.name,
    required this.price,
    required this.priceChangePercentage24h,
    required this.sparklineData,
    required this.imageUrl,
    required this.marketCap,
    required this.volume24h,
    required this.circulatingSupply,
    this.maxSupply,
    required this.rank,
  });

  bool get isPriceUp => priceChangePercentage24h >= 0;

  String get formattedPrice {
    if (price >= 1000) {
      return '\$${price.toStringAsFixed(2)}';
    } else if (price >= 1) {
      return '\$${price.toStringAsFixed(2)}';
    } else {
      return '\$${price.toStringAsFixed(price < 0.001 ? 6 : 4)}';
    }
  }

  String get formattedPriceChange {
    final sign = isPriceUp ? '+' : '';
    return '$sign${priceChangePercentage24h.toStringAsFixed(2)}%';
  }

  String get formattedMarketCap {
    if (marketCap >= 1000000000) {
      return '\$${(marketCap / 1000000000).toStringAsFixed(2)}B';
    } else if (marketCap >= 1000000) {
      return '\$${(marketCap / 1000000).toStringAsFixed(2)}M';
    } else {
      return '\$${marketCap.toStringAsFixed(2)}';
    }
  }

  String get formattedVolume {
    if (volume24h >= 1000000000) {
      return '\$${(volume24h / 1000000000).toStringAsFixed(2)}B';
    } else if (volume24h >= 1000000) {
      return '\$${(volume24h / 1000000).toStringAsFixed(2)}M';
    } else {
      return '\$${volume24h.toStringAsFixed(2)}';
    }
  }

  // Mock data for testing
  static List<CryptoCoin> getMockCoins() {
    return [
      CryptoCoin(
        id: 'bitcoin',
        symbol: 'BTC',
        name: 'Bitcoin',
        price: 62345.78,
        priceChangePercentage24h: 2.34,
        sparklineData: [
          61245.32, 61500.45, 61300.12, 61800.56, 62100.89, 
          61900.34, 62200.45, 62500.23, 62300.67, 62345.78
        ],
        imageUrl: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
        marketCap: 1210000000000,
        volume24h: 32500000000,
        circulatingSupply: 19400000,
        maxSupply: 21000000,
        rank: 1,
      ),
      CryptoCoin(
        id: 'ethereum',
        symbol: 'ETH',
        name: 'Ethereum',
        price: 3456.89,
        priceChangePercentage24h: 1.23,
        sparklineData: [
          3400.12, 3420.45, 3410.67, 3430.23, 3450.78, 
          3440.34, 3460.56, 3470.89, 3450.23, 3456.89
        ],
        imageUrl: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
        marketCap: 415000000000,
        volume24h: 18700000000,
        circulatingSupply: 120300000,
        maxSupply: null,
        rank: 2,
      ),
      CryptoCoin(
        id: 'solana',
        symbol: 'SOL',
        name: 'Solana',
        price: 145.67,
        priceChangePercentage24h: 5.67,
        sparklineData: [
          138.45, 139.23, 140.56, 142.78, 143.90, 
          142.34, 143.56, 144.78, 145.23, 145.67
        ],
        imageUrl: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
        marketCap: 63500000000,
        volume24h: 2800000000,
        circulatingSupply: 436000000,
        maxSupply: null,
        rank: 5,
      ),
      CryptoCoin(
        id: 'binancecoin',
        symbol: 'BNB',
        name: 'BNB',
        price: 567.89,
        priceChangePercentage24h: -0.45,
        sparklineData: [
          570.23, 569.45, 568.78, 567.90, 566.45, 
          567.23, 568.45, 567.89, 566.78, 567.89
        ],
        imageUrl: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
        marketCap: 87600000000,
        volume24h: 1500000000,
        circulatingSupply: 154500000,
        maxSupply: 200000000,
        rank: 4,
      ),
      CryptoCoin(
        id: 'ripple',
        symbol: 'XRP',
        name: 'XRP',
        price: 0.57,
        priceChangePercentage24h: 0.89,
        sparklineData: [
          0.56, 0.565, 0.563, 0.568, 0.57, 
          0.569, 0.571, 0.573, 0.571, 0.57
        ],
        imageUrl: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png',
        marketCap: 31200000000,
        volume24h: 1200000000,
        circulatingSupply: 54700000000,
        maxSupply: 100000000000,
        rank: 6,
      ),
      CryptoCoin(
        id: 'dogecoin',
        symbol: 'DOGE',
        name: 'Dogecoin',
        price: 0.12,
        priceChangePercentage24h: 1.45,
        sparklineData: [
          0.118, 0.119, 0.1195, 0.12, 0.121, 
          0.1205, 0.121, 0.1215, 0.121, 0.12
        ],
        imageUrl: 'https://assets.coingecko.com/coins/images/5/large/dogecoin.png',
        marketCap: 16800000000,
        volume24h: 650000000,
        circulatingSupply: 140000000000,
        maxSupply: null,
        rank: 9,
      ),
    ];
  }
}
