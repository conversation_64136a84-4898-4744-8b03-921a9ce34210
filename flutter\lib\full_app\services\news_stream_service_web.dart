import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/news_item.dart';
import '../models/sentiment_data.dart';
import '../models/sentiment_types.dart';

// Условный импорт для Web
import 'dart:html' as html show EventSource, MessageEvent;

/// Типы событий из потока новостей
enum NewsStreamEventType {
  newNews,
  connected,
  disconnected,
  error,
}

/// Web-специфичная реализация SSE для Flutter Web
class NewsStreamServiceWeb {
  static const String _backendBaseUrl = kDebugMode
    ? 'http://localhost:4000'
    : 'https://your-domain.com/api';
  
  StreamController<NewsStreamEvent>? _streamController;
  html.EventSource? _eventSource;
  bool _isConnected = false;
  bool _isConnecting = false;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 3);
  
  Stream<NewsStreamEvent> get newsStream {
    _streamController ??= StreamController<NewsStreamEvent>.broadcast();
    return _streamController!.stream;
  }
  
  bool get isConnected => _isConnected;
  bool get isConnecting => _isConnecting;

  /// Подключение к потоку новостей
  Future<void> connect() async {
    if (_isConnected || _isConnecting) {
      debugPrint('[NewsStreamWeb] Уже подключен или подключается к потоку');
      return;
    }
    
    _isConnecting = true;
    _cancelReconnectTimer();
    
    try {
      final url = '$_backendBaseUrl/news/stream';
      debugPrint('[NewsStreamWeb] Подключение к $url');
      
      // Закрываем предыдущее соединение если есть
      _eventSource?.close();
      
      // Создаем новое SSE соединение
      _eventSource = html.EventSource(url);
      
      _streamController ??= StreamController<NewsStreamEvent>.broadcast();
      
      // Обработчик открытия соединения
      _eventSource!.onOpen.listen((event) {
        debugPrint('[NewsStreamWeb] SSE соединение открыто');
        _isConnected = true;
        _isConnecting = false;
        _reconnectAttempts = 0;
      });
      
      // Обработчик сообщений
      _eventSource!.onMessage.listen((html.MessageEvent event) {
        debugPrint('[NewsStreamWeb] Получено SSE сообщение: ${event.data}');
        _handleStreamData(event.data.toString());
      });
      
      // Обработчик ошибок
      _eventSource!.onError.listen((event) {
        debugPrint('[NewsStreamWeb] Ошибка SSE соединения');
        _handleStreamError('SSE connection error');
      });
      
    } catch (e) {
      debugPrint('[NewsStreamWeb] Ошибка подключения: $e');
      _isConnected = false;
      _isConnecting = false;
      _streamController?.addError(e);
      _scheduleReconnect();
    }
  }

  /// Отключение от потока
  void disconnect() {
    debugPrint('[NewsStreamWeb] Отключение от потока новостей');
    
    _cancelReconnectTimer();
    _eventSource?.close();
    _eventSource = null;
    _isConnected = false;
    _isConnecting = false;
  }
  
  /// Отмена таймера переподключения
  void _cancelReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }
  
  /// Планирование переподключения
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint('[NewsStreamWeb] Превышено максимальное количество попыток переподключения');
      return;
    }
    
    _reconnectAttempts++;
    final delay = Duration(seconds: _reconnectDelay.inSeconds * _reconnectAttempts);
    
    debugPrint('[NewsStreamWeb] Планируем переподключение через ${delay.inSeconds} секунд (попытка $_reconnectAttempts/$_maxReconnectAttempts)');
    
    _reconnectTimer = Timer(delay, () {
      if (!_isConnected && !_isConnecting) {
        debugPrint('[NewsStreamWeb] Автоматическое переподключение...');
        connect();
      }
    });
  }

  /// Обработка данных из потока
  void _handleStreamData(String data) {
    try {
      final jsonData = json.decode(data);
      final event = NewsStreamEvent.fromJson(jsonData);
      _streamController?.add(event);
      debugPrint('[NewsStreamWeb] Обработано событие: ${event.type}');
    } catch (e) {
      debugPrint('[NewsStreamWeb] Ошибка парсинга данных: $e');
    }
  }
  
  /// Обработка ошибок потока
  void _handleStreamError(String error) {
    debugPrint('[NewsStreamWeb] Ошибка потока: $error');
    _isConnected = false;
    _isConnecting = false;
    _streamController?.addError(error);
    _scheduleReconnect();
  }

  /// Закрытие сервиса
  void dispose() {
    debugPrint('[NewsStreamWeb] Закрытие сервиса');
    disconnect();
    _streamController?.close();
    _streamController = null;
  }
  
  /// Принудительное переподключение
  Future<void> reconnect() async {
    debugPrint('[NewsStreamWeb] Принудительное переподключение');
    disconnect();
    await Future.delayed(const Duration(milliseconds: 500));
    await connect();
  }
}

/// Событие из потока новостей
class NewsStreamEvent {
  final NewsStreamEventType type;
  final NewsItem? news;
  final String? message;
  final DateTime timestamp;
  final int? totalCount;
  
  NewsStreamEvent({
    required this.type,
    this.news,
    this.message,
    required this.timestamp,
    this.totalCount,
  });
  
  factory NewsStreamEvent.fromJson(Map<String, dynamic> json) {
    return NewsStreamEvent(
      type: _parseEventType(json['type']),
      news: json['news'] != null ? _parseNewsItem(json['news']) : null,
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      totalCount: json['totalCount'],
    );
  }

  static NewsStreamEventType _parseEventType(String type) {
    switch (type.toLowerCase()) {
      case 'news-added':
      case 'new_news':
        return NewsStreamEventType.newNews;
      case 'connected':
        return NewsStreamEventType.connected;
      case 'disconnected':
        return NewsStreamEventType.disconnected;
      case 'error':
        return NewsStreamEventType.error;
      default:
        debugPrint('[NewsStreamWeb] Неизвестный тип события: $type');
        return NewsStreamEventType.newNews;
    }
  }
  
  static NewsItem _parseNewsItem(Map<String, dynamic> json) {
    return NewsItem(
      id: json['id'] ?? '',
      title: json['aiGeneratedTitle'] ?? json['title'] ?? '',
      description: json['originalDescription'] ?? json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? json['urlToImage'] ?? 'https://via.placeholder.com/300x200?text=No+Image',
      publishedAt: DateTime.parse(json['publishedAt'] ?? DateTime.now().toIso8601String()),
      source: json['source'] ?? '',
      url: json['url'] ?? '',
      sentiment: _parseSentimentType(json['sentiment']),
      tags: List<String>.from(json['tags'] ?? []),
      category: _parseNewsCategory(json['tags'] ?? []),
      content: json['rewrittenContent'] ?? json['content'] ?? json['summary'] ?? '',
      summary: json['summary'],
      sentimentData: json['sentimentData'] != null 
          ? SentimentData.fromJson(json['sentimentData'])
          : null,
      fetchedAt: DateTime.now(),
    );
  }
  
  static SentimentType _parseSentimentType(dynamic sentiment) {
    if (sentiment == null) return SentimentType.neutral;
    
    final sentimentStr = sentiment.toString().toLowerCase();
    switch (sentimentStr) {
      case 'positive':
        return SentimentType.positive;
      case 'negative':
        return SentimentType.negative;
      default:
        return SentimentType.neutral;
    }
  }
  
  static NewsCategory _parseNewsCategory(List<dynamic> tags) {
    final tagsLower = tags.map((tag) => tag.toString().toLowerCase()).toList();
    
    if (tagsLower.any((tag) => ['bitcoin', 'ethereum', 'crypto', 'blockchain', 'btc', 'eth'].contains(tag))) {
      return NewsCategory.crypto;
    } else if (tagsLower.any((tag) => ['stocks', 'market', 'nasdaq', 'dow', 'finance'].contains(tag))) {
      return NewsCategory.stocks;
    } else if (tagsLower.any((tag) => ['ai', 'artificial intelligence', 'machine learning'].contains(tag))) {
      return NewsCategory.ai;
    } else if (tagsLower.any((tag) => ['politics', 'government', 'regulation'].contains(tag))) {
      return NewsCategory.politics;
    } else if (tagsLower.any((tag) => ['whale', 'institutional'].contains(tag))) {
      return NewsCategory.whales;
    }
    
    return NewsCategory.all;
  }
}
