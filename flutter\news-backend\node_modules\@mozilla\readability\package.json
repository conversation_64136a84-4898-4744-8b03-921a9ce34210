{"name": "@mozilla/readability", "version": "0.6.0", "description": "A standalone version of the readability library used for Firefox Reader View.", "main": "index.js", "types": "index.d.ts", "scripts": {"lint": "eslint . && prettier Readability.js JSDOMParser.js test/*.js --check", "test": "mocha test/test-*.js", "generate-testcase": "node test/generate-testcase.js", "release": "release-it"}, "repository": {"type": "git", "url": "https://github.com/mozilla/readability"}, "author": "", "license": "Apache-2.0", "bugs": {"url": "https://github.com/mozilla/readability/issues"}, "engines": {"node": ">=14.0.0"}, "homepage": "https://github.com/mozilla/readability", "devDependencies": {"@release-it/keep-a-changelog": "5.0.0", "chai": "4.3.7", "eslint": "8.57.0", "eslint-plugin-mozilla": "^3.7.4", "eslint-plugin-no-unsanitized": "^4.0.2", "htmltidy2": "1.0.0", "js-beautify": "1.14.7", "jsdom": "20.0.2", "mocha": "10.8.2", "prettier": "^3.3.2", "release-it": "17.0.1", "sinon": "14.0.2", "xml-name-validator": "^5.0.0"}}