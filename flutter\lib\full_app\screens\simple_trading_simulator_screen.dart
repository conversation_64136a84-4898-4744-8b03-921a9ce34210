import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import '../models/trading_simulator_model.dart';
import '../models/candle.dart';

/// Простой экран симулятора криптотрейдинга с полностью переработанной логикой
class SimpleTradingSimulatorScreen extends StatefulWidget {
  const SimpleTradingSimulatorScreen({Key? key}) : super(key: key);

  @override
  State<SimpleTradingSimulatorScreen> createState() => _SimpleTradingSimulatorScreenState();
}

class _SimpleTradingSimulatorScreenState extends State<SimpleTradingSimulatorScreen> {
  WebViewController? _controller;
  bool _isWebViewLoaded = false;
  bool _entryPointSet = false;
  bool _showingAllCandles = false;

  @override
  void initState() {
    super.initState();

    // Инициализируем контроллер
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            setState(() {
              _isWebViewLoaded = true;
            });
            _loadInitialCandles();
          },
        ),
      )
      ..addJavaScriptChannel(
        'FlutterChannel',
        onMessageReceived: _handleJavaScriptMessage,
      )
      ..loadFlutterAsset('assets/tradingview_chart_fixed.html');
  }

  // Загрузка начальных свечей
  void _loadInitialCandles() {
    final model = Provider.of<TradingSimulatorModel>(context, listen: false);
    if (model.initialCandles.isEmpty) return;

    final candles = model.getAllCandles();
    _loadCandles(candles);
  }

  // Загрузка свечей на график
  void _loadCandles(List<Candle> candles) {
    if (!_isWebViewLoaded || _controller == null) return;

    try {
      final List<Map<String, dynamic>> candlesJson = candles.map((candle) => {
        'time': candle.time,
        'open': candle.open,
        'high': candle.high,
        'low': candle.low,
        'close': candle.close,
        'volume': candle.volume,
      }).toList();

      final String candlesString = jsonEncode(candlesJson);

      final String script = '''
        const candles = $candlesString;
        loadCandles(candles);
      ''';

      _controller!.runJavaScript(script);

      // Сбрасываем флаги
      setState(() {
        _entryPointSet = false;
        _showingAllCandles = false;
      });
    } catch (e) {
      debugPrint('Error loading candles: $e');
    }
  }

  // Показать все свечи
  void _showAllCandles() {
    if (!_isWebViewLoaded || _controller == null) return;

    try {
      _controller!.runJavaScript('showAllCandles()');
      setState(() {
        _showingAllCandles = true;
      });
    } catch (e) {
      debugPrint('Error showing all candles: $e');
    }
  }

  // Установка точки входа
  void _setEntryPoint() {
    if (!_isWebViewLoaded || _controller == null) return;

    try {
      _controller!.runJavaScript('setEntryPoint()');
      setState(() {
        _entryPointSet = true;
      });
    } catch (e) {
      debugPrint('Error setting entry point: $e');
    }
  }

  // Определение результата
  void _determineResult() {
    if (!_isWebViewLoaded || _controller == null) return;

    try {
      _controller!.runJavaScript('determineResult()');
    } catch (e) {
      debugPrint('Error determining result: $e');
    }
  }

  // Очистка элементов графика
  void _clearChartElements() {
    if (!_isWebViewLoaded || _controller == null) return;

    try {
      _controller!.runJavaScript('clearChartElements()');
      setState(() {
        _entryPointSet = false;
        _showingAllCandles = false;
      });
    } catch (e) {
      debugPrint('Error clearing chart elements: $e');
    }
  }

  // Обработка сообщений от JavaScript
  void _handleJavaScriptMessage(JavaScriptMessage message) {
    try {
      final Map<String, dynamic> data = jsonDecode(message.message);
      final String action = data['action'];
      final List<dynamic> messageData = data['data'] ?? [];

      final model = Provider.of<TradingSimulatorModel>(context, listen: false);

      switch (action) {
        case 'chartReady':
          _loadInitialCandles();
          break;
        case 'entryPointSet':
          if (messageData.length >= 2) {
            model.setEntryPoint(
              messageData[0], // price
              messageData[1], // time
            );
          }
          break;
        case 'tradeResult':
          if (messageData.length >= 3) {
            model.processTradeResult(
              messageData[0], // isUp
              messageData[1], // percentChange
              messageData[2], // finalPrice
            );
          }
          break;
        default:
          debugPrint('Unknown action from JavaScript: $action');
      }
    } catch (e) {
      debugPrint('Error handling JavaScript message: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final model = Provider.of<TradingSimulatorModel>(context);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF000428), Color(0xFF004e92)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Верхняя панель с информацией
              _buildInfoPanel(model),

              // График
              Expanded(
                child: !_isWebViewLoaded || model.initialCandles.isEmpty
                    ? const Center(
                        child: CircularProgressIndicator(),
                      )
                    : WebViewWidget(controller: _controller!),
              ),

              // Панель управления
              _buildControlPanel(model),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoPanel(TradingSimulatorModel model) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(77),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withAlpha(51),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Кнопка назад
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () {
              Navigator.pop(context);
            },
          ),

          // Результат сделки и прибыль/убыток
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (model.trades.isNotEmpty)
                Text(
                  model.trades.last.success ? 'УСПЕШНАЯ СДЕЛКА' : 'НЕУДАЧНАЯ СДЕЛКА',
                  style: TextStyle(
                    color: model.trades.isNotEmpty && model.trades.last.success
                        ? Colors.green
                        : Colors.red,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                )
              else
                const Text(
                  'ОЖИДАНИЕ СДЕЛКИ',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              if (model.trades.isNotEmpty)
                Text(
                  'Прибыль: ${model.trades.last.profit > 0 ? '+' : ''}${model.trades.last.profit.toStringAsFixed(2)}',
                  style: TextStyle(
                    color: model.trades.isNotEmpty && model.trades.last.profit > 0
                        ? Colors.green
                        : Colors.red,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                )
              else
                Text(
                  '${model.currentSymbol} (${model.timeframe})',
                  style: TextStyle(
                    color: Colors.grey[300],
                    fontSize: 14,
                  ),
                ),
            ],
          ),

          // Статистика
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Balance: \$${model.balance.toStringAsFixed(2)}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Раунды: ${model.totalPredictions} | Победы: ${model.correctPredictions}',
                style: TextStyle(
                  color: Colors.grey[300],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlPanel(TradingSimulatorModel model) {
    // Если ожидаем следующий уровень, показываем кнопку "Next Pattern"
    if (model.readyForNextLevel) {
      return Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(77),
          border: Border(
            top: BorderSide(
              color: Colors.grey.withAlpha(51),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () {
                // Очищаем элементы графика
                _clearChartElements();

                // Загружаем следующий уровень
                model.goToNextLevel();

                // Загружаем новые свечи
                _loadCandles(model.getAllCandles());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Next Pattern',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Если нет активной сделки, показываем кнопки UP/DOWN
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(77),
        border: Border(
          top: BorderSide(
            color: Colors.grey.withAlpha(51),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Кнопка UP
          ElevatedButton(
            onPressed: model.gameStatus == 'playing' && !model.readyForNextLevel
                ? () {
                    // Устанавливаем точку входа только при нажатии кнопки
                    _setEntryPoint();

                    // Показываем все свечи
                    _showAllCandles();

                    // Делаем сделку
                    model.makeTrade('buy');

                    // Определяем результат
                    _determineResult();
                  }
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'UP',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Кнопка DOWN
          ElevatedButton(
            onPressed: model.gameStatus == 'playing' && !model.readyForNextLevel
                ? () {
                    // Устанавливаем точку входа только при нажатии кнопки
                    _setEntryPoint();

                    // Показываем все свечи
                    _showAllCandles();

                    // Делаем сделку
                    model.makeTrade('sell');

                    // Определяем результат
                    _determineResult();
                  }
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'DOWN',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
