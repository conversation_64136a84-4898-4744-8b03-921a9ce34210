import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/crypto_currency.dart';

/// Виджет для отображения мини-графика криптовалюты
/// Специально оптимизирован для небольших размеров и стабильной работы
class MiniChart extends StatefulWidget {
  final CryptoCurrency crypto;
  final Color color;
  final double strokeWidth;
  final bool showArea;
  final double? height;
  final double? width;

  const MiniChart({
    Key? key,
    required this.crypto,
    required this.color,
    this.strokeWidth = 2.0,
    this.showArea = true,
    this.height,
    this.width,
  }) : super(key: key);

  @override
  State<MiniChart> createState() => _MiniChartState();
}

class _MiniChartState extends State<MiniChart> {
  // Кэшируем данные графика, чтобы они не менялись при каждом обновлении
  List<double>? _cachedPrices;
  String? _cachedSymbol;
  double? _cachedPriceChange;

  @override
  Widget build(BuildContext context) {
    // Проверяем, нужно ли обновить кэшированные данные
    if (_shouldRegenerateData()) {
      _cachedPrices = _generatePriceData();
      _cachedSymbol = widget.crypto.symbol;
      _cachedPriceChange = widget.crypto.priceChangePercentage24h;
    }

    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: CustomPaint(
        size: Size.infinite,
        painter: _MiniChartPainter(
          prices: _cachedPrices!,
          color: widget.color,
          strokeWidth: widget.strokeWidth,
          showArea: widget.showArea,
        ),
      ),
    );
  }

  // Время последнего обновления данных графика
  DateTime? _lastUpdateTime;

  // Проверяем, нужно ли регенерировать данные
  bool _shouldRegenerateData() {
    final DateTime now = DateTime.now();

    // Если данные еще не были сгенерированы
    if (_cachedPrices == null || _cachedSymbol == null || _cachedPriceChange == null || _lastUpdateTime == null) {
      _lastUpdateTime = now;
      return true;
    }

    // Если изменился символ криптовалюты
    if (_cachedSymbol != widget.crypto.symbol) {
      _lastUpdateTime = now;
      return true;
    }

    // Обновляем данные только раз в 15 минут
    if (now.difference(_lastUpdateTime!).inMinutes >= 15) {
      // Если процентное изменение значительно изменилось (более чем на 1%)
      if ((_cachedPriceChange! - widget.crypto.priceChangePercentage24h).abs() > 1.0) {
        _lastUpdateTime = now;
        return true;
      }
    }

    // В остальных случаях используем кэшированные данные
    return false;
  }

  /// Генерирует данные для графика с таймфреймом 1 час
  /// При этом процентное изменение основано на суточном изменении (24 часа)
  List<double> _generatePriceData() {
    // Базовые параметры
    final double currentPrice = widget.crypto.price;

    // Используем суточное процентное изменение для определения тренда
    final double dailyPriceChange = widget.crypto.priceChangePercentage24h / 100; // Конвертируем в десятичное число
    final bool isPositive = dailyPriceChange >= 0;

    // Для часового графика используем меньшее изменение
    // Строго используем только часовой таймфрейм, как требуется
    // Но сохраняем направление тренда
    final double hourlyPriceChangeFactor = dailyPriceChange / 6; // Делим на 6 для более выраженного часового графика

    // Количество точек для графика (оптимальное для часового таймфрейма)
    const int numPoints = 24; // 24 точки для часового графика (по точке на каждые 2.5 минуты)

    // Создаем список для хранения цен
    final List<double> prices = [];

    // Создаем стабильный генератор случайных чисел на основе символа криптовалюты и текущей цены
    // Добавляем хэш текущей цены, округленной до целого числа, чтобы график был стабильным
    final int priceHash = (currentPrice * 100).round();
    final random = math.Random(widget.crypto.symbol.hashCode + priceHash);

    // Вычисляем начальную цену (цена час назад)
    final double startPrice = currentPrice / (1 + hourlyPriceChangeFactor);

    // Для более выраженного графика
    final double volatilityFactor = math.min(0.01, math.max(0.002, dailyPriceChange.abs() * 0.1));

    // Генерируем точки для часового графика
    for (int i = 0; i < numPoints; i++) {
      // Нормализованная позиция от 0 до 1
      final double t = i / (numPoints - 1);

      // Базовая цена с нелинейной интерполяцией для более реалистичного тренда
      double price;

      if (isPositive) {
        // Для положительного тренда: плавная кривая вверх
        // Используем кубическую функцию для S-образной кривой
        final double curve = t < 0.5 ? 4 * t * t * t : 1 - math.pow(-2 * t + 2, 3) / 2;
        price = startPrice + (currentPrice - startPrice) * curve;
      } else {
        // Для отрицательного тренда: плавная кривая вниз
        // Используем обратную кубическую функцию
        final double curve = 1 - (t < 0.5 ? 4 * t * t * t : 1 - math.pow(-2 * t + 2, 3) / 2);
        price = startPrice - (startPrice - currentPrice) * curve;
      }

      // Добавляем реалистичные колебания (волатильность)
      // Колебания больше в середине графика и меньше по краям
      final double volatility = volatilityFactor * currentPrice * 4 * t * (1 - t);
      final double noise = (random.nextDouble() - 0.5) * volatility;
      price += noise;

      // Добавляем цену в список
      prices.add(price);
    }

    // Убеждаемся, что последняя цена точно равна текущей цене
    if (prices.isNotEmpty) {
      prices[prices.length - 1] = currentPrice;
    }

    return prices;
  }
}

/// Painter для отрисовки мини-графика
class _MiniChartPainter extends CustomPainter {
  final List<double> prices;
  final Color color;
  final double strokeWidth;
  final bool showArea;

  _MiniChartPainter({
    required this.prices,
    required this.color,
    required this.strokeWidth,
    required this.showArea,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (prices.isEmpty) return;

    final double width = size.width;
    final double height = size.height;

    // Находим минимальное и максимальное значения
    double minValue = prices.reduce((a, b) => a < b ? a : b);
    double maxValue = prices.reduce((a, b) => a > b ? a : b);

    // Добавляем отступы для более выраженного графика
    final double range = maxValue - minValue;
    final double padding = range * 0.2;
    minValue -= padding;
    maxValue += padding;

    // Проверяем, что есть диапазон для работы
    if (maxValue - minValue < 0.000001) {
      maxValue = minValue + 1;
    }

    // Вычисляем масштабные коэффициенты
    final double xScale = width / (prices.length - 1);
    final double yScale = height / (maxValue - minValue);

    // Создаем путь для линии
    final Path linePath = Path();

    // Перемещаемся к первой точке
    final double firstY = height - (prices[0] - minValue) * yScale;
    linePath.moveTo(0, firstY);

    // Соединяем точки плавной кривой
    if (prices.length > 2) {
      // Создаем точки для кривой
      final List<Offset> points = [];
      for (int i = 0; i < prices.length; i++) {
        final double x = i * xScale;
        final double y = height - (prices[i] - minValue) * yScale;
        points.add(Offset(x, y));
      }

      // Используем кривую Безье для плавного соединения точек
      for (int i = 0; i < points.length - 1; i++) {
        final Offset p0 = i > 0 ? points[i - 1] : points[i];
        final Offset p1 = points[i];
        final Offset p2 = points[i + 1];
        final Offset p3 = i < points.length - 2 ? points[i + 2] : p2;

        // Вычисляем контрольные точки для кривой Безье
        // Используем метод Catmull-Rom для более плавной кривой
        final double tension = 0.4; // Параметр натяжения (0.0-1.0)

        final double controlX1 = p1.dx + (p2.dx - p0.dx) * tension / 3;
        final double controlY1 = p1.dy + (p2.dy - p0.dy) * tension / 3;
        final double controlX2 = p2.dx - (p3.dx - p1.dx) * tension / 3;
        final double controlY2 = p2.dy - (p3.dy - p1.dy) * tension / 3;

        // Добавляем кубическую кривую Безье для более плавного графика
        linePath.cubicTo(controlX1, controlY1, controlX2, controlY2, p2.dx, p2.dy);
      }
    } else {
      // Если точек мало, просто соединяем их линиями
      for (int i = 1; i < prices.length; i++) {
        final double x = i * xScale;
        final double y = height - (prices[i] - minValue) * yScale;
        linePath.lineTo(x, y);
      }
    }

    // Создаем кисть для линии
    final Paint linePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;

    // Рисуем линию
    canvas.drawPath(linePath, linePaint);

    // Создаем и рисуем область под графиком, если нужно
    if (showArea) {
      final Path areaPath = Path.from(linePath);

      // Замыкаем путь, чтобы создать закрытую фигуру
      areaPath.lineTo(width, height);
      areaPath.lineTo(0, height);
      areaPath.close();

      // Создаем кисть для области с улучшенным градиентом
      final Paint areaPaint = Paint()
        ..shader = LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            color.withAlpha(100),  // Верхний цвет (более насыщенный)
            color.withAlpha(40),   // Средний цвет
            color.withAlpha(5),    // Нижний цвет (почти прозрачный)
          ],
          stops: const [0.0, 0.3, 0.9],
        ).createShader(Rect.fromLTWH(0, 0, width, height))
        ..style = PaintingStyle.fill;

      // Рисуем область
      canvas.drawPath(areaPath, areaPaint);
    }
  }

  @override
  bool shouldRepaint(_MiniChartPainter oldDelegate) {
    return oldDelegate.prices != prices ||
        oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.showArea != showArea;
  }
}
