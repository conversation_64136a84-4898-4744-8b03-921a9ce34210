# 🚀 Руководство по запуску через F5 в VS Code

## Быстрый старт

1. **Откройте VS Code** в папке `flutter/`
2. **Нажмите F5** или `Ctrl+Shift+D`
3. **Выберите конфигурацию** из выпадающего списка
4. **Нажмите зеленую кнопку** или снова F5

## Доступные конфигурации

### 🎯 Основные конфигурации

| Иконка | Название | Описание |
|--------|----------|----------|
| 🚀 | **TMM Full App (Debug)** | Полная версия в режиме отладки |
| 📱 | **TMM Lite App (Debug)** | Лайт версия в режиме отладки |

### 🌐 Платформо-специфичные

| Иконка | Название | Описание |
|--------|----------|----------|
| 🌐 | **TMM Full (Web - Chrome)** | Полная версия в браузере Chrome |
| 📲 | **TMM Lite (Android Emulator)** | Лайт версия на Android эмуляторе |
| 🍎 | **TMM Lite (iOS Simulator)** | Лайт версия на iOS симуляторе |

### ⚡ Производительность

| Иконка | Название | Описание |
|--------|----------|----------|
| ⚡ | **TMM Full (Profile Mode)** | Полная версия для профилирования |
| ⚡ | **TMM Lite (Profile Mode)** | Лайт версия для профилирования |

### 🔧 Экспериментальные

| Иконка | Название | Описание |
|--------|----------|----------|
| 🔧 | **TMM Unified (Full)** | Полная версия через unified точку входа |
| 🔧 | **TMM Unified (Lite)** | Лайт версия через unified точку входа |

## Пошаговая инструкция

### Способ 1: Через панель отладки

1. **Откройте панель отладки:**
   - Нажмите `Ctrl+Shift+D` (Windows/Linux)
   - Нажмите `Cmd+Shift+D` (macOS)
   - Или кликните на иконку "жука" в боковой панели

2. **Выберите конфигурацию:**
   - В выпадающем списке выберите нужную конфигурацию
   - Например: "🚀 TMM Full App (Debug)"

3. **Запустите:**
   - Нажмите зеленую кнопку "▶️"
   - Или нажмите `F5`

### Способ 2: Прямо через F5

1. **Нажмите F5**
2. **Если это первый запуск:**
   - VS Code покажет список конфигураций
   - Выберите нужную
3. **При повторных запусках:**
   - F5 запустит последнюю выбранную конфигурацию

### Способ 3: Через Command Palette

1. **Откройте Command Palette:**
   - `Ctrl+Shift+P` (Windows/Linux)
   - `Cmd+Shift+P` (macOS)

2. **Введите:** `Debug: Select and Start Debugging`

3. **Выберите конфигурацию** из списка

## Горячие клавиши во время отладки

| Клавиша | Действие |
|---------|----------|
| `F5` | Продолжить выполнение |
| `F10` | Шаг через (Step Over) |
| `F11` | Шаг в (Step Into) |
| `Shift+F11` | Шаг из (Step Out) |
| `Ctrl+Shift+F5` | Перезапустить |
| `Shift+F5` | Остановить отладку |
| `F9` | Переключить точку останова |

## Hot Reload во время разработки

Когда приложение запущено через F5:

| Клавиша | Действие |
|---------|----------|
| `Ctrl+F5` | Hot Reload (быстрая перезагрузка) |
| `Ctrl+Shift+F5` | Hot Restart (полный перезапуск) |
| `r` | Hot Reload (в терминале) |
| `R` | Hot Restart (в терминале) |
| `q` | Выход (в терминале) |

## Настройка устройств

### Автоматический выбор устройства

VS Code автоматически выберет доступное устройство для каждой конфигурации:

- **Web конфигурации** → Chrome
- **Android конфигурации** → Первый доступный Android эмулятор
- **iOS конфигурации** → Первый доступный iOS симулятор

### Ручной выбор устройства

1. **Откройте Command Palette** (`Ctrl+Shift+P`)
2. **Введите:** `Flutter: Select Device`
3. **Выберите устройство** из списка
4. **Запустите конфигурацию** через F5

### Проверка доступных устройств

1. **Откройте терминал** в VS Code
2. **Выполните:** `flutter devices`
3. **Или используйте задачу:** `Ctrl+Shift+P` → `Tasks: Run Task` → `📱 List Devices`

## Дополнительные задачи

Помимо F5, доступны дополнительные задачи через `Ctrl+Shift+P` → `Tasks: Run Task`:

| Задача | Описание |
|--------|----------|
| 🧹 Flutter Clean | Очистка проекта |
| 📦 Flutter Pub Get | Установка зависимостей |
| 🔨 Build Full APK | Сборка полной версии APK |
| 📱 Build Lite APK | Сборка лайт версии APK |
| 🌐 Build Web | Сборка для веб |
| 🧪 Run Tests | Запуск тестов |
| 🔍 Flutter Analyze | Анализ кода |
| ✨ Format Code | Форматирование кода |
| 📋 Flutter Doctor | Проверка настроек Flutter |

## Устранение проблем

### Проблема: "No configuration found"

**Решение:**
1. Убедитесь, что вы открыли папку `flutter/` в VS Code
2. Проверьте, что файл `.vscode/launch.json` существует
3. Перезапустите VS Code

### Проблема: "No devices found"

**Решение:**
1. Запустите `flutter doctor` в терминале
2. Убедитесь, что эмулятор/симулятор запущен
3. Для веб: убедитесь, что Chrome установлен

### Проблема: "Build failed"

**Решение:**
1. Выполните `flutter clean`
2. Выполните `flutter pub get`
3. Проверьте ошибки в терминале

### Проблема: Hot Reload не работает

**Решение:**
1. Убедитесь, что приложение запущено в Debug режиме
2. Попробуйте Hot Restart (`Ctrl+Shift+F5`)
3. Проверьте, что файлы сохранены

## Рекомендуемые расширения

VS Code автоматически предложит установить рекомендуемые расширения:

- **Dart** - Поддержка языка Dart
- **Flutter** - Поддержка Flutter фреймворка
- **Error Lens** - Показ ошибок прямо в коде
- **Todo Tree** - Управление TODO комментариями
- **Material Icon Theme** - Красивые иконки файлов

## Советы по эффективности

### 🎯 Для ежедневной разработки
- Используйте **🚀 TMM Full App (Debug)** для desktop функций
- Используйте **📱 TMM Lite App (Debug)** для мобильных функций

### 🌐 Для веб-разработки
- Используйте **🌐 TMM Full (Web - Chrome)** 
- Включите DevTools в браузере для отладки

### 📱 Для мобильной разработки
- Используйте **📲 TMM Lite (Android Emulator)** или **🍎 TMM Lite (iOS Simulator)**
- Тестируйте на реальных устройствах для финальной проверки

### ⚡ Для оптимизации производительности
- Используйте **Profile Mode** конфигурации
- Анализируйте производительность через Flutter Inspector

## Заключение

Запуск через F5 в VS Code - это самый удобный способ для ежедневной разработки:

✅ **Быстро** - один клик для запуска  
✅ **Удобно** - встроенная отладка  
✅ **Гибко** - множество конфигураций  
✅ **Эффективно** - Hot Reload из коробки  

Просто нажмите **F5** и начинайте разработку! 🚀
