import 'package:flutter/material.dart';
import '../models/news_item.dart';
import '../models/sentiment_types.dart';
import '../styles/news_card_colors.dart';
// 🚫 Убрали import 'news_freshness_indicator.dart' - больше не используется

class NewsCard extends StatelessWidget {
  final NewsItem news;
  final VoidCallback onTap;

  const NewsCard({
    Key? key,
    required this.news,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: NewsCardColors.cardBackground,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: NewsCardColors.cardBorder, width: 1.0),
        boxShadow: NewsCardColors.cardShadow,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      (news.aiGeneratedTitle?.isNotEmpty == true)
                          ? news.aiGeneratedTitle!
                          : news.title,
                      style: NewsCardColors.tagStyle.copyWith(
                          color: NewsCardColors.title,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildSentimentIcon(news.sentiment),
                ],
              ),
              if (news.summary != null && news.summary!.isNotEmpty) ...[
                const SizedBox(height: 6),
                Text(
                  news.summary!,
                  style: NewsCardColors.tagStyle.copyWith(
                      color: NewsCardColors.description,
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      letterSpacing: 0.25),
                  maxLines: 5,
                  overflow: TextOverflow.ellipsis,
                ),
              ]
              else ...[
                const SizedBox(height: 8),
                Text(
                  news.description,
                  style: NewsCardColors.tagStyle.copyWith(
                      color: NewsCardColors.description,
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      letterSpacing: 0.25),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          news.source,
                          style: NewsCardColors.tagStyle.copyWith(
                              color: NewsCardColors.source,
                              fontSize: 10,
                              fontWeight: FontWeight.w500),
                        ),
                        // 🚫 Убрали NewsFreshnessIndicator для чистого интерфейса
                      ],
                    ),
                  ),
                  Text(
                    _formatDate(_getDisplayTime(news)),
                    style: NewsCardColors.tagStyle.copyWith(
                        color: NewsCardColors.meta,
                        fontSize: 10,
                        fontWeight: FontWeight.w500),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: news.tags.map((tag) {
                  return _buildTag(tag);
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTag(String tag) {
    final colors = _getTagColors(tag);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: colors.bgColor,
        borderRadius: BorderRadius.circular(6.0),
        border: Border.all(color: colors.borderColor, width: 1.0),
      ),
      child: Text(
        tag.toUpperCase(),
        style: NewsCardColors.tagStyle.copyWith(color: colors.textColor),
      ),
    );
  }

  _TagColors _getTagColors(String tag) {
    switch (tag.toLowerCase()) {
      case 'btc':
        return _TagColors(NewsCardColors.tagBTCBg, NewsCardColors.tagBTCBorder, NewsCardColors.tagBTCText);
      case 'ethereum':
      case 'eth':
        return _TagColors(NewsCardColors.tagETHBg, NewsCardColors.tagETHBorder, NewsCardColors.tagETHText);
      case 'defi':
        return _TagColors(NewsCardColors.tagDeFiBg, NewsCardColors.tagDeFiBorder, NewsCardColors.tagDeFiText);
      case 'ai':
        return _TagColors(NewsCardColors.tagAIBg, NewsCardColors.tagAIBorder, NewsCardColors.tagAIText);
      case 'stocks':
        return _TagColors(NewsCardColors.tagStocksBg, NewsCardColors.tagStocksBorder, NewsCardColors.tagStocksText);
      case 'sp500':
        return _TagColors(NewsCardColors.tagSP500Bg, NewsCardColors.tagSP500Border, NewsCardColors.tagSP500Text);
      case 'macro':
        return _TagColors(NewsCardColors.tagMacroBg, NewsCardColors.tagMacroBorder, NewsCardColors.tagMacroText);
      case 'crypto':
         return _TagColors(NewsCardColors.tagDefaultBg, NewsCardColors.tagDefaultBorder, NewsCardColors.tagDefaultText);
      case 'nasdaq':
         return _TagColors(NewsCardColors.tagNasdaqBg, NewsCardColors.tagNasdaqBorder, NewsCardColors.tagNasdaqText);
      case 'congress':
         return _TagColors(NewsCardColors.tagCongressBg, NewsCardColors.tagCongressBorder, NewsCardColors.tagCongressText);
      default:
        return _TagColors(NewsCardColors.tagDefaultBg, NewsCardColors.tagDefaultBorder, NewsCardColors.tagDefaultText);
    }
  }

  class _TagColors {
    final Color bgColor;
    final Color borderColor;
    final Color textColor;

    _TagColors(this.bgColor, this.borderColor, this.textColor);
  }

  Widget _buildSentimentIcon(SentimentType sentiment) {
    IconData iconData;
    Color color;

    switch (sentiment) {
      case SentimentType.positive:
        iconData = Icons.arrow_upward;
        color = NewsCardColors.trendUp;
        break;
      case SentimentType.negative:
        iconData = Icons.arrow_downward;
        color = NewsCardColors.trendDown;
        break;
      default:
        iconData = Icons.remove;
        color = NewsCardColors.trendNeutral;
    }

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(
        iconData,
        color: color,
        size: 20,
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} д. назад';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ч. назад';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} мин. назад';
    } else {
      return 'Только что';
    }
  }

  /// Возвращает время для отображения - приоритет publishedAt над fetchedAt
  DateTime _getDisplayTime(NewsItem news) {
    // Если publishedAt и fetchedAt отличаются более чем на 1 час,
    // используем publishedAt (реальное время публикации)
    final timeDiff = news.fetchedAt.difference(news.publishedAt).abs();
    if (timeDiff.inHours > 1) {
      return news.publishedAt;
    }

    // Если времена близки, используем publishedAt для более точного отображения
    return news.publishedAt;
  }
} 