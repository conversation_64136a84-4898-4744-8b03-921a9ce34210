#!/bin/bash

# Функция для отображения помощи
show_help() {
    echo "Использование: ./run_unified.sh [РЕЖИМ] [УСТРОЙСТВО]"
    echo ""
    echo "РЕЖИМ:"
    echo "  full, desktop    - Полная версия приложения"
    echo "  lite, mobile     - Лайт версия приложения"
    echo ""
    echo "УСТРОЙСТВО (опционально):"
    echo "  -d [device_id]   - Запуск на конкретном устройстве"
    echo "  -h, --help       - Показать эту справку"
    echo ""
    echo "Примеры:"
    echo "  ./run_unified.sh full"
    echo "  ./run_unified.sh lite"
    echo "  ./run_unified.sh full -d chrome"
    echo "  ./run_unified.sh lite -d emulator-5554"
}

# Проверяем аргументы
if [ "$1" = "-h" ] || [ "$1" = "--help" ] || [ $# -eq 0 ]; then
    show_help
    exit 0
fi

MODE=$1
DEVICE_ARG=""

# Проверяем второй аргумент для устройства
if [ "$2" = "-d" ] && [ -n "$3" ]; then
    DEVICE_ARG="-d $3"
fi

# Устанавливаем переменную окружения и запускаем
case $MODE in
    "full"|"desktop")
        echo "🚀 Запуск полной версии TMM..."
        flutter run --dart-define=APP_MODE=full lib/main_unified.dart $DEVICE_ARG
        ;;
    "lite"|"mobile")
        echo "📱 Запуск лайт версии TMM..."
        flutter run --dart-define=APP_MODE=lite lib/main_unified.dart $DEVICE_ARG
        ;;
    *)
        echo "❌ Неизвестный режим: $MODE"
        echo "Используйте: full, desktop, lite, или mobile"
        show_help
        exit 1
        ;;
esac
