import 'dart:convert';
import 'package:http/http.dart' as http;

class AltcoinSeasonIndexService {
  static const String _apiKey = '64510907-1901-4fb7-bb95-d42a26ebaf9b';
  static const String _endpoint = 'https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest?start=1&limit=100&convert=USD';

  static Future<double?> fetchAltcoinSeasonIndex() async {
    try {
      final response = await http.get(
        Uri.parse('http://localhost:4000/altcoin-index'),
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['index'] != null) {
          print('AltcoinSeasonIndex (proxy): ' + data['index'].toString());
          return (data['index'] as num).toDouble();
        }
      }
    } catch (e) {
      print('AltcoinSeasonIndex proxy error: ' + e.toString());
    }
    return null;
  }
} 