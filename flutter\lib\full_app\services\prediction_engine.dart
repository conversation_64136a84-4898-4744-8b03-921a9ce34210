import 'dart:math';
import 'package:flutter/foundation.dart';
import '../services/market_analytics.dart';

class PredictionEngine {
  static Future<List<double>> predictFutureValues(List<HistoricalEntry> history, int daysAhead) async {
    debugPrint('PredictionEngine received ${history.length} historical entries');
    if (history.length < 2) {
      debugPrint('Not enough data, using last value with deterministic variation');
      final baseValue = history.isNotEmpty ? history.last.value : 50.0;

      // Создаем детерминированные прогнозы на основе текущей даты
      final today = DateTime.now();
      final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;

      // Создаем прогнозы с детерминированной вариацией
      return List.generate(daysAhead, (i) {
        // Используем синус для создания волнообразного паттерна
        final variation = sin((dayOfYear + i) * 0.1) * 2.5 * (i + 1);
        return (baseValue + variation).clamp(0.0, 100.0);
      });
    }

    // Более продвинутый алгоритм прогнозирования
    final values = history.map((e) => e.value).toList();
    final window = min(7, values.length);

    // Расчет скользящего среднего на основе последних значений
    final movingAverage = values.sublist(values.length - window).reduce((a, b) => a + b) / window;

    // Расчет тренда с учетом последних значений для большей точности
    final trend = values.length > 1
      ? (values.last - values.sublist(values.length - window).first) / window
      : 0.0;

    // Расчет волатильности для определения размера вариации
    final volatility = calculateVolatility(history);

    // Используем текущую дату для создания детерминированной вариации
    final today = DateTime.now();
    final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;

    debugPrint('Moving Average: $movingAverage, Trend: $trend, Volatility: $volatility');

    // Генерация прогнозов с учетом тренда и детерминированной вариации
    List<double> predictions = [];
    for (int i = 0; i < daysAhead; i++) {
      // Базовый прогноз на основе тренда
      double forecast = movingAverage + trend * (i + 1);

      // Добавление детерминированной вариации, пропорциональной волатильности
      // Чем дальше в будущее, тем больше вариация
      double variationFactor = sin((dayOfYear + i * 3) * 0.2) * volatility * sqrt(i + 1) * 0.5;
      forecast += variationFactor;

      predictions.add(forecast.clamp(0.0, 100.0));
    }

    debugPrint('Enhanced Predictions: $predictions');
    return predictions;
  }

  /// Calculate volatility (standard deviation) of historical values
  static double calculateVolatility(List<HistoricalEntry> history) {
    if (history.length < 2) return 5.0; // Default volatility

    final values = history.map((e) => e.value).toList();

    // Используем только последние значения для расчета волатильности
    final window = min(14, values.length);
    final recentValues = values.sublist(values.length - window);

    final mean = recentValues.reduce((a, b) => a + b) / recentValues.length;
    final variance = recentValues.map((x) => pow(x - mean, 2)).reduce((a, b) => a + b) / recentValues.length;

    return sqrt(variance);
  }

  /// Calculate momentum (rate of change) of the sentiment
  static double calculateMomentum(List<HistoricalEntry> history) {
    if (history.length < 5) return 0.0;

    final values = history.map((e) => e.value).toList();
    final window = min(5, values.length);
    final recentValues = values.sublist(values.length - window);

    // Расчет линейного тренда за последние дни
    double sumX = 0;
    double sumY = 0;
    double sumXY = 0;
    double sumX2 = 0;

    for (int i = 0; i < recentValues.length; i++) {
      sumX += i;
      sumY += recentValues[i];
      sumXY += i * recentValues[i];
      sumX2 += i * i;
    }

    final n = recentValues.length.toDouble();
    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);

    return slope;
  }
}
