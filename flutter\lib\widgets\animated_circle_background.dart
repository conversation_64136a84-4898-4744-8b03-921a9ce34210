import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';

// Виджет анимированного фона в стиле Mintlify с шарами цвета #575757
class AnimatedCircleBackground extends StatefulWidget {
  const AnimatedCircleBackground({super.key});

  @override
  State<AnimatedCircleBackground> createState() => _AnimatedCircleBackgroundState();
}

class _AnimatedCircleBackgroundState extends State<AnimatedCircleBackground> 
    with TickerProviderStateMixin {
  late AnimationController _backgroundAnimationController;
  late AnimationController _colorAnimationController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _colorAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize background animation controllers – very slow for subtle motion
    _backgroundAnimationController = AnimationController(
      // Gentle drift of the blobs
      duration: const Duration(seconds: 40),
      vsync: this,
    );
    
    _colorAnimationController = AnimationController(
      // Even slower hue-shift to make background feel "alive" yet unobtrusive
      duration: const Duration(seconds: 60),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundAnimationController,
      curve: Curves.easeInOut,
    ));

    _colorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _colorAnimationController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _backgroundAnimationController.repeat(reverse: true);
    _colorAnimationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _backgroundAnimationController.dispose();
    _colorAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_backgroundAnimation, _colorAnimation]),
      builder: (context, child) {
        // Aurora-like colour palette (deep indigo  → violet  → teal  → indigo)
        const palette = [
          Color(0xFF1F2A40), // deep indigo-blue
          Color(0xFF36264F), // dark violet
          Color(0xFF154D4F), // deep teal
          Color(0xFF1F2A40), // back to indigo for seamless loop
        ];

        // Interpolate between palette colours to get current orb hue
        final segment = _colorAnimation.value * (palette.length - 1);
        final segIndex = segment.floor();
        final t = segment - segIndex;
        final ballColor = Color.lerp(
          palette[segIndex],
          palette[(segIndex + 1) % palette.length],
          t,
        )!;
        
        // First orb (main) - increased by 2x
        final centerX1 = -0.3 + (_backgroundAnimation.value * 0.3); // Move from -0.3 to 0.0
        final centerY1 = -0.4 + (_backgroundAnimation.value * 0.2); // Move from -0.4 to -0.2
        final radius1 = 1.4 + (_backgroundAnimation.value * 0.6); // From 1.4 to 2.0 (2x larger)
        
        // Second orb (smaller) - increased by 2x
        final centerX2 = 0.2 + (_colorAnimation.value * 0.2); // Move from 0.2 to 0.4
        final centerY2 = -0.6 + (_colorAnimation.value * 0.3); // Move from -0.6 to -0.3
        final radius2 = 0.8 + (_colorAnimation.value * 0.4); // From 0.8 to 1.2 (2x larger)
        
        // Third orb (below the right one)
        final centerX3 = 0.3 + (_backgroundAnimation.value * 0.15); // Move from 0.3 to 0.45
        final centerY3 = 0.1 + (_backgroundAnimation.value * 0.2); // Move from 0.1 to 0.3 (below the second orb)
        final radius3 = 0.6 + (_backgroundAnimation.value * 0.3); // From 0.6 to 0.9
        
        // Fourth orb (in the highlighted area - bottom left of screen)
        final centerX4 = -0.8 + (_colorAnimation.value * 0.15); // Move from -0.8 to -0.65 (far left)
        final centerY4 = 0.6 + (_colorAnimation.value * 0.2); // Move from 0.6 to 0.8 (bottom area)
        final radius4 = 0.5 + (_colorAnimation.value * 0.3); // From 0.5 to 0.8 (smaller size)
        
        // Slightly higher intensity to make background more visible but not overwhelming
        final colorIntensity = 0.12 + (_colorAnimation.value * 0.15); // 0.12 → 0.27
        
        return Container(
          decoration: const BoxDecoration(
            // Darker base with slight blue tint like Mintlify
            color: Color(0xFF0A0B0D),
          ),
          child: Stack(
            children: [
              // First orb (main)
              Container(
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(centerX1, centerY1),
                    radius: radius1,
                    colors: [
                      ballColor.withOpacity(colorIntensity),
                      ballColor.withOpacity(colorIntensity * 0.9),
                      ballColor.withOpacity(colorIntensity * 0.75),
                      ballColor.withOpacity(colorIntensity * 0.55),
                      ballColor.withOpacity(colorIntensity * 0.35),
                      ballColor.withOpacity(colorIntensity * 0.2),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.05, 0.12, 0.25, 0.45, 0.7, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: ballColor.withOpacity(0.4 * colorIntensity),
                      blurRadius: 120,
                      spreadRadius: 30,
                    ),
                  ],
                ),
              ),
              // Second orb (smaller)
              Container(
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(centerX2, centerY2),
                    radius: radius2,
                    colors: [
                      ballColor.withOpacity(colorIntensity * 0.8),
                      ballColor.withOpacity(colorIntensity * 0.6),
                      ballColor.withOpacity(colorIntensity * 0.45),
                      ballColor.withOpacity(colorIntensity * 0.3),
                      ballColor.withOpacity(colorIntensity * 0.18),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.1, 0.25, 0.45, 0.7, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: ballColor.withOpacity(0.35 * colorIntensity),
                      blurRadius: 90,
                      spreadRadius: 25,
                    ),
                  ],
                ),
              ),
              // Third orb (below the right one)
              Container(
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(centerX3, centerY3),
                    radius: radius3,
                    colors: [
                      ballColor.withOpacity(colorIntensity * 0.65),
                      ballColor.withOpacity(colorIntensity * 0.45),
                      ballColor.withOpacity(colorIntensity * 0.32),
                      ballColor.withOpacity(colorIntensity * 0.22),
                      ballColor.withOpacity(colorIntensity * 0.12),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.17, 0.34, 0.55, 0.8, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: ballColor.withOpacity(0.3 * colorIntensity),
                      blurRadius: 70,
                      spreadRadius: 20,
                    ),
                  ],
                ),
              ),
              // Fourth orb (bottom left from the first orb)
              Container(
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(centerX4, centerY4),
                    radius: radius4,
                    colors: [
                      ballColor.withOpacity(colorIntensity * 0.55),
                      ballColor.withOpacity(colorIntensity * 0.4),
                      ballColor.withOpacity(colorIntensity * 0.28),
                      ballColor.withOpacity(colorIntensity * 0.2),
                      ballColor.withOpacity(colorIntensity * 0.13),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.2, 0.38, 0.58, 0.82, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: ballColor.withOpacity(0.25 * colorIntensity),
                      blurRadius: 60,
                      spreadRadius: 18,
                    ),
                  ],
                ),
              ),
              // Subtle overlay for additional depth
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      ballColor.withOpacity(0.026 + (_colorAnimation.value * 0.039)),
                      Colors.transparent,
                      ballColor.withOpacity(0.0195 + (_backgroundAnimation.value * 0.026)),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.3, 0.7, 1.0],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
} 