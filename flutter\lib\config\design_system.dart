import 'package:flutter/material.dart';

/// Design system for the Finance AI app
/// Contains constants for typography, colors, spacing, and other design elements
class DesignSystem {
  // Typography
  // Using system font until Inter font files are added
  static const String fontFamily = '.SF Pro Text';

  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;

  // Font sizes
  static const double fontSizeXS = 12.0;
  static const double fontSizeS = 14.0;
  static const double fontSizeM = 16.0;
  static const double fontSizeL = 18.0;
  static const double fontSizeXL = 20.0;
  static const double fontSizeXXL = 24.0;
  static const double fontSizeXXXL = 32.0;

  // Line heights
  static const double lineHeightTight = 1.2;
  static const double lineHeightNormal = 1.5;
  static const double lineHeightLoose = 1.8;

  // Letter spacing
  static const double letterSpacingTight = -0.5;
  static const double letterSpacingNormal = 0.0;
  static const double letterSpacingWide = 0.5;

  // Spacing (8px grid system)
  static const double spacing2 = 2.0;
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing16 = 16.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing40 = 40.0;
  static const double spacing48 = 48.0;
  static const double spacing56 = 56.0;
  static const double spacing64 = 64.0;

  // Border radius
  static const double borderRadiusS = 4.0;
  static const double borderRadiusM = 8.0;
  static const double borderRadiusL = 16.0;
  static const double borderRadiusXL = 24.0;
  static const double borderRadiusCircular = 999.0;

  // Colors
  static const Color backgroundStart = Color(0xFF07080A);
  static const Color backgroundEnd = Color(0xFF0A0C0F);

  // Cosmic background
  static const String cosmicBackgroundImage = 'assets/images/background.png';
  static const Color cosmicBackgroundOverlay = Color(0x33000000); // Легкое затемнение для строгости

  // Card colors
  static const Color cardBackground = Color(0x26182030);
  static const Color cardBorder = Color(0x26FFFFFF);

  // Text colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xBBFFFFFF);
  static const Color textTertiary = Color(0x88FFFFFF);

  // Accent colors
  static const Color accentBlue = Color(0xFF003B7A);
  static const Color accentGreen = Color(0xFF00C853);
  static const Color accentOrange = Color(0xFFFF9500);
  static const Color accentRed = Color(0xFFFF3B30);

  // Sentiment colors
  static const Color sentimentCrash = Color(0xFFFF3B30);
  static const Color sentimentAnxiety = Color(0xFFFF9500);
  static const Color sentimentStasis = Color(0xFFFFCC00);
  static const Color sentimentLift = Color(0xFF87FC70);
  static const Color sentimentSurge = Color(0xFF00C853);

  // Shadows
  static List<BoxShadow> get subtleShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 40,
      spreadRadius: -5,
      offset: const Offset(0, 10),
    ),
  ];

  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.15),
      blurRadius: 50,
      spreadRadius: -2,
      offset: const Offset(0, 15),
    ),
  ];

  // Glassmorphism
  static BoxDecoration get glassCard => BoxDecoration(
    color: cardBackground,
    borderRadius: BorderRadius.circular(borderRadiusL),
    border: Border.all(
      color: cardBorder,
      width: 1.0,
    ),
    boxShadow: subtleShadow,
  );

  // Text styles
  static TextStyle get headingXL => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeXXXL,
    fontWeight: bold,
    letterSpacing: letterSpacingTight,
    color: textPrimary,
    height: lineHeightTight,
  );

  static TextStyle get headingL => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeXXL,
    fontWeight: bold,
    letterSpacing: letterSpacingTight,
    color: textPrimary,
    height: lineHeightTight,
  );

  static TextStyle get headingM => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeXL,
    fontWeight: semiBold,
    letterSpacing: letterSpacingTight,
    color: textPrimary,
    height: lineHeightTight,
  );

  static TextStyle get headingS => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeL,
    fontWeight: semiBold,
    letterSpacing: letterSpacingTight,
    color: textPrimary,
    height: lineHeightTight,
  );

  static TextStyle get bodyL => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeM,
    fontWeight: regular,
    letterSpacing: letterSpacingNormal,
    color: textPrimary,
    height: lineHeightNormal,
  );

  static TextStyle get bodyM => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeS,
    fontWeight: regular,
    letterSpacing: letterSpacingNormal,
    color: textPrimary,
    height: lineHeightNormal,
  );

  static TextStyle get bodyS => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeXS,
    fontWeight: regular,
    letterSpacing: letterSpacingNormal,
    color: textSecondary,
    height: lineHeightNormal,
  );

  static TextStyle get labelL => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeM,
    fontWeight: medium,
    letterSpacing: letterSpacingNormal,
    color: textPrimary,
    height: lineHeightTight,
  );

  static TextStyle get labelM => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeS,
    fontWeight: medium,
    letterSpacing: letterSpacingNormal,
    color: textPrimary,
    height: lineHeightTight,
  );

  static TextStyle get labelS => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeXS,
    fontWeight: medium,
    letterSpacing: letterSpacingNormal,
    color: textSecondary,
    height: lineHeightTight,
  );

  static TextStyle get numberL => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeXXL,
    fontWeight: bold,
    letterSpacing: letterSpacingTight,
    color: textPrimary,
    height: lineHeightTight,
  );

  static TextStyle get numberM => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeL,
    fontWeight: semiBold,
    letterSpacing: letterSpacingTight,
    color: textPrimary,
    height: lineHeightTight,
  );

  static TextStyle get numberS => TextStyle(
    fontFamily: fontFamily,
    fontSize: fontSizeM,
    fontWeight: medium,
    letterSpacing: letterSpacingTight,
    color: textPrimary,
    height: lineHeightTight,
  );

  // Get sentiment color based on value
  static Color getSentimentColor(double value) {
    if (value >= 80) return sentimentSurge;
    if (value >= 60) return sentimentLift;
    if (value >= 40) return sentimentStasis;
    if (value >= 20) return sentimentAnxiety;
    return sentimentCrash;
  }

  // Get sentiment glow based on value
  static List<BoxShadow> getSentimentGlow(double value) {
    final color = getSentimentColor(value);
    return [
      BoxShadow(
        color: color.withOpacity(0.3),
        blurRadius: 20,
        spreadRadius: 1,
      ),
    ];
  }
}
