import 'dart:convert';
import 'package:http/http.dart' as http;
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../models/trading_simulator_models.dart';
import 'binance_api_service.dart';

/// Helper class to represent market cycles for synthetic data generation
class MarketCycle {
  final MarketCycleType type;
  final int length;
  final double strength; // 0.0 to 1.0

  MarketCycle({
    required this.type,
    required this.length,
    required this.strength,
  });
}

/// Types of market cycles
enum MarketCycleType {
  bullish,
  bearish,
  sideways,
}

/// Class for generating and managing trading simulation data
class TradingSimulatorService {
  final random = math.Random();

  /// Fetch candles for a specific trading pair and timeframe
  /// If symbol is 'RANDOM', a random chart will be generated
  Future<List<CandleData>> fetchCandles({
    required String symbol,
    required TimeFrame timeFrame,
    int limit = 100,
    String difficulty = 'Medium',
    int? seed,
  }) async {
    if (symbol.toUpperCase() == 'RANDOM') {
      return generateRandomCandles(
        timeFrame: timeFrame, 
        limit: limit, 
        difficulty: difficulty,
        seed: seed,
      );
    }

    try {
      // Вместо текущей даты используем случайную дату из прошлого
      // Это позволит получать разные исторические данные при каждом запросе
      final random = seed != null ? math.Random(seed) : this.random;
      
      // Выбираем случайную дату за последние 2 года (730 дней)
      final now = DateTime.now();
      final daysToSubtract = random.nextInt(730) + 30; // от 30 до 760 дней назад
      final randomDate = now.subtract(Duration(days: daysToSubtract));
      
      final apiTimeframe = timeFrame.apiValue;

      // Конечная дата - это случайная дата из прошлого
      final endTime = randomDate;
      // Начальная дата рассчитывается на основе количества свечей и таймфрейма
      final startTime = endTime.subtract(timeFrame.duration * limit);
      
      final startTimeMs = startTime.millisecondsSinceEpoch;
      final endTimeMs = endTime.millisecondsSinceEpoch;

      // Делаем запрос к API с указанием конкретного временного диапазона
      final response = await http.get(Uri.parse(
        'https://api.binance.com/api/v3/klines?symbol=${symbol}USDT&interval=$apiTimeframe&limit=$limit&startTime=$startTimeMs&endTime=$endTimeMs',
      ));

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        
        // Если получили данные, преобразуем их в CandleData
        if (data.isNotEmpty) {
          final candles = data.map((item) => CandleData.fromBinanceData(item)).toList();
          
          // Если получили меньше свечей, чем запрашивали, возвращаем то что есть
          // или выбрасываем исключение если данных слишком мало
          if (candles.length < limit * 0.5) {
            throw Exception('Недостаточно исторических данных для ${symbol}USDT ($apiTimeframe). Получено ${candles.length} из $limit свечей.');
          }
          
          return candles;
        } else {
          throw Exception('Не удалось получить исторические данные для ${symbol}USDT ($apiTimeframe). Пустой ответ от API.');
        }
      } else {
        throw Exception('Ошибка при загрузке исторических данных для ${symbol}USDT ($apiTimeframe). Код: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching candles: $e');
      // Выбрасываем исключение вместо генерации случайных данных
      throw Exception('Не удалось загрузить данные: $e');
    }
  }

  // Generate missing candles
  List<CandleData> _generateMissingCandles(
    List<CandleData> existingCandles, 
    int count, 
    TimeFrame timeFrame,
    {String difficulty = 'Medium',
     int? seed,}
  ) {
    if (existingCandles.isEmpty) {
      return [];
    }

    // Инициализируем генератор случайных чисел с seed если он указан
    final random = seed != null ? math.Random(seed) : this.random;

    // Get volatility multiplier based on difficulty
    final volatilityMultiplier = _getVolatilityMultiplierForDifficulty(difficulty);
    
    final generated = <CandleData>[];
    var previousCandle = existingCandles.first;
    
    for (int i = 0; i < count; i++) {
      // Generate a candle before the first existing candle
      final newCandle = CandleData.generateRandom(
        previousCandle,
        volatilityFactor: 1.0 * volatilityMultiplier,
        random: random,
      );
      
      generated.add(newCandle);
      previousCandle = newCandle;
    }
    
    return generated;
  }

  /// Generate random candles for simulation
  List<CandleData> generateRandomCandles({
    required TimeFrame timeFrame,
    int limit = 100,
    String difficulty = 'Medium',
    String symbol = 'BTC',
    int? seed,
  }) {
    List<CandleData> candles = [];
    final now = DateTime.now();
    
    // Инициализируем генератор случайных чисел с seed если он указан
    final random = seed != null ? math.Random(seed) : this.random;
    
    // Get volatility multiplier based on difficulty
    final volatilityMultiplier = _getVolatilityMultiplierForDifficulty(difficulty);
    
    // Generate a reasonable base price based on common cryptocurrency price ranges
    double basePrice;
    switch (symbol) {
      case 'BTC':
        basePrice = 30000.0 + random.nextDouble() * 10000.0;
        break;
      case 'ETH':
        basePrice = 2000.0 + random.nextDouble() * 500.0;
        break;
      case 'BNB':
        basePrice = 300.0 + random.nextDouble() * 100.0;
        break;
      case 'SOL':
        basePrice = 100.0 + random.nextDouble() * 50.0;
        break;
      case 'XRP':
        basePrice = 0.5 + random.nextDouble() * 0.2;
        break;
      case 'ADA':
        basePrice = 0.3 + random.nextDouble() * 0.1;
        break;
      case 'AVAX':
        basePrice = 20.0 + random.nextDouble() * 10.0;
        break;
      case 'DOT':
        basePrice = 5.0 + random.nextDouble() * 2.0;
        break;
      case 'MATIC':
        basePrice = 0.8 + random.nextDouble() * 0.3;
        break;
      case 'LINK':
        basePrice = 10.0 + random.nextDouble() * 5.0;
        break;
      default:
        // For other coins, generate a reasonable price
        basePrice = 10.0 + random.nextDouble() * 90.0;
    }

    // Generate first candle
    final firstCandleTime = now.subtract(timeFrame.duration * limit);
    final firstCandle = CandleData(
      time: firstCandleTime,
      open: basePrice,
      high: basePrice * 1.01,
      low: basePrice * 0.99,
      close: basePrice * (1 + (random.nextDouble() - 0.5) * 0.02),
      volume: basePrice * (50 + random.nextDouble() * 100),
    );

    candles.add(firstCandle);

    // Create market cycles (bull, bear, sideways)
    // A typical market has cycles of different lengths
    List<MarketCycle> marketCycles = [];
    int remainingCandles = limit - 1;

    // Общая тенденция рынка - по умолчанию больше бычьих трендов (восходящий тренд)
    // Используем seed для создания предсказуемого, но разнообразного поведения
    final bool isBullishBiased = seed != null 
        ? math.Random(seed * 31 + 17).nextDouble() < 0.65 // 65% шанс смещения к бычьему рынку
        : random.nextDouble() < 0.65;

    while (remainingCandles > 0) {
      // Determine cycle type with balanced probabilities
      double cycleRandom = random.nextDouble();
      
      // Увеличиваем шанс появления лонг сценариев (бычьих трендов)
      final MarketCycleType cycleType;
      
      // Учитываем общую тенденцию рынка
      if (isBullishBiased) {
        // Для бычьего рынка: 55% бычьих, 30% медвежьих, 15% боковик
        if (cycleRandom < 0.55) {
          cycleType = MarketCycleType.bullish;
        } else if (cycleRandom < 0.85) {
          cycleType = MarketCycleType.bearish;
        } else {
          cycleType = MarketCycleType.sideways;
        }
      } else {
        // Для медвежьего рынка, но все равно с хорошим количеством бычьих трендов
        // 45% бычьих, 40% медвежьих, 15% боковик
        if (cycleRandom < 0.45) {
          cycleType = MarketCycleType.bullish;
        } else if (cycleRandom < 0.85) {
          cycleType = MarketCycleType.bearish;
        } else {
          cycleType = MarketCycleType.sideways;
        }
      }

      // Используем разные диапазоны длины цикла для разных типов циклов
      final int cycleLength;
      if (cycleType == MarketCycleType.bullish) {
        // Бычьи циклы могут быть длиннее (8-35 свечей)
        cycleLength = math.min(8 + random.nextInt(28), remainingCandles);
      } else if (cycleType == MarketCycleType.bearish) {
        // Медвежьи циклы обычно короче (5-25 свечей)
        cycleLength = math.min(5 + random.nextInt(21), remainingCandles);
      } else {
        // Боковые циклы очень короткие (3-15 свечей)
        cycleLength = math.min(3 + random.nextInt(13), remainingCandles);
      }

      // Дополнительная рандомизация силы цикла
      final double baseStrength = 0.2 + random.nextDouble() * 0.8;
      // Добавляем вариации для разных типов циклов
      final double cycleStrength;
      if (cycleType == MarketCycleType.bullish) {
        // Бычьи циклы могут быть сильнее
        cycleStrength = math.min(baseStrength * 1.2, 1.0);
      } else if (cycleType == MarketCycleType.bearish) {
        // Медвежьи циклы обычно слабее
        cycleStrength = baseStrength * 0.9;
      } else {
        // Боковые циклы самые слабые
        cycleStrength = baseStrength * 0.7;
      }

      marketCycles.add(MarketCycle(
        type: cycleType,
        length: cycleLength,
        strength: cycleStrength,
      ));

      remainingCandles -= cycleLength;
    }

    // Generate remaining candles based on market cycles
    int candleIndex = 1;
    for (final cycle in marketCycles) {
      for (int i = 0; i < cycle.length; i++) {
        final previousCandle = candles[candleIndex - 1];

        // Determine trend based on cycle type and position in cycle
        bool? forceTrend;
        
        // Высчитываем позицию в цикле (начало, середина, конец)
        final double cyclePosition = i / cycle.length; // от 0 до 1
        
        switch (cycle.type) {
          case MarketCycleType.bullish:
            // В бычьих циклах:
            // - Начало цикла: шанс восходящего тренда очень высокий
            // - Середина цикла: всё ещё высокий шанс восходящего тренда
            // - Конец цикла: уменьшенный шанс (подготовка к следующему циклу)
            double trendChance;
            if (cyclePosition < 0.3) {
              // Начало бычьего цикла - сильный импульс вверх
              trendChance = 0.85 + (cycle.strength * 0.15);
            } else if (cyclePosition < 0.7) {
              // Середина - умеренный рост
              trendChance = 0.7 + (cycle.strength * 0.2);
            } else {
              // Конец цикла - замедление роста
              trendChance = 0.5 + (cycle.strength * 0.3);
            }
            forceTrend = random.nextDouble() < trendChance ? true : null;
            break;
            
          case MarketCycleType.bearish:
            // В медвежьих циклах:
            // - Начало цикла: шанс нисходящего тренда очень высокий
            // - Середина цикла: всё ещё высокий шанс нисходящего тренда
            // - Конец цикла: уменьшенный шанс (подготовка к следующему циклу)
            double trendChance;
            if (cyclePosition < 0.3) {
              // Начало медвежьего цикла - сильный импульс вниз
              trendChance = 0.8 + (cycle.strength * 0.15);
            } else if (cyclePosition < 0.7) {
              // Середина - умеренное падение
              trendChance = 0.65 + (cycle.strength * 0.2);
            } else {
              // Конец цикла - замедление падения
              trendChance = 0.45 + (cycle.strength * 0.3);
            }
            forceTrend = random.nextDouble() < trendChance ? false : null;
            break;
            
          case MarketCycleType.sideways:
            // В боковом рынке чередуем небольшие подъемы и спады
            // с небольшим уклоном в ту или иную сторону
            if (i % 2 == 0) {
              forceTrend = random.nextDouble() < 0.55 ? true : false;
            } else {
              forceTrend = random.nextDouble() < 0.45 ? true : false;
            }
            break;
        }

        // Add volatility based on timeframe and cycle type
        double volatilityFactor = 1.0;

        // Timeframe-based volatility
        switch (timeFrame) {
          case TimeFrame.m30:
            volatilityFactor = 0.8;
            break;
          case TimeFrame.h1:
            volatilityFactor = 1.0;
            break;
          case TimeFrame.h4:
            volatilityFactor = 1.5;
            break;
          case TimeFrame.d1:
            volatilityFactor = 2.0;
            break;
        }

        // Cycle-based volatility adjustment
        switch (cycle.type) {
          case MarketCycleType.bullish:
          case MarketCycleType.bearish:
            volatilityFactor *= (0.8 + cycle.strength * 0.4);
            break;
          case MarketCycleType.sideways:
            volatilityFactor *= (0.3 + cycle.strength * 0.2);
            break;
        }

        // Apply difficulty multiplier to volatility
        volatilityFactor *= volatilityMultiplier;

        // Generate next candle with the calculated parameters
        final nextCandle = CandleData.generateRandom(
          previousCandle,
          volatilityFactor: volatilityFactor,
          forceTrend: forceTrend,
          random: random,
        );

        candles.add(nextCandle);
        candleIndex++;
      }
    }

    return candles;
  }

  /// Get a list of popular trading pairs
  List<String> getPopularTradingPairs() {
    return [
      'BTC', 'ETH', 'BNB', 'SOL', 'XRP',
      'ADA', 'AVAX', 'DOT', 'MATIC', 'LINK',
      'UNI', 'ATOM', 'LTC', 'DOGE', 'SHIB',
    ];
  }

  /// Get a random trading pair
  String getRandomTradingPair() {
    final pairs = getPopularTradingPairs();
    final random = math.Random().nextInt(pairs.length);
    return pairs[random];
  }

  /// Get a random timeframe
  TimeFrame getRandomTimeFrame() {
    final timeframes = TimeFrame.values;
    final random = math.Random().nextInt(timeframes.length);
    return timeframes[random];
  }

  /// Calculate the result of a trade
  TradingResult calculateTradeResult({
    required TradeAction action,
    required CandleData entryCandle,
    required CandleData resultCandle,
    required double tradeAmount,
    required double leverageMultiplier,
  }) {
    return TradingResult.calculate(
      action: action,
      entryCandle: entryCandle,
      resultCandle: resultCandle,
      tradeAmount: tradeAmount,
      leverageMultiplier: leverageMultiplier,
    );
  }

  /// Format currency value for display
  String formatCurrency(double value) {
    if (value.abs() >= 1000000) {
      return '\$${(value / 1000000).toStringAsFixed(2)}M';
    } else if (value.abs() >= 1000) {
      return '\$${(value / 1000).toStringAsFixed(2)}K';
    } else {
      return '\$${value.toStringAsFixed(2)}';
    }
  }

  /// Format percentage for display
  String formatPercentage(double percentage) {
    final sign = percentage >= 0 ? '+' : '';
    return '$sign${percentage.toStringAsFixed(2)}%';
  }

  /// Get volatility multiplier based on difficulty level
  double _getVolatilityMultiplierForDifficulty(String difficulty) {
    switch (difficulty) {
      case 'Easy':
        return 0.7; // Lower volatility for easier trading
      case 'Medium':
        return 1.0; // Normal volatility
      case 'Hard':
        return 1.5; // Higher volatility, more unpredictable
      case 'Expert':
        return 2.2; // Extreme volatility, very challenging
      default:
        return 1.0; // Default to Medium
    }
  }
}
