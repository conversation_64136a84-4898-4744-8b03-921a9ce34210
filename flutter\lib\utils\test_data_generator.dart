import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/sentiment_history_model.dart';
import '../services/sentiment_prediction_service.dart';

/// Utility class for generating test data for ARIMA model
class TestDataGenerator {
  /// Generate and save test data for ARIMA model
  /// 
  /// This method generates realistic test data for the ARIMA model
  /// and saves it to the historical data storage.
  /// 
  /// [daysToGenerate] is the number of days of data to generate
  /// [baseValue] is the starting value for the data
  /// [trend] is the overall trend direction (positive or negative)
  /// [volatility] is the amount of random variation in the data
  static Future<void> generateTestData({
    int daysToGenerate = 14,
    double baseValue = 50.0,
    double trend = 0.5,
    double volatility = 3.0,
  }) async {
    debugPrint('Generating $daysToGenerate days of test data for ARIMA model');
    debugPrint('Base value: $baseValue, Trend: $trend, Volatility: $volatility');
    
    final predictionService = SentimentPredictionService();
    final random = Random(42); // Fixed seed for reproducible results
    
    // Get current history to see what data we already have
    final history = await predictionService.getHistoricalData();
    final today = DateTime.now();
    
    // Generate data for the past [daysToGenerate] days
    for (int i = daysToGenerate; i >= 1; i--) {
      final date = DateTime(today.year, today.month, today.day - i);
      
      // Check if we already have data for this date
      final existingEntry = history.entries.where((e) => 
        DateTime(e.date.year, e.date.month, e.date.day).isAtSameMomentAs(date)
      ).toList();
      
      if (existingEntry.isNotEmpty) {
        debugPrint('Data already exists for ${date.toIso8601String()}, skipping');
        continue;
      }
      
      // Calculate value with trend and random variation
      // The further back in time, the more the value differs from the base
      final trendComponent = trend * i;
      final randomComponent = (random.nextDouble() * 2 - 1) * volatility;
      double value = baseValue - trendComponent + randomComponent;
      
      // Add some cyclical patterns (sine wave with 7-day period)
      final cyclicalComponent = sin(i * pi / 7) * volatility * 0.5;
      value += cyclicalComponent;
      
      // Ensure value is within valid range
      value = value.clamp(0.0, 100.0);
      
      // Create entry
      final entry = SentimentHistoryEntry(
        date: date,
        value: value,
        metrics: {
          'test_data': 1.0,
          'volatility': volatility,
          'trend': trend,
        },
      );
      
      // Add to history
      await predictionService.addHistoricalEntry(entry);
      debugPrint('Added test data for ${date.toIso8601String()}: $value');
    }
    
    // Verify the data was added
    final updatedHistory = await predictionService.getHistoricalData();
    debugPrint('Historical data now has ${updatedHistory.entries.length} entries');
  }
  
  /// Clear all test data from historical data storage
  static Future<void> clearTestData() async {
    debugPrint('Clearing test data from historical storage');
    
    final predictionService = SentimentPredictionService();
    final history = await predictionService.getHistoricalData();
    
    // Filter out entries with test_data metric
    final realEntries = history.entries.where((entry) => 
      entry.metrics == null || !entry.metrics!.containsKey('test_data')
    ).toList();
    
    debugPrint('Removing ${history.entries.length - realEntries.length} test data entries');
    
    // Save only real entries
    final newHistory = SentimentHistory(entries: realEntries);
    await predictionService.saveHistoricalData(newHistory);
    
    // Verify the data was removed
    final updatedHistory = await predictionService.getHistoricalData();
    debugPrint('Historical data now has ${updatedHistory.entries.length} entries');
  }
}
