import 'package:flutter/foundation.dart';
import '../models/news_item.dart';
import '../models/sentiment_types.dart';
import '../services/news_service.dart';

class TestNewsProvider with ChangeNotifier {
  final NewsService _newsService = NewsService();
  List<NewsItem> _news = [];
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';
  List<String> _selectedCategories = [];
  List<SentimentType> _selectedSentiments = [];

  List<NewsItem> get news => _news;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get searchQuery => _searchQuery;
  List<String> get selectedCategories => _selectedCategories;
  List<SentimentType> get selectedSentiments => _selectedSentiments;

  List<NewsItem> get filteredNews {
    return _news.where((news) {
      // Фильтр по категориям
      if (_selectedCategories.isNotEmpty && 
          !_selectedCategories.contains(news.category)) {
        return false;
      }

      // Фильтр по сентименту
      if (_selectedSentiments.isNotEmpty && 
          !_selectedSentiments.contains(news.sentiment)) {
        return false;
      }

      // Фильтр по поисковому запросу
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return news.title.toLowerCase().contains(query) ||
               news.description.toLowerCase().contains(query) ||
               (news.content?.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();
  }

  Future<void> loadTestNews() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _news = await _newsService.getAllNews();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  void searchNews(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setCategoryFilter(List<String> categories) {
    _selectedCategories = categories;
    notifyListeners();
  }

  void setSentimentFilter(List<SentimentType> sentiments) {
    _selectedSentiments = sentiments;
    notifyListeners();
  }

  void clearFilters() {
    _selectedCategories = [];
    _selectedSentiments = [];
    _searchQuery = '';
    notifyListeners();
  }

  Future<void> generateTestNews() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _news = await _newsService.getAllNews();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
} 