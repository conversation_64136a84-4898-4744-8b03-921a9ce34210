import 'package:flutter/material.dart';
import '../models/trading_simulator_models.dart';

/// A widget that displays the result of a trade
class TradeResultWidget extends StatelessWidget {
  final TradingResult result;
  final VoidCallback? onNextRound;

  const TradeResultWidget({
    super.key,
    required this.result,
    this.onNextRound,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(76), // Translucent background
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withAlpha(15),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            blurRadius: 15,
            spreadRadius: 0,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Result header - iOS style
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: result.isSuccess ? Colors.green.withAlpha(30) : Colors.red.withAlpha(30),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  result.isSuccess ? Icons.check_circle_outline : Icons.highlight_off,
                  color: result.isSuccess ? Colors.green : Colors.red,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  result.isSuccess ? 'Success!' : 'Wrong!',
                  style: TextStyle(
                    color: result.isSuccess ? Colors.green : Colors.red,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.5,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Trade details - iOS style
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(10),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                _buildDetailRow('Action', _getActionText()),
                const Divider(height: 24, color: Colors.white12),
                _buildDetailRow('Price Change', _getPriceChangeText()),
                const Divider(height: 24, color: Colors.white12),
                _buildDetailRow('Leverage', '${result.leverageMultiplier.toInt()}x'),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Profit/loss - iOS style
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: result.profitAmount >= 0
                    ? [Colors.green.withAlpha(40), Colors.green.withAlpha(20)]
                    : [Colors.red.withAlpha(40), Colors.red.withAlpha(20)],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Profit/Loss',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _formatProfitLoss(result.profitAmount),
                  style: TextStyle(
                    color: result.profitAmount >= 0 ? Colors.green : Colors.red,
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.5,
                  ),
                ),
              ],
            ),
          ),

          // Next round button - removed as it's handled by the parent
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: -0.3,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 15,
            fontWeight: FontWeight.w600,
            letterSpacing: -0.3,
          ),
        ),
      ],
    );
  }

  String _getActionText() {
    return result.action == TradeAction.buy ? 'BUY (UP)' : 'SELL (DOWN)';
  }

  String _getPriceChangeText() {
    final change = ((result.resultCandle.close - result.entryCandle.close) / result.entryCandle.close * 100);
    final sign = change >= 0 ? '+' : '';
    return '$sign${change.toStringAsFixed(2)}%';
  }

  String _formatProfitLoss(double amount) {
    final sign = amount >= 0 ? '+' : '';
    return '$sign\$${amount.abs().toStringAsFixed(2)}';
  }
}

/// A widget that displays the result of a FOMO trade
class FomoTradeResultWidget extends StatelessWidget {
  final String action;
  final String optimalAction;
  final double profitAmount;
  final double profitPercentage;
  final VoidCallback onNextRound;
  final bool isSpecialScenario;

  const FomoTradeResultWidget({
    super.key,
    required this.action,
    required this.optimalAction,
    required this.profitAmount,
    required this.profitPercentage,
    required this.onNextRound,
    this.isSpecialScenario = false,
  });

  @override
  Widget build(BuildContext context) {
    final isSuccess = profitAmount >= 0;
    final isPerfectDecision = action == optimalAction;
    final isGoodDecision = isSuccess && !isPerfectDecision; // Good but not perfect
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(76),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSuccess ? Colors.green.withAlpha(100) : Colors.red.withAlpha(100),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Decision outcome
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.close,
                color: isSuccess ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                isSuccess ? 'Good Decision!' : 'Bad Decision!',
                style: TextStyle(
                  color: isSuccess ? Colors.green : Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (isSpecialScenario)
            Container(
              margin: const EdgeInsets.only(top: 6.0),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.3),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(color: Colors.amber.withOpacity(0.5)),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.warning_amber_rounded, color: Colors.amber, size: 14),
                  SizedBox(width: 5),
                  Text(
                    "Special Market Conditions",
                    style: TextStyle(color: Colors.amber, fontSize: 11, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 10),
          // Горизонтальный блок с метриками и кнопкой
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Метрики
                Row(
                  children: [
                    _buildMetricColumn('Your Action', action),
                    const SizedBox(width: 16),
                    _buildMetricColumn('Optimal Action', optimalAction),
                    const SizedBox(width: 16),
                    _buildMetricColumn('Result', isSuccess ? 'Completely right!' : 'Completely wrong!'),
                    const SizedBox(width: 16),
                    _buildProfitColumn(profitAmount, profitPercentage, isSuccess),
                  ],
                ),
                const SizedBox(width: 24),
                // Кнопка справа
                SizedBox(
                  height: 60,
                  child: Center(
                    child: ElevatedButton(
                      onPressed: onNextRound,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
                        minimumSize: const Size(120, 40),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: const Text(
                        'Next Round',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 3),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildProfitColumn(double profitAmount, double profitPercentage, bool isSuccess) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profit/Loss',
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 3),
        Row(
          children: [
            Text(
              _formatProfit(profitAmount),
              style: TextStyle(
                color: isSuccess ? Colors.green : Colors.red,
                fontSize: 15,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              '(${_formatPercentage(profitPercentage)})',
              style: TextStyle(
                color: isSuccess ? Colors.green : Colors.red,
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _formatProfit(double amount) {
    final sign = amount >= 0 ? '+' : '';
    return '$sign\$${amount.abs().toStringAsFixed(2)}';
  }

  String _formatPercentage(double percentage) {
    final sign = percentage >= 0 ? '+' : '';
    return '$sign${percentage.toStringAsFixed(2)}%';
  }
}
