import 'dart:math' as math;
import 'dart:ui';

import 'package:flutter/material.dart';

/// Memory-Upload Transition screen.
/// Replaces the previous HyperjumpAnimationScreen.
///
/// Sequence:
/// 1. Quickly dims previous login scene via a fade overlay.
/// 2. Matrix-style vertical code rain starts falling.
/// 3. Central circular progress ring animates from 0 → 100 %.
/// 4. When progress completes, navigates to `/news` (main screen).
class HyperjumpAnimationScreen extends StatefulWidget {
  const HyperjumpAnimationScreen({Key? key}) : super(key: key);

  @override
  State<HyperjumpAnimationScreen> createState() => _HyperjumpAnimationScreenState();
}

class _HyperjumpAnimationScreenState extends State<HyperjumpAnimationScreen>
    with TickerProviderStateMixin {
  // Controllers
  late final AnimationController _fadeController;
  late final Animation<double> _fadeAnimation;

  late final AnimationController _codeController;
  late final AnimationController _progressController;

  late final AnimationController _hexRotController;

  // Pre-calculated X positions for code columns.
  final List<double> _columns = [];

  // Each column gets an individual speed multiplier & color offset for diversity.
  final List<double> _columnSpeeds = [];
  final List<Color> _columnColors = [];

  // Configuration
  static const _columnSpacing = 18.0;
  static const _codeColor = Color(0xFF00FF9C); // Neon-green

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _fadeAnimation = CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut);
    _fadeController.forward();

    _codeController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat();

    _progressController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    )..forward();

    _hexRotController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    )..repeat();

    // Navigate when "upload" finished.
    _progressController.addStatusListener((status) {
      if (status == AnimationStatus.completed && mounted) {
        Navigator.of(context).pushReplacementNamed('/news');
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Generate columns once when we know logical screen width.
    if (_columns.isEmpty) {
      final width = MediaQuery.of(context).size.width;
      for (double x = 0; x < width; x += _columnSpacing) {
        _columns.add(x + math.Random().nextDouble() * (_columnSpacing * 0.4));

        // random speed 0.6–1.4x
        _columnSpeeds.add(0.6 + math.Random().nextDouble() * 0.8);

        // slight hue shift for every column (neon green → aqua / magenta)
        final hsv = HSVColor.fromColor(_codeColor);
        final double hueShift = (math.Random().nextDouble() - 0.5) * 20; // ±20°
        _columnColors.add(hsv.withHue((hsv.hue + hueShift) % 360).toColor());
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _codeController.dispose();
    _progressController.dispose();
    _hexRotController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Cinematic dynamic backdrop.
          AnimatedBuilder(
            animation: _codeController,
            builder: (_, __) => CustomPaint(
              size: size,
              painter: _BackdropPainter(progress: _codeController.value),
            ),
          ),
          // Code rain background.
          AnimatedBuilder(
            animation: _codeController,
            builder: (_, __) => CustomPaint(
              size: size,
              painter: _CodeRainPainter(
                columns: _columns,
                progress: _codeController.value,
                baseColor: _codeColor,
                speeds: _columnSpeeds,
                colors: _columnColors,
              ),
            ),
          ),
          // Fade overlay that dims previous screen.
          FadeTransition(
            opacity: _fadeAnimation,
            child: Container(color: Colors.black.withOpacity(0.75)),
          ),
          // Central hexagonal progress indicator with neon glow.
          Center(
            child: AnimatedBuilder(
              animation: _progressController,
              builder: (_, __) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Transform.rotate(
                      angle: _hexRotController.value * 2 * math.pi,
                      child: CustomPaint(
                        size: const Size(140, 140),
                        painter: _HexagonProgressPainter(
                          progress: _progressController.value,
                          color: _codeColor,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Opacity(
                      opacity: 0.85,
                      child: Text(
                        _progressController.value < 1.0
                            ? 'Connecting to neural core…'
                            : 'Access granted',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// Painter that draws Matrix-like vertical code rain.
class _CodeRainPainter extends CustomPainter {
  _CodeRainPainter({
    required this.columns,
    required this.progress,
    required this.baseColor,
    required this.speeds,
    required this.colors,
  });

  final List<double> columns;
  final List<double> speeds; // per column speed multiplier
  final List<Color> colors; // per column tint
  final double progress; // 0..1 looped
  final Color baseColor;

  // Cache paint objects per alpha+color hash.
  final Map<int, Paint> _paintCache = {};

  Paint _getPaint(int alpha, Color color) {
    final int key = (alpha << 24) ^ color.value;
    return _paintCache.putIfAbsent(key, () {
      return Paint()..color = color.withAlpha(alpha);
    });
  }

  @override
  void paint(Canvas canvas, Size size) {
    const double columnHeight = 14.0; // size of symbol
    final double offsetY = progress * columnHeight * 20; // vertical movement

    for (int idx = 0; idx < columns.length; idx++) {
      final double x = columns[idx];
      final double speed = speeds[idx];
      final Color col = colors[idx];
      // Draw ~25 symbols per column.
      for (int i = -20; i < 25; i++) {
        final double y = i * columnHeight + offsetY * speed;
        if (y < -columnHeight || y > size.height + columnHeight) continue;

        // Alpha fades for tail effect; leading symbol brighter.
        int alpha = (255 * (1.0 - (i.abs() / 25))).clamp(30, 200).toInt();
        if (i == 0) alpha = 255;
        final paint = _getPaint(alpha, col);
        // Instead of real glyphs (heavy), draw small rectangles / bars.
        canvas.drawRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(x, y, 2.5, columnHeight * 0.8),
            const Radius.circular(1),
          ),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant _CodeRainPainter oldDelegate) => true;
}

/// Painter for a neon hexagon progress indicator.
class _HexagonProgressPainter extends CustomPainter {
  _HexagonProgressPainter({required this.progress, required this.color});

  final double progress; // 0..1
  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    final double radius = math.min(size.width, size.height) * 0.4;

    // Build hexagon vertices.
    final List<Offset> points = List.generate(6, (i) {
      final double angle = (math.pi / 3) * i - math.pi / 2; // start top
      return Offset(size.width / 2 + radius * math.cos(angle), size.height / 2 + radius * math.sin(angle));
    });

    // Dark base stroke.
    final Paint basePaint = Paint()
      ..color = Colors.white24
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    final Path hexPath = Path()..addPolygon(points, true);
    canvas.drawPath(hexPath, basePaint);

    // Neon glow backdrop.
    final Paint glowPaint = Paint()
      ..color = color.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 6;

    // Foreground neon stroke.
    final Paint fgPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    // Draw progress along perimeter.
    final double sideLen = (points[0] - points[1]).distance;
    final double perimeter = sideLen * 6;
    final double target = perimeter * progress;

    double drawn = 0;
    Path progressPath = Path();

    for (int i = 0; i < 6; i++) {
      final Offset start = points[i];
      final Offset end = points[(i + 1) % 6];
      final double remain = target - drawn;
      if (remain <= 0) break;

      final double thisLen = (end - start).distance;
      final double portion = remain >= thisLen ? 1.0 : remain / thisLen;

      final Offset segmentEnd = Offset(
        start.dx + (end.dx - start.dx) * portion,
        start.dy + (end.dy - start.dy) * portion,
      );

      progressPath.moveTo(start.dx, start.dy);
      progressPath.lineTo(segmentEnd.dx, segmentEnd.dy);

      if (remain < thisLen) break;
      drawn += thisLen;
    }

    canvas.drawPath(progressPath, glowPaint);
    canvas.drawPath(progressPath, fgPaint);
  }

  @override
  bool shouldRepaint(covariant _HexagonProgressPainter oldDelegate) => progress != oldDelegate.progress;
}

/// Painter for the cinematic dynamic backdrop.
class _BackdropPainter extends CustomPainter {
  _BackdropPainter({required this.progress});

  final double progress; // 0..1

  @override
  void paint(Canvas canvas, Size size) {
    // --- Radial gradient backdrop ---
    final Rect rect = Rect.fromLTWH(0, 0, size.width, size.height);
    const Color inner = Color(0xFF001014);
    const Color outer = Colors.black;
    final Paint gradientPaint = Paint()
      ..shader = RadialGradient(
        colors: [inner, outer],
        stops: const [0.0, 1.0],
      ).createShader(rect);
    canvas.drawRect(rect, gradientPaint);

    // --- Diagonal grid lines with parallax ---
    final Paint gridPaint = Paint()
      ..color = Colors.white12
      ..strokeWidth = 0.7;

    const double gridSpacing = 40.0;
    final double shift = progress * gridSpacing * 2; // moves grid

    for (double x = -size.height; x < size.width + size.height; x += gridSpacing) {
      // diagonal lines from top to bottom-right
      canvas.drawLine(
        Offset(x + shift, 0),
        Offset(x - size.height + shift, size.height),
        gridPaint,
      );
    }

    // --- Flash pulses based on sine wave ---
    final double flash = math.pow(math.sin(progress * math.pi * 4), 4).toDouble();
    if (flash > 0.05) {
      canvas.drawRect(
        rect,
        Paint()..color = Colors.white.withOpacity(0.07 * flash),
      );
    }

    // --- Vignette ---
    final Paint vignettePaint = Paint()
      ..shader = RadialGradient(
        colors: [Colors.transparent, Colors.black.withOpacity(0.7)],
        stops: const [0.7, 1.0],
      ).createShader(rect);
    canvas.drawRect(rect, vignettePaint);
  }

  @override
  bool shouldRepaint(covariant _BackdropPainter oldDelegate) => progress != oldDelegate.progress;
} 