import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

/// A customizable cosmic background widget with animated stars and optional effects.
/// 
/// This widget creates a beautiful animated star field background that can be used
/// across different screens in the application. It supports various customization
/// options including star colors, density, animation speed, and additional effects.
class CosmicBackground extends StatefulWidget {
  /// The number of stars to display in the background
  final int starCount;
  
  /// The base color of the stars
  final Color baseStarColor;
  
  /// Additional colors for star variety
  final List<Color> starColors;
  
  /// The minimum size of stars
  final double minStarSize;
  
  /// The maximum size of stars
  final double maxStarSize;
  
  /// The duration of the star twinkling animation
  final Duration animationDuration;
  
  /// Whether to enable the comet effect
  final bool enableComet;
  
  /// The background color
  final Color backgroundColor;
  
  /// Whether to enable parallax effect on mouse movement
  final bool enableParallax;
  
  /// The intensity of the parallax effect
  final double parallaxIntensity;
  
  /// Whether to enable asteroids
  final bool enableAsteroids;
  
  /// Whether to enable satellites
  final bool enableSatellites;

  const CosmicBackground({
    Key? key,
    this.starCount = 450,
    this.baseStarColor = Colors.white,
    this.starColors = const [],
    this.minStarSize = 1.0,
    this.maxStarSize = 3.0,
    this.animationDuration = const Duration(milliseconds: 6000),
    this.enableComet = true,
    this.backgroundColor = const Color(0xFF000011),
    this.enableParallax = true,
    this.parallaxIntensity = 17.5,
    this.enableAsteroids = true,
    this.enableSatellites = true,
  }) : super(key: key);

  @override
  State<CosmicBackground> createState() => _CosmicBackgroundState();
}

class _CosmicBackgroundState extends State<CosmicBackground> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final List<Star> _stars = [];
  final List<Comet> _comets = [];
  final List<Asteroid> _asteroids = [];
  final List<Satellite> _satellites = [];
  final List<StarlinkTrain> _starlinkTrains = [];
  Offset _pointerPosition = Offset.zero;
  late DateTime _lastCometSpawn;
  late DateTime _lastAsteroidSpawn;
  late DateTime _lastSatelliteSpawn;
  late DateTime _lastStarlinkSpawn;
  Size _lastSize = Size.zero;
  bool _isLowPerformanceMode = false;
  
  // Max number of flying objects
  final int _maxFlyingObjects = 10;

  @override
  void initState() {
    super.initState();
    _initializeStars();
    _initializeComets();
    _initializeAsteroids();
    if (widget.enableSatellites) {
      _initializeSatellites();
    }
    _lastCometSpawn = DateTime.now();
    _lastAsteroidSpawn = DateTime.now();
    _lastSatelliteSpawn = DateTime.now();
    _lastStarlinkSpawn = DateTime.now();
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    )..repeat(reverse: true);
    
    // Check for low performance mode
    _detectPerformance();
  }
  
  void _detectPerformance() {
    // Simple heuristic to detect low performance devices
    // Will be updated based on frame times during runtime
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final stopwatch = Stopwatch()..start();
      
      Future.delayed(const Duration(seconds: 2), () {
        stopwatch.stop();
        final fps = 2000 / (stopwatch.elapsedMilliseconds / 60);
        
        if (fps < 40) {
          setState(() {
            _isLowPerformanceMode = true;
            // Reduce number of active objects
            while (_asteroids.length > 2) {
              _asteroids.removeLast();
            }
          });
        }
      });
    });
  }

  void _initializeStars() {
    final random = math.Random();
    final allColors = [widget.baseStarColor, ...widget.starColors];
    
    for (int i = 0; i < widget.starCount; i++) {
      double x, y;
      
      if (i < widget.starCount * 0.2) {
        x = 0.6 + random.nextDouble() * 0.4;
        y = 0.0 + random.nextDouble() * 0.4;
      } else {
        x = random.nextDouble();
        y = random.nextDouble();
      }
      
      _stars.add(Star(
        x: x,
        y: y,
        size: widget.minStarSize + random.nextDouble() * (widget.maxStarSize - widget.minStarSize),
        brightness: 0.3 + random.nextDouble() * 0.7,
        blinkDuration: (random.nextDouble() * 2.0 + 3.0) * 1000,
        parallaxFactor: random.nextDouble() * 0.5 + 0.5,
      ));
    }
  }

  void _initializeComets() {
    if (widget.enableComet) {
      _spawnComet();
    }
  }

  void _spawnComet() {
    // Check if we can add more flying objects
    if (_getTotalFlyingObjects() >= _maxFlyingObjects) return;
    
    // Проверяем, есть ли уже активная комета
    bool hasActiveComet = false;
    for (var comet in _comets) {
      if (comet.isActive) {
        hasActiveComet = true;
        break;
      }
    }
    
    // Если уже есть активная комета, не создаем новую
    if (hasActiveComet) return;
    
    final random = math.Random();
    final side = random.nextInt(4); // 0: top, 1: right, 2: bottom, 3: left
    double x, y, angle;
    
    // Узкий диапазон углов, чтобы гарантировать пролёт через всю видимую область
    const double deviation = 0.2; // ~11 градусов

    switch (side) {
      case 0: // top -> вниз
        x = random.nextDouble();
        y = -0.05;
        angle = math.pi / 2 + (random.nextDouble() * 2 * deviation - deviation);
        break;
      case 1: // right -> влево
        x = 1.05;
        y = random.nextDouble();
        angle = math.pi + (random.nextDouble() * 2 * deviation - deviation);
        break;
      case 2: // bottom -> вверх
        x = random.nextDouble();
        y = 1.05;
        angle = -math.pi / 2 + (random.nextDouble() * 2 * deviation - deviation);
        break;
      default: // left -> вправо
        x = -0.05;
        y = random.nextDouble();
        angle = 0 + (random.nextDouble() * 2 * deviation - deviation);
        break;
    }
    
    _comets.add(Comet(
      x: x,
      y: y,
      speed: 0.015 + random.nextDouble() * 0.015,
      angle: angle,
      tailLength: 160.0 + random.nextDouble() * 80.0,
      size: 2.5 + random.nextDouble() * 2.5,
      isActive: true,
    ));
  }

  int _getTotalFlyingObjects() {
    int count = 0;
    for (var comet in _comets) if (comet.isActive) count++;
    for (var asteroid in _asteroids) if (asteroid.isActive) count++;
    for (var satellite in _satellites) if (satellite.isActive) count++;
    for (var train in _starlinkTrains) if (train.isActive) count++;
    return count;
  }

  void _initializeAsteroids() {
    if (widget.enableAsteroids) {
      for (int i = 0; i < 10; i++) {
        _spawnAsteroid();
      }
    }
  }

  void _spawnAsteroid() {
    // Check if we can add more flying objects
    if (_getTotalFlyingObjects() >= _maxFlyingObjects) return;
    
    // Проверяем количество активных астероидов
    int activeAsteroids = 0;
    for (var asteroid in _asteroids) {
      if (asteroid.x >= 0 && asteroid.x <= 1 && 
          asteroid.y >= 0 && asteroid.y <= 1) {
        activeAsteroids++;
      }
    }
    
    // Если уже есть астероид, не создаем новый
    if (activeAsteroids >= 5) return;
    
    final random = math.Random();
    final side = random.nextInt(4);
    double x, y, angle;
    
    switch (side) {
      case 0: // top
        x = random.nextDouble();
        y = -0.05;
        angle = random.nextDouble() * math.pi * 0.6 + math.pi * 0.2; // Downward angle
        break;
      case 1: // right
        x = 1.05;
        y = random.nextDouble();
        angle = random.nextDouble() * math.pi * 0.6 + math.pi * 0.7; // Leftward angle
        break;
      case 2: // bottom
        x = random.nextDouble();
        y = 1.05;
        angle = random.nextDouble() * math.pi * 0.6 - math.pi * 0.8; // Upward angle
        break;
      default: // left
        x = -0.05;
        y = random.nextDouble();
        angle = random.nextDouble() * math.pi * 0.6 - math.pi * 0.3; // Rightward angle
        break;
    }

    // Добавляем больше разнообразия метеоритов
    final asteroidType = random.nextInt(3); // 0 - обычный, 1 - ледяной, 2 - металлический
    
    _asteroids.add(Asteroid(
      x: x,
      y: y,
      speed: 0.5 + random.nextDouble() * 1.0,
      angle: angle,
      size: 5.0 + random.nextDouble() * 15.0,
      rotation: random.nextDouble() * math.pi * 2,
      rotationSpeed: (random.nextDouble() - 0.5) * 0.002 / 5,
      type: asteroidType,
    ));
  }

  void _initializeSatellites() {
    for (int i = 0; i < 5; i++) {
      _spawnSatellite();
    }
  }

  void _spawnSatellite() {
    // Check if we can add more flying objects
    if (_getTotalFlyingObjects() >= _maxFlyingObjects) return;
    
    // Check how many satellites are already active
    int activeSatellites = 0;
    for (var satellite in _satellites) {
      if (satellite.x >= 0 && satellite.x <= 1 && satellite.y >= 0 && satellite.y <= 1) {
        activeSatellites++;
      }
    }
    
    // If there's already a satellite, don't spawn a new one
    if (activeSatellites >= 5) return;
    
    final random = math.Random();
    final side = random.nextInt(4);
    double x, y, angle;
    
    switch (side) {
      case 0: // top
        x = random.nextDouble();
        y = -0.05;
        angle = random.nextDouble() * math.pi * 0.6 + math.pi * 0.2; // Downward angle
        break;
      case 1: // right
        x = 1.05;
        y = random.nextDouble();
        angle = random.nextDouble() * math.pi * 0.6 + math.pi * 0.7; // Leftward angle
        break;
      case 2: // bottom
        x = random.nextDouble();
        y = 1.05;
        angle = random.nextDouble() * math.pi * 0.6 - math.pi * 0.8; // Upward angle
        break;
      default: // left
        x = -0.05;
        y = random.nextDouble();
        angle = random.nextDouble() * math.pi * 0.6 - math.pi * 0.3; // Rightward angle
        break;
    }

    final satelliteType = random.nextInt(4);
    
    _satellites.add(Satellite(
      x: x,
      y: y,
      speed: 0.7 + random.nextDouble() * 0.8,
      angle: angle,
      size: 3.0 + random.nextDouble() * 2.0,
      rotation: random.nextDouble() * math.pi * 2,
      rotationSpeed: 0.008,
      type: satelliteType,
    ));
  }

  void _spawnStarlinkTrain() {
    // Check if we can add more flying objects
    if (_getTotalFlyingObjects() >= _maxFlyingObjects) return;
    
    // Check if there's already a starlink train on screen
    for (var train in _starlinkTrains) {
      if (!train.isActive) {
        return;
      }
    }
    
    final random = math.Random();
    final side = random.nextInt(4);
    double x, y, angle;
    
    switch (side) {
      case 0: // top
        x = random.nextDouble();
        y = -0.05;
        angle = random.nextDouble() * math.pi * 0.6 + math.pi * 0.2; // Downward angle
        break;
      case 1: // right
        x = 1.05;
        y = random.nextDouble();
        angle = random.nextDouble() * math.pi * 0.6 + math.pi * 0.7; // Leftward angle
        break;
      case 2: // bottom
        x = random.nextDouble();
        y = 1.05;
        angle = random.nextDouble() * math.pi * 0.6 - math.pi * 0.8; // Upward angle
        break;
      default: // left
        x = -0.05;
        y = random.nextDouble();
        angle = random.nextDouble() * math.pi * 0.6 - math.pi * 0.3; // Rightward angle
        break;
    }

    final train = StarlinkTrain(
      x: x,
      y: y,
      speed: 0.3 + random.nextDouble() * 0.15, // Reduced for smoother movement
      angle: angle,
      satelliteCount: 5 + random.nextInt(5),
      satelliteSize: 2.0 + random.nextDouble() * 1.0,
      satelliteSpacing: 20.0 + random.nextDouble() * 10.0,
    );

    _starlinkTrains.add(train);
  }

  void _handleCollisions() {
    // Check collisions between asteroids
    for (int i = 0; i < _asteroids.length; i++) {
      for (int j = i + 1; j < _asteroids.length; j++) {
        if (_asteroids[i].intersectsWith(_asteroids[j])) {
          _asteroids[i].collideWith(_asteroids[j]);
        }
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentSize = MediaQuery.of(context).size;
    if (_lastSize != currentSize && _lastSize != Size.zero) {
      // Adjust star positions on size change
      for (final star in _stars) {
        star.x = math.min(1.0, star.x);
        star.y = math.min(1.0, star.y);
      }
    }
    _lastSize = currentSize;
    
    return MouseRegion(
      opaque: false, // Allow mouse events to pass through to widgets below
      onHover: widget.enableParallax ? (event) {
        setState(() {
          _pointerPosition = event.localPosition;
        });
      } : null,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          // Update objects
          final now = DateTime.now();
          
          if (widget.enableComet && 
              now.difference(_lastCometSpawn).inSeconds >= (_isLowPerformanceMode ? 15 : 10)) {
            _spawnComet();
            _lastCometSpawn = now;
          }
          
          if (widget.enableAsteroids && 
              now.difference(_lastAsteroidSpawn).inSeconds >= (_isLowPerformanceMode ? 15 : 10)) {
            _spawnAsteroid();
            _lastAsteroidSpawn = now;
          }

          if (widget.enableSatellites) {
            if (now.difference(_lastSatelliteSpawn).inSeconds >= (_isLowPerformanceMode ? 10 : 5)) {
              _spawnSatellite();
              _lastSatelliteSpawn = now;
            }
            if (now.difference(_lastStarlinkSpawn).inSeconds >= (_isLowPerformanceMode ? 40 : 25)) {
              _spawnStarlinkTrain();
              _lastStarlinkSpawn = now;
            }
          }
          
          // Handle collisions
          _handleCollisions();

          return CustomPaint(
            size: Size.infinite,
            painter: CosmicBackgroundPainter(
              stars: _stars,
              comets: _comets,
              asteroids: _asteroids,
              satellites: _satellites,
              starlinkTrains: _starlinkTrains,
              animationValue: _animationController.value,
              pointerPosition: _pointerPosition,
              screenSize: MediaQuery.of(context).size,
              backgroundColor: widget.backgroundColor,
              enableParallax: widget.enableParallax,
              parallaxIntensity: widget.parallaxIntensity,
              isLowPerformanceMode: _isLowPerformanceMode,
            ),
          );
        },
      ),
    );
  }
}

/// Represents a star in the cosmic background
class Star {
  double x;
  double y;
  final double size;
  final double brightness;
  final double blinkDuration;
  final double parallaxFactor;

  Star({
    required this.x,
    required this.y,
    required this.size,
    this.brightness = 1.0,
    required this.blinkDuration,
    required this.parallaxFactor,
  });
}

/// Represents a comet in the cosmic background
class Comet {
  double x;
  double y;
  final double speed;
  double angle;
  final double tailLength;
  final double size;
  double opacity = 1.0;
  bool isActive = true;

  Comet({
    required this.x,
    required this.y,
    required this.speed,
    required this.angle,
    required this.tailLength,
    required this.size,
    required this.isActive,
  });

  void update(Size canvasSize) {
    if (!isActive) return;

    // Плавно меняем угол для лёгкой кривизны траектории
    angle += 0.0008; // небольшое значение для аккуратного изгиба

    // Обновляем позицию (медленное движение)
    x += math.cos(angle) * speed;
    y += math.sin(angle) * speed;
    
    // No bouncing off boundaries, just check visibility
    // Check if comet is in visible area
    final isInVisibleArea = x >= 0.0 && x <= 1.0 && y >= 0.0 && y <= 1.0;

    // Fade in when entering visible area
    if (isInVisibleArea && opacity < 1.0) {
      opacity = math.min(1.0, opacity + 0.1);
    }

    // Fade out when leaving visible area
    if (!isInVisibleArea && opacity > 0.0) {
      opacity = math.max(0.0, opacity - 0.1);
    }

    // Check if comet is completely out of bounds
    if (x < -tailLength * 2 ||
        x > canvasSize.width + tailLength * 2 ||
        y < -tailLength * 2 ||
        y > canvasSize.height + tailLength * 2) {
      isActive = false;
    }
  }

  bool isVisible() {
    return isActive && opacity > 0.0;
  }
}

class Asteroid {
  double x;
  double y;
  final double speed;
  double angle;
  final double size;
  bool isActive;
  double opacity;
  double rotation;
  final double rotationSpeed;
  final int type;

  Asteroid({
    required this.x,
    required this.y,
    required this.speed,
    required this.angle,
    required this.size,
    this.isActive = true,
    this.opacity = 0.0,
    required this.rotation,
    required this.rotationSpeed,
    this.type = 0,
  });

  void update(Size canvasSize) {
    if (!isActive) return;

    // Update position
    x += math.cos(angle) * speed;
    y += math.sin(angle) * speed;
    rotation += rotationSpeed;

    // Check if asteroid is in visible area
    final isInVisibleArea = x >= 0.0 && x <= 1.0 && y >= 0.0 && y <= 1.0;

    // Fade in when entering visible area
    if (isInVisibleArea && opacity < 1.0) {
      opacity = math.min(1.0, opacity + 0.1);
    }

    // Fade out when leaving visible area
    if (!isInVisibleArea && opacity > 0.0) {
      opacity = math.max(0.0, opacity - 0.1);
    }

    // Check if asteroid is completely out of bounds
    if (x < -0.1 || x > 1.1 || y < -0.1 || y > 1.1) {
      isActive = false;
    }
  }
  
  bool intersectsWith(Asteroid other) {
    final distance = math.sqrt(math.pow(x - other.x, 2) + math.pow(y - other.y, 2));
    return distance < (size + other.size) / 100; // Convert size to relative coordinates
  }
  
  void collideWith(Asteroid other) {
    // Mark as colliding
    isActive = false;
    other.isActive = false;
  }
}

class Satellite {
  double x;
  double y;
  final double speed;
  double angle;
  final double size;
  bool isActive;
  double opacity;
  double rotation;
  final double rotationSpeed;
  final int type;

  Satellite({
    required this.x,
    required this.y,
    required this.speed,
    required this.angle,
    required this.size,
    this.isActive = true,
    this.opacity = 0.0,
    required this.rotation,
    required this.rotationSpeed,
    this.type = 0,
  });

  void update(Size canvasSize) {
    if (!isActive) return;

    // Update position
    x += math.cos(angle) * speed;
    y += math.sin(angle) * speed;
    rotation += rotationSpeed;

    // Check if satellite is in visible area
    final isInVisibleArea = x >= 0.0 && x <= 1.0 && y >= 0.0 && y <= 1.0;

    // Fade in when entering visible area
    if (isInVisibleArea && opacity < 1.0) {
      opacity = math.min(1.0, opacity + 0.1);
    }

    // Fade out when leaving visible area
    if (!isInVisibleArea && opacity > 0.0) {
      opacity = math.max(0.0, opacity - 0.1);
    }

    // Check if satellite is completely out of bounds
    if (x < -0.1 || x > 1.1 || y < -0.1 || y > 1.1) {
      isActive = false;
    }
  }
}

class StarlinkTrain {
  double x;
  double y;
  final double speed;
  double angle;
  final int satelliteCount;
  final double satelliteSize;
  final double satelliteSpacing;
  bool isActive;
  double opacity;

  StarlinkTrain({
    required this.x,
    required this.y,
    required this.speed,
    required this.angle,
    required this.satelliteCount,
    required this.satelliteSize,
    required this.satelliteSpacing,
    this.isActive = true,
    this.opacity = 0.0,
  });

  bool isFullyInvisible(Size size) {
    final firstSatelliteX = x;
    final firstSatelliteY = y;
    final lastSatelliteX = x + math.cos(angle) * satelliteSpacing * (satelliteCount - 1);
    final lastSatelliteY = y + math.sin(angle) * satelliteSpacing * (satelliteCount - 1);

    return (firstSatelliteX < -0.1 && lastSatelliteX < -0.1) ||
           (firstSatelliteX > 1.1 && lastSatelliteX > 1.1) ||
           (firstSatelliteY < -0.1 && lastSatelliteY < -0.1) ||
           (firstSatelliteY > 1.1 && lastSatelliteY > 1.1);
  }

  void update(Size canvasSize) {
    if (!isActive) return;

    // Update position
    x += math.cos(angle) * speed;
    y += math.sin(angle) * speed;

    // Check if train is in visible area
    final isInVisibleArea = !isFullyInvisible(canvasSize);

    // Fade in when entering visible area
    if (isInVisibleArea && opacity < 1.0) {
      opacity = math.min(1.0, opacity + 0.1);
    }

    // Fade out when leaving visible area
    if (!isInVisibleArea && opacity > 0.0) {
      opacity = math.max(0.0, opacity - 0.1);
    }

    // Check if train is completely out of bounds
    if (isFullyInvisible(canvasSize)) {
      isActive = false;
    }
  }
}

class CosmicBackgroundPainter extends CustomPainter {
  final List<Star> stars;
  final List<Comet> comets;
  final List<Asteroid> asteroids;
  final List<Satellite> satellites;
  final List<StarlinkTrain> starlinkTrains;
  final double animationValue;
  final Offset pointerPosition;
  final Size screenSize;
  final Color backgroundColor;
  final bool enableParallax;
  final double parallaxIntensity;
  final bool isLowPerformanceMode;

  CosmicBackgroundPainter({
    required this.stars,
    required this.comets,
    required this.asteroids,
    required this.satellites,
    required this.starlinkTrains,
    required this.animationValue,
    required this.pointerPosition,
    required this.screenSize,
    required this.backgroundColor,
    required this.enableParallax,
    required this.parallaxIntensity,
    this.isLowPerformanceMode = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = backgroundColor,
    );

    // Calculate parallax offset
    final parallaxX = enableParallax ? 
        (pointerPosition.dx / screenSize.width - 0.5) * parallaxIntensity : 0.0;
    final parallaxY = enableParallax ? 
        (pointerPosition.dy / screenSize.height - 0.5) * parallaxIntensity : 0.0;

    // Update and draw all objects
    _drawStars(canvas, size, parallaxX, parallaxY);
    _updateAndDrawComets(canvas, size);
    _updateAndDrawAsteroids(canvas, size, parallaxX, parallaxY);
    _updateAndDrawSatellites(canvas, size, parallaxX, parallaxY);
    _updateAndDrawStarlinkTrains(canvas, size, parallaxX, parallaxY);
  }
  
  void _drawStars(Canvas canvas, Size size, double parallaxX, double parallaxY) {
    // Optimize star drawing
    final starsToDraw = isLowPerformanceMode ? stars.length ~/ 2 : stars.length;
    final starStep = isLowPerformanceMode ? 2 : 1;
    
    for (int i = 0; i < starsToDraw; i += starStep) {
      final star = stars[i * starStep % stars.length];
      
      final x = star.x * size.width + parallaxX * star.parallaxFactor;
      final y = star.y * size.height + parallaxY * star.parallaxFactor;

      final pulseOffset = math.sin(2 * math.pi * (animationValue * star.brightness + star.x * star.y * 1.3) % 1);
      final pulseExp = math.pow(pulseOffset.abs(), 1.5) * (pulseOffset >= 0 ? 1 : -1);
      final opacity = star.brightness * (0.1 + (0.9 - 0.1) * (pulseExp * 0.5 + 0.5));

      if (star.size > 1.5 && !isLowPerformanceMode) {
        final outerGlowPaint = Paint()
          ..color = Colors.white.withAlpha((opacity * 60).toInt())
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);
        
        canvas.drawCircle(
          Offset(x, y),
          star.size * 2.5,
          outerGlowPaint,
        );
        
        final glowPaint = Paint()
          ..color = Colors.white.withAlpha((opacity * 100).toInt())
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

        canvas.drawCircle(
          Offset(x, y),
          star.size * 1.8,
          glowPaint,
        );
      }

      // Draw star
      final starPaint = Paint()
        ..color = Colors.white.withAlpha((opacity * 255).toInt());

      canvas.drawCircle(
        Offset(x, y),
        star.size,
        starPaint,
      );
      
      if (star.size > 1.8 && !isLowPerformanceMode) {
        final highlightPaint = Paint()
          ..color = Colors.white;
          
        canvas.drawCircle(
          Offset(x - star.size * 0.3, y - star.size * 0.3),
          star.size * 0.2,
          highlightPaint,
        );
      }
    }
  }
  
  void _updateAndDrawComets(Canvas canvas, Size size) {
    // Update and draw comets
    for (var comet in comets.toList()) {
      comet.update(size);
      if (comet.isVisible()) {
        _drawComet(canvas, size, comet);
      } else {
        comets.remove(comet);
      }
    }
  }
  
  void _updateAndDrawAsteroids(Canvas canvas, Size size, double parallaxX, double parallaxY) {
    // Update and draw asteroids
    for (var asteroid in asteroids.toList()) {
      asteroid.update(size);
      _drawAsteroid(canvas, size, asteroid, parallaxX, parallaxY);
    }
  }
  
  void _updateAndDrawSatellites(Canvas canvas, Size size, double parallaxX, double parallaxY) {
    // Update and draw satellites
    for (var satellite in satellites.toList()) {
      satellite.update(size);
      _drawSatellite(canvas, size, satellite, parallaxX, parallaxY);
    }
  }
  
  void _updateAndDrawStarlinkTrains(Canvas canvas, Size size, double parallaxX, double parallaxY) {
    // Update and draw Starlink trains
    for (var train in starlinkTrains.toList()) {
      train.update(size);
      
      // Check if train is fully invisible before removing
      if (train.isActive) {
        _drawStarlinkTrain(canvas, size, train, parallaxX, parallaxY);
      } else {
        starlinkTrains.remove(train);
      }
    }
  }

  void _drawComet(Canvas canvas, Size size, Comet comet) {
    final x = comet.x * size.width;
    final y = comet.y * size.height;
    
    // Create gradient for comet tail
    final tailGradient = ui.Gradient.linear(
      Offset(x, y),
      Offset(
        x - comet.tailLength * math.cos(comet.angle),
        y - comet.tailLength * math.sin(comet.angle),
      ),
      [
        Colors.white.withAlpha((comet.opacity * 255).toInt()),
        Colors.white.withAlpha((comet.opacity * 0.8 * 255).toInt()),
        Colors.blue.withAlpha((comet.opacity * 0.6 * 255).toInt()),
        Colors.transparent,
      ],
      [0.0, 0.3, 0.6, 1.0],
    );

    // Draw comet tail
    final tailPaint = Paint()
      ..shader = tailGradient
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      Offset(x, y),
      Offset(
        x - comet.tailLength * math.cos(comet.angle),
        y - comet.tailLength * math.sin(comet.angle),
      ),
      tailPaint,
    );

    // Draw comet head with glow
    final outerGlowPaint = Paint()
      ..color = Colors.white.withAlpha((comet.opacity * 70).toInt())
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);

    canvas.drawCircle(
      Offset(x, y),
      comet.size * 2.2,
      outerGlowPaint,
    );

    final glowPaint = Paint()
      ..color = Colors.white.withAlpha((comet.opacity * 100).toInt())
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

    canvas.drawCircle(
      Offset(x, y),
      comet.size * 1.5,
      glowPaint,
    );

    final headPaint = Paint()
      ..color = Colors.white.withAlpha((comet.opacity * 255).toInt())
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1.0);

    canvas.drawCircle(
      Offset(x, y),
      comet.size,
      headPaint,
    );
    
    // Блик на ядре кометы
    final highlightPaint = Paint()
      ..color = Colors.white.withAlpha((comet.opacity * 255).toInt());
      
    canvas.drawCircle(
      Offset(x - comet.size * 0.3, y - comet.size * 0.3),
      comet.size * 0.3,
      highlightPaint,
    );
  }

  void _drawAsteroid(Canvas canvas, Size size, Asteroid asteroid, double parallaxX, double parallaxY) {
    final x = asteroid.x * size.width + parallaxX * 0.3;
    final y = asteroid.y * size.height + parallaxY * 0.3;
    final random = math.Random(asteroid.hashCode); // Стабильный рандом

    canvas.save();
    canvas.translate(x, y);
    canvas.rotate(asteroid.rotation);
    
    // Тени для 3D эффекта
    final shadowOffset = Offset(asteroid.size * 0.1, asteroid.size * 0.1);
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 8.0);
    
    // Создаем детализированную форму астероида
    final baseRadius = asteroid.size * 0.9;
    const numPoints = 48; // Больше точек для детализации
    
    // Создаем два отдельных пути для более трехмерного вида
    final path = Path();
    final topPath = Path();
    final basePoints = <Offset>[];
    
    // Создаем базовую форму
    for (int i = 0; i < numPoints; i++) {
      final angle = i * 2 * math.pi / numPoints;
      
      // Несколько уровней шума для более реалистичной формы
      final noiseBig = math.sin(angle * 3 + asteroid.hashCode) * 0.15;
      final noiseMed = math.sin(angle * 7 + asteroid.hashCode * 2) * 0.08;
      final noiseSmall = math.sin(angle * 13 + asteroid.hashCode * 3) * 0.04;
      
      final radius = baseRadius * (0.8 + noiseBig + noiseMed + noiseSmall + random.nextDouble() * 0.1);
      final pointX = math.cos(angle) * radius;
      final pointY = math.sin(angle) * radius;
      basePoints.add(Offset(pointX, pointY));
    }
    
    // Основной путь
    path.moveTo(basePoints[0].dx, basePoints[0].dy);
    for (int i = 0; i < numPoints; i++) {
      final current = basePoints[i];
      final next = basePoints[(i + 1) % numPoints];
      
      // Сглаживаем путь с помощью кривых Безье
      final controlPoint1 = Offset(
        current.dx + (next.dx - current.dx) * 0.5 + (random.nextDouble() - 0.5) * asteroid.size * 0.05,
        current.dy + (next.dy - current.dy) * 0.5 + (random.nextDouble() - 0.5) * asteroid.size * 0.05,
      );
      
      path.quadraticBezierTo(
        controlPoint1.dx, controlPoint1.dy,
        next.dx, next.dy
      );
    }
    path.close();
    
    // "Верхняя" часть астероида (выглядит как освещенная часть)
    topPath.moveTo(basePoints[0].dx * 0.7, basePoints[0].dy * 0.7);
    for (int i = 0; i < numPoints; i++) {
      if (i % 3 != 0) continue; // Делаем более грубую форму для верхней части
      
      final idx = (i == 0) ? 0 : i;
      final current = Offset(basePoints[idx].dx * 0.7, basePoints[idx].dy * 0.7);
      final nextIdx = ((i + 3) % numPoints == 0) ? 0 : (i + 3) % numPoints;
      final next = Offset(basePoints[nextIdx].dx * 0.7, basePoints[nextIdx].dy * 0.7);
      
      final controlPoint = Offset(
        current.dx + (next.dx - current.dx) * 0.5 + (random.nextDouble() - 0.5) * asteroid.size * 0.1,
        current.dy + (next.dy - current.dy) * 0.5 + (random.nextDouble() - 0.5) * asteroid.size * 0.1,
      );
      
      topPath.quadraticBezierTo(
        controlPoint.dx, controlPoint.dy,
        next.dx, next.dy
      );
    }
    topPath.close();
    
    // Выбираем текстуры и градиенты в зависимости от типа астероида
    ui.Gradient mainGradient;
    ui.Gradient topGradient;
    Color craterColor;
    Color highlightColor;
    
    switch (asteroid.type) {
      case 1: // Ледяной
        mainGradient = ui.Gradient.radial(
          Offset(asteroid.size * -0.2, asteroid.size * -0.2),
          asteroid.size * 2.5,
          [
            Colors.lightBlue.shade200,
            Colors.lightBlue.shade400,
            Colors.lightBlue.shade700,
            Colors.blue.shade900,
          ],
          [0.0, 0.3, 0.7, 1.0],
        );
        
        topGradient = ui.Gradient.radial(
          Offset(asteroid.size * -0.1, asteroid.size * -0.1),
          asteroid.size * 1.5,
          [
            Colors.white,
            Colors.lightBlue.shade100,
            Colors.lightBlue.shade300,
          ],
          [0.0, 0.5, 1.0],
        );
        
        craterColor = Colors.blue.shade800;
        highlightColor = Colors.white.withOpacity(0.7);
        break;
        
      case 2: // Металлический
        mainGradient = ui.Gradient.radial(
          Offset(asteroid.size * -0.2, asteroid.size * -0.2),
          asteroid.size * 2.5,
          [
            Colors.grey.shade300,
            Colors.grey.shade400,
            Colors.grey.shade700,
            Colors.grey.shade900,
          ],
          [0.0, 0.3, 0.7, 1.0],
        );
        
        topGradient = ui.Gradient.linear(
          Offset(asteroid.size * -0.5, asteroid.size * -0.5),
          Offset(asteroid.size * 0.5, asteroid.size * 0.5),
          [
            Colors.grey.shade200,
            Colors.grey.shade300,
            Colors.grey.shade500,
          ],
          [0.0, 0.5, 1.0],
        );
        
        craterColor = Colors.grey.shade800;
        highlightColor = Colors.white.withOpacity(0.5);
        break;
        
      default: // Обычный, каменистый
        mainGradient = ui.Gradient.radial(
          Offset(asteroid.size * -0.2, asteroid.size * -0.2),
          asteroid.size * 2.5,
          [
            Colors.brown.shade400,
            Colors.brown.shade500,
            Colors.brown.shade700,
            Colors.brown.shade900,
          ],
          [0.0, 0.3, 0.7, 1.0],
        );
        
        topGradient = ui.Gradient.radial(
          Offset(asteroid.size * -0.1, asteroid.size * -0.1),
          asteroid.size * 1.5,
          [
            Colors.brown.shade200,
            Colors.brown.shade300,
            Colors.brown.shade500,
          ],
          [0.0, 0.5, 1.0],
        );
        
        craterColor = Colors.brown.shade900;
        highlightColor = Colors.brown.shade200.withOpacity(0.5);
    }
    
    // Рисуем тень
    canvas.drawPath(path, shadowPaint);
    
    // Рисуем основной астероид
    final mainPaint = Paint()
      ..shader = mainGradient
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(path, mainPaint);
    
    // Рисуем микротекстуру (мелкие детали поверхности)
    final detailPaint = Paint()
      ..color = Colors.black.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.2;
    
    // Много мелких линий текстуры
    for (int i = 0; i < 80; i++) {
      final startAngle = random.nextDouble() * 2 * math.pi;
      final len = random.nextDouble() * asteroid.size * 0.5;
      final startR = random.nextDouble() * asteroid.size * 0.9;
      
      final startX = math.cos(startAngle) * startR;
      final startY = math.sin(startAngle) * startR;
      
      final endX = startX + (random.nextDouble() - 0.5) * len;
      final endY = startY + (random.nextDouble() - 0.5) * len;
      
      // Проверяем, что линия внутри астероида
      if (path.contains(Offset(startX, startY)) && path.contains(Offset(endX, endY))) {
        canvas.drawLine(
          Offset(startX, startY),
          Offset(endX, endY),
          detailPaint,
        );
      }
    }
    
    // Рисуем верхнюю часть астероида (освещенная сторона)
    final topPaint = Paint()
      ..shader = topGradient
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(topPath, topPaint);
    
    // Рисуем кратеры
    for (int i = 0; i < 20; i++) {
      final craterX = (random.nextDouble() * 2 - 1) * asteroid.size * 0.8;
      final craterY = (random.nextDouble() * 2 - 1) * asteroid.size * 0.8;
      final craterSize = asteroid.size * (0.05 + random.nextDouble() * 0.15);
      
      // Проверяем, что кратер внутри астероида
      if (path.contains(Offset(craterX, craterY))) {
        // Тень кратера
        final craterShadowPaint = Paint()
          ..shader = ui.Gradient.radial(
            Offset(craterX + craterSize * 0.2, craterY + craterSize * 0.2),
            craterSize * 1.2,
            [
              Colors.black.withOpacity(0.5),
              Colors.black.withOpacity(0.3),
              Colors.transparent,
            ],
            [0.0, 0.7, 1.0],
          );
        
        canvas.drawCircle(
          Offset(craterX, craterY),
          craterSize * 1.1,
          craterShadowPaint,
        );
        
        // Дно кратера
        final craterBottomPaint = Paint()
          ..color = craterColor
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(
          Offset(craterX, craterY),
          craterSize * 0.9,
          craterBottomPaint,
        );
        
        // Края кратера
        final craterEdgePaint = Paint()
          ..color = Colors.black.withOpacity(0.5)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;
        
        canvas.drawCircle(
          Offset(craterX, craterY),
          craterSize,
          craterEdgePaint,
        );
        
        // Детали внутри кратера
        if (craterSize > asteroid.size * 0.1) {
          for (int j = 0; j < 5; j++) {
            final rockX = craterX + (random.nextDouble() * 2 - 1) * craterSize * 0.5;
            final rockY = craterY + (random.nextDouble() * 2 - 1) * craterSize * 0.5;
            final rockSize = craterSize * 0.1 * random.nextDouble();
            
            final distToCenter = math.sqrt(math.pow(rockX - craterX, 2) + math.pow(rockY - craterY, 2));
            if (distToCenter < craterSize * 0.7) {
              canvas.drawCircle(
                Offset(rockX, rockY),
                rockSize,
                Paint()..color = craterColor.withOpacity(0.7),
              );
            }
          }
        }
      }
    }
    
    // Блики света для 3D-эффекта
    canvas.drawCircle(
      Offset(-asteroid.size * 0.3, -asteroid.size * 0.3),
      asteroid.size * 0.3,
      Paint()..color = highlightColor,
    );
    
    canvas.restore();
  }

  void _drawSatellite(Canvas canvas, Size size, Satellite satellite, double parallaxX, double parallaxY) {
    final x = satellite.x * size.width + parallaxX * 0.3;
    final y = satellite.y * size.height + parallaxY * 0.3;

    canvas.save();
    canvas.translate(x, y);
    canvas.rotate(satellite.rotation);

    switch (satellite.type) {
      case 0: // Стандартный спутник с двумя солнечными панелями
        _drawStandardSatellite(canvas, satellite);
        break;
      case 1: // Компактный спутник с большой антенной
        _drawCompactSatellite(canvas, satellite);
        break;
      case 2: // Круглый спутник с тарелкой
        _drawDishSatellite(canvas, satellite);
        break;
      case 3: // Спутник с множеством панелей
        _drawMultiPanelSatellite(canvas, satellite);
        break;
    }

    canvas.restore();
  }
  
  void _drawStandardSatellite(Canvas canvas, Satellite satellite) {
    final bodyPaint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.fill;

    final panelPaint = Paint()
      ..color = Colors.blue.shade900
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromCenter(
        center: Offset.zero,
        width: satellite.size * 2,
        height: satellite.size,
      ),
      bodyPaint,
    );

    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(-satellite.size * 1.5, 0),
        width: satellite.size * 2,
        height: satellite.size * 0.8,
      ),
      panelPaint,
    );

    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(satellite.size * 1.5, 0),
        width: satellite.size * 2,
        height: satellite.size * 0.8,
      ),
      panelPaint,
    );

    final detailPaint = Paint()
      ..color = Colors.grey.shade400
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    for (int i = 0; i < 4; i++) {
      canvas.drawLine(
        Offset(-satellite.size * 1.5, -satellite.size * 0.4 + i * satellite.size * 0.2),
        Offset(-satellite.size * 0.5, -satellite.size * 0.4 + i * satellite.size * 0.2),
        detailPaint,
      );

      canvas.drawLine(
        Offset(satellite.size * 0.5, -satellite.size * 0.4 + i * satellite.size * 0.2),
        Offset(satellite.size * 1.5, -satellite.size * 0.4 + i * satellite.size * 0.2),
        detailPaint,
      );
    }

    canvas.drawLine(
      Offset(0, -satellite.size * 0.5),
      Offset(0, -satellite.size * 1.5),
      detailPaint,
    );

    canvas.drawCircle(
      Offset(0, -satellite.size * 1.5),
      satellite.size * 0.2,
      bodyPaint,
    );
  }
  
  void _drawCompactSatellite(Canvas canvas, Satellite satellite) {
    final bodyPaint = Paint()
      ..color = Colors.grey.shade400
      ..style = PaintingStyle.fill;
    
    final detailPaint = Paint()
      ..color = Colors.grey.shade700
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
      
    final antennaPaint = Paint()
      ..color = Colors.grey.shade600
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.2;
    
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset.zero,
        width: satellite.size * 1.5,
        height: satellite.size * 1.2,
      ),
      bodyPaint,
    );
    
    final antennaDish = Path();
    antennaDish.moveTo(-satellite.size * 0.7, -satellite.size * 0.6);
    antennaDish.quadraticBezierTo(
      0, -satellite.size * 1.5,
      satellite.size * 0.7, -satellite.size * 0.6
    );
    canvas.drawPath(antennaDish, antennaPaint);
    
    canvas.drawLine(
      Offset(0, -satellite.size * 0.6),
      Offset(0, -satellite.size * 2.0),
      antennaPaint,
    );
    
    canvas.drawLine(
      Offset(-satellite.size * 0.7, 0),
      Offset(satellite.size * 0.7, 0),
      detailPaint,
    );
    
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(0, satellite.size * 0.4),
        width: satellite.size,
        height: satellite.size * 0.3,
      ),
      detailPaint,
    );
    
    canvas.drawLine(
      Offset(-satellite.size * 0.7, -satellite.size * 0.3),
      Offset(-satellite.size * 1.0, -satellite.size * 0.8),
      detailPaint,
    );
    
    canvas.drawLine(
      Offset(satellite.size * 0.7, -satellite.size * 0.3),
      Offset(satellite.size * 1.0, -satellite.size * 0.8),
      detailPaint,
    );
  }
  
  void _drawDishSatellite(Canvas canvas, Satellite satellite) {
    final bodyPaint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.fill;
    
    final dishPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    final detailPaint = Paint()
      ..color = Colors.grey.shade600
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    canvas.drawCircle(
      Offset.zero,
      satellite.size,
      bodyPaint,
    );
    
    final dishRect = Rect.fromCenter(
      center: Offset(0, -satellite.size * 1.8),
      width: satellite.size * 2.5,
      height: satellite.size * 1.2,
    );
    
    final dish = Path();
    dish.addArc(dishRect, 0, math.pi);
    canvas.drawPath(dish, dishPaint);
    canvas.drawPath(dish, detailPaint);
    
    canvas.drawLine(
      Offset(0, -satellite.size),
      Offset(0, -satellite.size * 1.2),
      Paint()
        ..color = Colors.grey.shade600
        ..strokeWidth = 2.0,
    );
    
    for (int i = 0; i < 4; i++) {
      final angle = i * math.pi / 2;
      canvas.drawLine(
        Offset(math.cos(angle) * satellite.size * 0.5, math.sin(angle) * satellite.size * 0.5),
        Offset(math.cos(angle) * satellite.size * 0.9, math.sin(angle) * satellite.size * 0.9),
        detailPaint,
      );
    }
    
    canvas.drawCircle(
      Offset(0, -satellite.size * 1.8),
      satellite.size * 0.2,
      Paint()..color = Colors.grey.shade600,
    );
  }
  
  void _drawMultiPanelSatellite(Canvas canvas, Satellite satellite) {
    final bodyPaint = Paint()
      ..color = Colors.grey.shade500
      ..style = PaintingStyle.fill;
    
    final panelPaint = Paint()
      ..color = Colors.blue.shade800
      ..style = PaintingStyle.fill;
    
    final detailPaint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    final hexPath = Path();
    for (int i = 0; i < 6; i++) {
      final angle = i * math.pi / 3;
      final x = math.cos(angle) * satellite.size;
      final y = math.sin(angle) * satellite.size;
      
      if (i == 0) {
        hexPath.moveTo(x, y);
      } else {
        hexPath.lineTo(x, y);
      }
    }
    hexPath.close();
    canvas.drawPath(hexPath, bodyPaint);
    
    for (int i = 0; i < 6; i++) {
      final angle = i * math.pi / 3;
      final baseX = math.cos(angle) * satellite.size * 0.8;
      final baseY = math.sin(angle) * satellite.size * 0.8;
      
      final panelRect = Rect.fromCenter(
        center: Offset(baseX * 1.7, baseY * 1.7),
        width: satellite.size * 1.2,
        height: satellite.size * 0.7,
      );
      
      canvas.save();
      canvas.translate(baseX * 1.7, baseY * 1.7);
      canvas.rotate(angle + math.pi/2);
      
      canvas.drawRect(
        Rect.fromCenter(
          center: Offset.zero,
          width: satellite.size * 1.2,
          height: satellite.size * 0.7,
        ),
        panelPaint,
      );
      
      for (int j = 0; j < 3; j++) {
        canvas.drawLine(
          Offset(-satellite.size * 0.6, -satellite.size * 0.35 + j * satellite.size * 0.35),
          Offset(satellite.size * 0.6, -satellite.size * 0.35 + j * satellite.size * 0.35),
          detailPaint,
        );
      }
      
      canvas.restore();
    }
    
    canvas.drawCircle(
      Offset.zero,
      satellite.size * 0.4,
      Paint()..color = Colors.grey.shade600,
    );
    
    canvas.drawLine(
      Offset(0, -satellite.size),
      Offset(0, -satellite.size * 2.0),
      Paint()
        ..color = Colors.grey.shade400
        ..strokeWidth = 1.0,
    );
    
    canvas.drawCircle(
      Offset(0, -satellite.size * 2.0),
      satellite.size * 0.15,
      Paint()..color = Colors.grey.shade300,
    );
  }

  void _drawStarlinkTrain(Canvas canvas, Size size, StarlinkTrain train, double parallaxX, double parallaxY) {
    final x = train.x * size.width + parallaxX * 0.3;
    final y = train.y * size.height + parallaxY * 0.3;
    
    bool isTrainVisible = false;
    
    final firstSatelliteX = x;
    final firstSatelliteY = y;
    final lastSatelliteX = x - (train.satelliteCount - 1) * train.satelliteSpacing * math.cos(train.angle);
    final lastSatelliteY = y - (train.satelliteCount - 1) * train.satelliteSpacing * math.sin(train.angle);
    
    bool movingLeft = math.cos(train.angle) < 0;
    bool movingUp = math.sin(train.angle) < 0;
    
    if (movingLeft) {
      if (lastSatelliteX > -train.satelliteSize * 4) {
        isTrainVisible = true;
      }
    } else {
      if (lastSatelliteX < size.width + train.satelliteSize * 4) {
        isTrainVisible = true;
      }
    }
    
    if (movingUp) {
      if (lastSatelliteY > -train.satelliteSize * 4) {
        isTrainVisible = true;
      }
    } else {
      if (lastSatelliteY < size.height + train.satelliteSize * 4) {
        isTrainVisible = true;
      }
    }
    
    if (!isTrainVisible) return;

    for (int i = 0; i < train.satelliteCount; i++) {
      final satelliteX = x - i * train.satelliteSpacing * math.cos(train.angle);
      final satelliteY = y - i * train.satelliteSpacing * math.sin(train.angle);

      if (satelliteX >= -train.satelliteSize*4 && satelliteX <= size.width + train.satelliteSize*4 && 
          satelliteY >= -train.satelliteSize*4 && satelliteY <= size.height + train.satelliteSize*4) {
        canvas.save();
        canvas.translate(satelliteX, satelliteY);
        canvas.rotate(train.angle + math.pi / 2);

        final bodyPaint = Paint()
          ..color = Colors.grey.shade300
          ..style = PaintingStyle.fill;

        final panelPaint = Paint()
          ..color = Colors.blue.shade900
          ..style = PaintingStyle.fill;

        canvas.drawRect(
          Rect.fromCenter(
            center: Offset.zero,
            width: train.satelliteSize * 2,
            height: train.satelliteSize,
          ),
          bodyPaint,
        );

        canvas.drawRect(
          Rect.fromCenter(
            center: Offset(-train.satelliteSize * 1.5, 0),
            width: train.satelliteSize * 2,
            height: train.satelliteSize * 0.8,
          ),
          panelPaint,
        );

        canvas.drawRect(
          Rect.fromCenter(
            center: Offset(train.satelliteSize * 1.5, 0),
            width: train.satelliteSize * 2,
            height: train.satelliteSize * 0.8,
          ),
          panelPaint,
        );

        final detailPaint = Paint()
          ..color = Colors.grey.shade400
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;

        for (int j = 0; j < 4; j++) {
          canvas.drawLine(
            Offset(-train.satelliteSize * 1.5, -train.satelliteSize * 0.4 + j * train.satelliteSize * 0.2),
            Offset(-train.satelliteSize * 0.5, -train.satelliteSize * 0.4 + j * train.satelliteSize * 0.2),
            detailPaint,
          );

          canvas.drawLine(
            Offset(train.satelliteSize * 0.5, -train.satelliteSize * 0.4 + j * train.satelliteSize * 0.2),
            Offset(train.satelliteSize * 1.5, -train.satelliteSize * 0.4 + j * train.satelliteSize * 0.2),
            detailPaint,
          );
        }

        canvas.drawLine(
          Offset(0, -train.satelliteSize * 0.5),
          Offset(0, -train.satelliteSize * 1.2),
          detailPaint,
        );

        canvas.drawCircle(
          Offset(0, -train.satelliteSize * 1.2),
          train.satelliteSize * 0.15,
          bodyPaint,
        );

        canvas.restore();
      }
    }
  }

  @override
  bool shouldRepaint(CosmicBackgroundPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.pointerPosition != pointerPosition;
  }
} 