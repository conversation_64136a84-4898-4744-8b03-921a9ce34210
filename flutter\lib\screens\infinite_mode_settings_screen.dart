import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../models/trading_simulator_models.dart';

class InfiniteModeSettingsScreen extends StatefulWidget {
  const InfiniteModeSettingsScreen({super.key});

  @override
  State<InfiniteModeSettingsScreen> createState() => _InfiniteModeSettingsScreenState();
}

class _InfiniteModeSettingsScreenState extends State<InfiniteModeSettingsScreen> {
  double _leverage = 10.0;
  final List<double> _leverageOptions = [1, 2, 5, 10, 20, 50, 100];

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : const Color(0xFFF2F2F7),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text('Infinite Mode Settings'),
        leading: CupertinoNavigationBarBackButton(
          color: isDarkMode ? Colors.white : Colors.black,
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Заголовок
              Text(
                'Select Leverage',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Подзаголовок
              Text(
                'Higher leverage means higher risk and reward',
                style: TextStyle(
                  fontSize: 16,
                  color: isDarkMode ? Colors.white.withAlpha(179) : Colors.black.withAlpha(138),
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 40),
              
              // Текущее значение кредитного плеча
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: isDarkMode ? const Color(0xFF1C1C1E) : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(isDarkMode ? 77 : 13),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Text(
                      '${_leverage.toInt()}x',
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: _getLeverageColor(_leverage),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getLeverageDescription(_leverage),
                      style: TextStyle(
                        fontSize: 14,
                        color: isDarkMode ? Colors.white.withAlpha(179) : Colors.black.withAlpha(138),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Выбор кредитного плеча
              Container(
                height: 60,
                decoration: BoxDecoration(
                  color: isDarkMode ? const Color(0xFF1C1C1E) : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(isDarkMode ? 77 : 13),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: CupertinoSegmentedControl<double>(
                  children: {
                    for (var leverage in _leverageOptions)
                      leverage: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          '${leverage.toInt()}x',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  },
                  onValueChanged: (value) {
                    setState(() {
                      _leverage = value;
                    });
                  },
                  groupValue: _leverage,
                  selectedColor: const Color(0xFF007AFF),
                  unselectedColor: isDarkMode ? const Color(0xFF2C2C2E) : Colors.white,
                  borderColor: isDarkMode ? Colors.white.withAlpha(26) : Colors.black.withAlpha(13),
                  padding: const EdgeInsets.all(4),
                ),
              ),
              
              const Spacer(),
              
              // Кнопка Start Trading
              CupertinoButton(
                onPressed: _startTrading,
                color: const Color(0xFF007AFF),
                borderRadius: BorderRadius.circular(16),
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: const Text(
                  'Start Trading',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // Получить цвет в зависимости от значения кредитного плеча
  Color _getLeverageColor(double leverage) {
    if (leverage <= 5) {
      return Colors.green;
    } else if (leverage <= 20) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
  
  // Получить описание в зависимости от значения кредитного плеча
  String _getLeverageDescription(double leverage) {
    if (leverage <= 5) {
      return 'Low risk, suitable for beginners';
    } else if (leverage <= 20) {
      return 'Medium risk, for experienced traders';
    } else {
      return 'High risk, for professional traders only';
    }
  }
  
  // Начать торговлю
  void _startTrading() {
    Navigator.pushNamed(
      context,
      '/crypto_simulator',
      arguments: {
        'mode': SimulatorMode.infinitePatterns,
        'leverage': _leverage,
        'symbol': 'BTCUSDT',
        'interval': '1h',
        'initialBalance': 1000.0,
      },
    );
  }
}
