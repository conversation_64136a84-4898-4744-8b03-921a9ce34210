import 'package:flutter/material.dart';
import '../widgets/app_bottom_navigation.dart';

class SavedAnalysesScreen extends StatelessWidget {
  const SavedAnalysesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Saved'),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Header
          const Text(
            'Your Saved Items',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24.0),

          // Saved analyses
          _buildSectionHeader('Analyses', 7),
          const SizedBox(height: 12.0),
          _buildAnalysisItem(
            context,
            'Bitcoin Price Analysis',
            'Detailed technical analysis of BTC price movements and predictions',
            'May 15, 2023',
            Icons.analytics,
            Colors.orange,
          ),
          _buildAnalysisItem(
            context,
            'Ethereum Market Outlook',
            'Long-term market outlook for Ethereum and related tokens',
            'June 2, 2023',
            Icons.bar_chart,
            Colors.blue,
          ),
          _buildAnalysisItem(
            context,
            'DeFi Sector Review',
            'Comprehensive review of the DeFi sector performance',
            'June 10, 2023',
            Icons.account_balance,
            Colors.green,
          ),

          const SizedBox(height: 24.0),

          // Saved articles
          _buildSectionHeader('Articles', 4),
          const SizedBox(height: 12.0),
          _buildArticleItem(
            context,
            'Understanding Crypto Market Cycles',
            'Learn about the different market cycles in cryptocurrency and how to identify them.',
            'https://via.placeholder.com/300x150?text=Market+Cycles',
            'May 20, 2023',
          ),
          _buildArticleItem(
            context,
            'The Future of DeFi',
            'Exploring the potential future developments in decentralized finance.',
            'https://via.placeholder.com/300x150?text=DeFi+Future',
            'June 5, 2023',
          ),
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 3,
        onTap: (index) {
          if (index != 3) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 1:
                Navigator.pushReplacementNamed(context, '/charts');
                break;
              case 2:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title, int count) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
          decoration: BoxDecoration(
            color: Colors.blue.withAlpha(50),
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Text(
            '$count items',
            style: const TextStyle(
              color: Colors.blue,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisItem(
    BuildContext context,
    String title,
    String description,
    String date,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16.0),
        leading: Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: color.withAlpha(50),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16.0,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4.0),
            Text(
              description,
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14.0,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8.0),
            Text(
              date,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12.0,
              ),
            ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onPressed: () {
            // Show options menu
          },
        ),
        onTap: () {
          // Open analysis details
        },
      ),
    );
  }

  Widget _buildArticleItem(
    BuildContext context,
    String title,
    String description,
    String imageUrl,
    String date,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Article image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12.0),
              topRight: Radius.circular(12.0),
            ),
            child: Image.network(
              imageUrl,
              height: 150,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150,
                  width: double.infinity,
                  color: Colors.grey[800],
                  child: const Icon(Icons.image_not_supported, size: 50, color: Colors.white),
                );
              },
            ),
          ),

          // Article content
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16.0,
                  ),
                ),
                const SizedBox(height: 8.0),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 14.0,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      date,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12.0,
                      ),
                    ),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.share, color: Colors.white, size: 20),
                          onPressed: () {
                            // Share article
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.bookmark, color: Colors.blue, size: 20),
                          onPressed: () {
                            // Remove from saved
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

