import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentiment_history_model.dart';
import '../utils/arima.dart';
import 'sentiment_prediction_service.dart';

/// Service for predicting future market sentiment using ARIMA model
class ArimaPredictionService {
  // Original service for delegating methods
  final SentimentPredictionService _originalService = SentimentPredictionService();
  // Keys for storing ARIMA model parameters
  static const String _arimaParamsKey = 'arima_model_params';
  static const String _arimaTimestampKey = 'arima_model_timestamp';
  static const String _arimaPredictionsKey = 'arima_predictions';
  static const String _arimaMetricsHashKey = 'arima_metrics_hash';

  // Default ARIMA parameters
  static const int _defaultP = 1; // AR order
  static const int _defaultD = 1; // Differencing order
  static const int _defaultQ = 1; // MA order

  // Cache duration for ARIMA model (1 hour)
  static const int _modelCacheDurationMs = 60 * 60 * 1000;

  // ARIMA model instance
  ArimaModel? _arimaModel;

  /// Get or create an ARIMA model
  Future<ArimaModel> _getArimaModel() async {
    // If we already have a model, return it
    if (_arimaModel != null) {
      return _arimaModel!;
    }

    // Try to load model parameters from cache
    final params = await _loadModelParameters();

    if (params != null) {
      // Create model from cached parameters
      _arimaModel = ArimaModel.withCoefficients(
        p: params['p'],
        d: params['d'],
        q: params['q'],
        arCoefficients: List<double>.from(params['ar_coefficients']),
        maCoefficients: List<double>.from(params['ma_coefficients']),
        intercept: params['intercept'],
      );
      debugPrint('Loaded ARIMA model from cache: (${_arimaModel!.p},${_arimaModel!.d},${_arimaModel!.q})');
    } else {
      // Create a new default model
      _arimaModel = ArimaModel(p: _defaultP, d: _defaultD, q: _defaultQ);
      debugPrint('Created new ARIMA model: (${_arimaModel!.p},${_arimaModel!.d},${_arimaModel!.q})');
    }

    return _arimaModel!;
  }

  /// Load ARIMA model parameters from cache
  Future<Map<String, dynamic>?> _loadModelParameters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final paramsJson = prefs.getString(_arimaParamsKey);
      final timestamp = prefs.getInt(_arimaTimestampKey);

      // Check if we have cached parameters and they're not expired
      if (paramsJson != null && timestamp != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - timestamp < _modelCacheDurationMs) {
          final params = jsonDecode(paramsJson);
          debugPrint('Found valid ARIMA model parameters in cache');
          return params;
        } else {
          debugPrint('Cached ARIMA model parameters expired');
        }
      }
    } catch (e) {
      debugPrint('Error loading ARIMA model parameters: $e');
    }

    return null;
  }

  /// Save ARIMA model parameters to cache
  Future<void> _saveModelParameters(ArimaModel model) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final params = model.getParameters();
      final paramsJson = jsonEncode(params);

      await prefs.setString(_arimaParamsKey, paramsJson);
      await prefs.setInt(_arimaTimestampKey, DateTime.now().millisecondsSinceEpoch);

      debugPrint('Saved ARIMA model parameters to cache');
    } catch (e) {
      debugPrint('Error saving ARIMA model parameters: $e');
    }
  }

  /// Train the ARIMA model on historical data
  Future<void> _trainArimaModel(List<double> historicalValues) async {
    try {
      // Log the historical values for debugging
      debugPrint('Historical values for ARIMA training: $historicalValues');

      // Make sure we have enough data
      if (historicalValues.length < 3) {
        debugPrint('Not enough historical data for ARIMA training (need at least 3, got ${historicalValues.length})');
        return;
      }

      // Check for invalid values
      bool hasInvalidValues = historicalValues.any((value) => value.isNaN || value.isInfinite);
      if (hasInvalidValues) {
        debugPrint('Historical data contains invalid values (NaN or Infinity)');
        return;
      }

      // Check if all values are the same
      bool allSameValues = historicalValues.toSet().length == 1;
      if (allSameValues) {
        debugPrint('All historical values are the same (${historicalValues.first}), ARIMA may not be effective');
        // We'll still train the model, but with a warning
      }

      final model = await _getArimaModel();

      // Train the model
      model.train(historicalValues);

      // Save the trained model parameters
      await _saveModelParameters(model);

      debugPrint('Trained ARIMA model on ${historicalValues.length} data points');
    } catch (e) {
      debugPrint('Error training ARIMA model: $e');
    }
  }

  /// Calculate a hash of the metrics to detect changes
  String _calculateMetricsHash(Map<String, double> metrics) {
    // Sort the keys to ensure consistent order
    final sortedKeys = metrics.keys.toList()..sort();

    // Create a string representation of the metrics
    final buffer = StringBuffer();

    // Add the date to ensure predictions are stable for the same day
    // but can change between days
    final now = DateTime.now();
    final dateKey = '${now.year}-${now.month}-${now.day}';
    buffer.write('date:$dateKey;');

    // Add a minute-based component to force updates more frequently
    // This will make predictions update at least every 5 minutes
    final minuteGroup = (now.minute / 5).floor(); // Updates every 5 minutes
    buffer.write('time:$minuteGroup;');

    // Add hour component to ensure updates at least every hour
    buffer.write('hour:${now.hour};');

    for (final key in sortedKeys) {
      // Round to 2 decimal places to be more sensitive to changes
      // while still avoiding noise from tiny fluctuations
      final roundedValue = (metrics[key]! * 100).round() / 100;
      buffer.write('$key:$roundedValue;');
    }

    final result = buffer.toString();
    debugPrint('Calculated metrics hash: $result');
    return result;
  }

  /// Check if metrics have changed significantly since last prediction
  Future<bool> _haveMetricsChanged(Map<String, double> currentMetrics) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastMetricsHash = prefs.getString(_arimaMetricsHashKey);

      // If no previous hash exists, we need to generate new predictions
      if (lastMetricsHash == null) {
        debugPrint('No previous metrics hash found, metrics considered changed');
        return true;
      }

      // Calculate current hash
      final currentHash = _calculateMetricsHash(currentMetrics);

      // Check if the hash has changed
      final hasChanged = currentHash != lastMetricsHash;

      // Log the result
      if (hasChanged) {
        debugPrint('Metrics have changed - will generate new predictions');
        debugPrint('Previous hash: $lastMetricsHash');
        debugPrint('Current hash: $currentHash');
      } else {
        debugPrint('Metrics have not changed - will use cached predictions');
      }

      // Check if we have cached predictions
      final cachedPredictions = await _getArimaCachedPredictions();
      if (cachedPredictions == null || cachedPredictions.isEmpty) {
        debugPrint('No cached predictions found, will generate new ones regardless of metrics');
        return true;
      }

      return hasChanged;
    } catch (e) {
      debugPrint('Error checking if metrics have changed: $e');
      return true; // Assume changed on error
    }
  }

  /// Save ARIMA predictions to cache
  Future<void> _saveArimaPredictions(List<SentimentHistoryEntry> predictions, Map<String, double> metrics) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert predictions to JSON
      final jsonList = predictions.map((entry) => entry.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      // Save predictions
      await prefs.setString(_arimaPredictionsKey, jsonString);

      // Save metrics hash
      final metricsHash = _calculateMetricsHash(metrics);
      await prefs.setString(_arimaMetricsHashKey, metricsHash);

      debugPrint('Saved ${predictions.length} ARIMA predictions to cache with metrics hash: $metricsHash');
    } catch (e) {
      debugPrint('Error saving ARIMA predictions to cache: $e');
    }
  }

  /// Get cached ARIMA predictions
  Future<List<SentimentHistoryEntry>?> _getArimaCachedPredictions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_arimaPredictionsKey);

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        final predictions = jsonList
            .map((json) => SentimentHistoryEntry.fromJson(json as Map<String, dynamic>))
            .toList();

        debugPrint('Retrieved ${predictions.length} ARIMA predictions from cache');
        return predictions;
      }
    } catch (e) {
      debugPrint('Error getting cached ARIMA predictions: $e');
    }

    return null;
  }

  /// Get historical data from the original service
  Future<SentimentHistory> getHistoricalData() async {
    return await _originalService.getHistoricalData();
  }

  /// Predict future sentiment values using ARIMA
  Future<List<SentimentHistoryEntry>> predictFutureSentiment(
    int daysAhead,
    [Map<String, double>? currentMetrics]
  ) async {
    debugPrint('ARIMA Prediction Service: Starting prediction for $daysAhead days ahead');

    // Force update every 30 minutes regardless of metrics changes
    final now = DateTime.now();
    final forceUpdate = now.minute % 30 == 0;

    if (forceUpdate) {
      debugPrint('ARIMA Prediction Service: Forcing update due to 30-minute interval');
    }

    // If metrics are provided and we're not forcing an update, check if they've changed
    if (currentMetrics != null && !forceUpdate) {
      final metricsChanged = await _haveMetricsChanged(currentMetrics);

      // If metrics haven't changed, try to use cached predictions
      if (!metricsChanged) {
        final cachedPredictions = await _getArimaCachedPredictions();
        if (cachedPredictions != null && cachedPredictions.length >= daysAhead) {
          debugPrint('ARIMA Prediction Service: Using cached predictions as metrics have not changed');
          return cachedPredictions.take(daysAhead).toList();
        } else {
          debugPrint('ARIMA Prediction Service: No valid cached predictions found, generating new ones');
        }
      } else {
        debugPrint('ARIMA Prediction Service: Metrics have changed, generating new predictions');
      }
    }

    // Get historical data
    final history = await getHistoricalData();

    debugPrint('ARIMA Prediction Service: Generating new predictions for the next $daysAhead days');
    debugPrint('ARIMA Prediction Service: Historical data points available: ${history.entries.length}');

    // Log the historical data for debugging
    if (history.entries.isNotEmpty) {
      final values = history.entries.map((e) => e.value).toList();
      debugPrint('ARIMA Prediction Service: Historical values: $values');
    }

    // Need at least 3 data points for ARIMA
    if (history.entries.length < 3) {
      debugPrint('ARIMA Prediction Service: Not enough historical data for ARIMA prediction, falling back to default method');
      return await _originalService.predictFutureSentiment(daysAhead, currentMetrics);
    }

    // If we have exactly 3-4 data points, add a warning
    if (history.entries.length < 5) {
      debugPrint('ARIMA Prediction Service: Warning - only ${history.entries.length} data points available, predictions may be less accurate');
    }

    try {
      // Sort entries by date (oldest first for ARIMA)
      history.entries.sort((a, b) => a.date.compareTo(b.date));

      // Extract values for ARIMA
      final values = history.entries.map((e) => e.value).toList();

      // Train the ARIMA model
      await _trainArimaModel(values);

      // Get the trained model
      final arimaModel = await _getArimaModel();

      // Generate forecasts
      final forecasts = arimaModel.forecast(daysAhead);

      // Create prediction entries
      final predictions = <SentimentHistoryEntry>[];
      final today = DateTime.now();

      // Calculate volatility for confidence estimation
      double volatility = 0;
      if (history.entries.length >= 5) {
        final recentValues = history.entries.take(5).map((e) => e.value).toList();
        final mean = recentValues.reduce((a, b) => a + b) / recentValues.length;
        volatility = recentValues.map((v) => (v - mean) * (v - mean)).reduce((a, b) => a + b) / recentValues.length;
        volatility = sqrt(volatility);
      }

      // Create prediction entries with confidence metrics
      for (int i = 0; i < daysAhead; i++) {
        final futureDate = today.add(Duration(days: i + 1));

        // Clamp the forecast to valid range
        double forecastValue = forecasts[i].clamp(0.0, 100.0);

        // Use a deterministic approach for stable predictions
        // We'll use the date and forecast value to create a stable seed
        final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
        final monthDay = today.month * 100 + today.day; // More stable than just day of year
        final seed = (monthDay * 10000 + dayOfYear * 100 + i * 10).toInt();
        final random = Random(seed);

        // Add small variation based on volatility and prediction distance
        // The further the prediction, the more variation we allow
        double noiseFactor = 0.0;

        // Only add noise if we have enough data points
        if (history.entries.length >= 5) {
          // Scale noise based on prediction distance
          noiseFactor = (i + 1) / daysAhead * 0.3; // Reduced from 0.5 for more stability

          // Apply noise, scaled by volatility
          double noise = (random.nextDouble() * 2 - 1) * volatility * noiseFactor;

          // Apply noise to forecast, but with reduced impact
          forecastValue = (forecastValue + noise * 0.7).clamp(0.0, 100.0);
        }

        // Calculate confidence (decreases with prediction distance)
        // Base confidence depends on how much data we have
        double confidenceBase = 0.0;
        if (history.entries.length >= 10) {
          confidenceBase = 0.85; // High confidence with lots of data
        } else if (history.entries.length >= 7) {
          confidenceBase = 0.75; // Medium confidence
        } else if (history.entries.length >= 5) {
          confidenceBase = 0.65; // Lower confidence
        } else {
          confidenceBase = 0.55; // Minimal confidence with little data
        }

        // Confidence decays with distance into the future
        final distanceFactor = (i + 1) / daysAhead;
        final confidenceDecay = pow(1 - distanceFactor, 1.2); // Reduced from 1.5 for more gradual decay
        final confidence = (confidenceBase * (0.8 + 0.2 * confidenceDecay)).clamp(0.3, 0.9);

        // Calculate trend direction based on previous and current forecast
        String trendDirection = "stable";
        if (i > 0) {
          double diff = forecastValue - predictions[i-1].value;
          if (diff > 1.0) {
            trendDirection = "up";
          } else if (diff < -1.0) {
            trendDirection = "down";
          }
        } else if (history.entries.isNotEmpty) {
          double diff = forecastValue - history.entries.last.value;
          if (diff > 1.0) {
            trendDirection = "up";
          } else if (diff < -1.0) {
            trendDirection = "down";
          }
        }

        debugPrint('ARIMA prediction for day ${i+1} ($futureDate): $forecastValue (confidence: ${(confidence * 100).toStringAsFixed(1)}%, trend: $trendDirection)');

        // Create metrics map with prediction metadata
        final metrics = <String, double>{
          'confidence': (confidence * 100).clamp(30.0, 90.0),
          'volatility': volatility,
          'data_points': history.entries.length.toDouble(),
          'noise_factor': noiseFactor * 100,
        };

        predictions.add(SentimentHistoryEntry(
          date: futureDate,
          value: forecastValue,
          metrics: metrics,
        ));
      }

      // Cache the predictions if metrics are provided
      if (currentMetrics != null) {
        await _saveArimaPredictions(predictions, currentMetrics);
      }

      return predictions;
    } catch (e) {
      debugPrint('Error in ARIMA prediction: $e');
      debugPrint('Falling back to default prediction method');

      // Fall back to the original service implementation
      return await _originalService.predictFutureSentiment(daysAhead, currentMetrics);
    }
  }
}
