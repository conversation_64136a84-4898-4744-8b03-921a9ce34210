import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/trading_simulator_models.dart' as models;
import '../services/trading_simulator_service.dart';
import '../models/trading_simulator_models.dart' show TimeFrame;

class TradingSimulatorProvider extends ChangeNotifier {
  final TradingSimulatorService _simulatorService = TradingSimulatorService();
  final math.Random _random = math.Random();
  
  // State
  List<models.CandleData> _currentCandles = [];
  List<models.CandleData> _allCandles = [];
  List<models.CandleData> _nextChartCandles = []; // Предзагруженные свечи для следующего раунда
  models.SimulatorMode _mode = models.SimulatorMode.infinitePatterns;
  double _balance = 1000.0;
  double _leverage = 10.0;
  String _difficulty = 'Medium'; // Add difficulty state
  int _roundsPlayed = 0;
  int _wins = 0;
  int _deaths = 0;
  int _winStreak = 0;
  double? _entryPrice;
  DateTime? _entryTime;
  bool _isTradeActive = false;
  String _currentSymbol = 'BTCUSDT';
  String _currentTimeframe = '1h';
  double _dragOffset = 0.0; // для прокрутки графика
  int _visibleCandlesCount = 30; // сколько свечей видно на экране
  double? _lastTradeProfit; // прибыль/убыток последней сделки
  String _nextSymbol = ''; // Символ для следующего раунда
  String _nextTimeframe = ''; // Таймфрейм для следующего раунда
  bool _isLoadingError = false; // Флаг ошибки загрузки данных
  
  // История использованных сценариев для предотвращения повторений
  final Set<String> _usedScenarios = {};
  
  // Список доступных символов для custom mode
  final List<String> _availableSymbols = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'XRPUSDT',
    'ADAUSDT', 'AVAXUSDT', 'DOGEUSDT', 'MATICUSDT', 'DOTUSDT',
    'LTCUSDT', 'UNIUSDT', 'ATOMUSDT', 'MANAUSDT', 'SANDUSDT',
    'DOTUSDT', 'AAVEUSDT', 'AXSUSDT', 'SHIB', 'DOTUSDT',
  ];
  
  // Seed для генерации сценариев
  int _currentScenarioSeed = 0;

  // Getters
  List<models.CandleData> get currentCandles => _currentCandles;
  models.SimulatorMode get mode => _mode;
  double get balance => _balance;
  double get leverage => _leverage;
  String get difficulty => _difficulty; // Add difficulty getter
  int get roundsPlayed => _roundsPlayed;
  double get winRate => _roundsPlayed > 0 ? (_wins / _roundsPlayed) * 100 : 0;
  int get deaths => _deaths;
  int get winStreak => _winStreak;
  double? get entryPrice => _entryPrice;
  DateTime? get entryTime => _entryTime;
  bool get isTradeActive => _isTradeActive;
  String get currentSymbol => _currentSymbol;
  String get currentTimeframe => _currentTimeframe;
  double get dragOffset => _dragOffset;
  int get visibleCandlesCount => _visibleCandlesCount;
  double? get lastTradeProfit => _lastTradeProfit;
  bool get isLoadingError => _isLoadingError; // Геттер для состояния ошибки загрузки

  // Добавляем геттер для доступных символов
  List<String> get availableSymbols => _availableSymbols;

  // Добавляем геттер для доступных таймфреймов
  List<String> get availableTimeframes => ['30m', '1h', '4h', '1d'];

  // Constructor
  TradingSimulatorProvider() {
    _initializeSimulator();
  }

  // Set initial parameters
  void setInitialParameters({
    required models.SimulatorMode mode,
    required double leverage,
    required double balance,
    String? symbol,
    String? timeframe,
    String? difficulty, // Add difficulty parameter
  }) {
    _mode = mode;
    _leverage = leverage;
    _balance = balance;
    if (symbol != null) _currentSymbol = symbol;
    if (timeframe != null) _currentTimeframe = timeframe;
    if (difficulty != null) _difficulty = difficulty; // Set difficulty
    
    // Очищаем историю использованных сценариев при новой инициализации
    _usedScenarios.clear();
    _currentScenarioSeed = math.Random().nextInt(1000000);
    
    _initializeSimulator();
  }

  // Initialize simulator
  Future<void> _initializeSimulator() async {
    await loadNewChart();
    // Предзагрузка данных для следующего раунда
    if (!_isLoadingError) {
      _preloadNextChart();
    }
  }

  // Load new chart data
  Future<void> loadNewChart() async {
    try {
      _isLoadingError = false; // Сбрасываем состояние ошибки перед новой загрузкой
      notifyListeners();
      
      // Если у нас есть предзагруженные данные, используем их
      if (_nextChartCandles.isNotEmpty) {
        _allCandles = _nextChartCandles;
        _currentSymbol = _nextSymbol;
        _currentTimeframe = _nextTimeframe;
        _nextChartCandles = [];
        
        // Устанавливаем видимые свечи (все кроме последних 7)
        _currentCandles = _allCandles.sublist(0, _allCandles.length - 7);
        _entryPrice = null;
        _entryTime = null;
        _isTradeActive = false;
        _dragOffset = 0.0;
        
        // Сразу начинаем загружать данные для следующего раунда
        _preloadNextChart();
        notifyListeners();
        return;
      }
      
      // Если предзагруженных данных нет, загружаем обычным способом
      final symbols = [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'XRPUSDT',
        'ADAUSDT', 'AVAXUSDT', 'DOGEUSDT', 'MATICUSDT', 'DOTUSDT',
      ];
      final timeframes = ['30m', '1h', '4h', '1d'];
      
      if (_mode == models.SimulatorMode.infinitePatterns) {
        _currentSymbol = symbols[_random.nextInt(symbols.length)];
        _currentTimeframe = timeframes[_random.nextInt(timeframes.length)];
      }
      
      // Получаем свечи через сервис, передавая уровень сложности
      final candles = await _simulatorService.fetchCandles(
        symbol: _currentSymbol.replaceAll('USDT', ''),
        timeFrame: models.TimeFrame.values.firstWhere((tf) => tf.apiValue == _currentTimeframe),
        limit: 400,
        difficulty: _difficulty, 
        seed: _currentScenarioSeed, // Используем seed для воспроизводимости
      );
      
      // Проверяем, что данные были успешно загружены
      if (candles.isEmpty) {
        throw Exception('Не удалось загрузить данные: пустой список свечей');
      }
      
      // Сохраняем текущий сценарий в историю использованных
      final scenarioKey = '${_currentSymbol}_${_currentTimeframe}_${_currentScenarioSeed}';
      _usedScenarios.add(scenarioKey);
      
      _allCandles = candles;
      _currentCandles = candles.sublist(0, candles.length - 7);
      _entryPrice = null;
      _entryTime = null;
      _isTradeActive = false;
      _dragOffset = 0.0;
      
      // Начинаем загружать данные для следующего раунда
      _preloadNextChart();
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading chart: $e');
      _isLoadingError = true;
      notifyListeners();
    }
  }

  // Предзагрузка данных для следующего раунда
  Future<void> _preloadNextChart() async {
    try {
      String nextSymbol;
      String nextTimeframe;
      int nextSeed;
      String scenarioKey;
      
      // Логика выбора следующего сценария зависит от режима
      if (_mode == models.SimulatorMode.infinitePatterns) {
        // Для режима Infinite Patterns - случайные символ и таймфрейм
        final symbols = [
          'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'XRPUSDT',
          'ADAUSDT', 'AVAXUSDT', 'DOGEUSDT', 'MATICUSDT', 'DOTUSDT',
        ];
        final timeframes = ['30m', '1h', '4h', '1d'];
        
        nextSymbol = symbols[_random.nextInt(symbols.length)];
        nextTimeframe = timeframes[_random.nextInt(timeframes.length)];
        
        // Для Infinite Patterns режима тоже увеличиваем энтропию
        nextSeed = _random.nextInt(1000000) + DateTime.now().microsecondsSinceEpoch;
      } else {
        // Для Custom Mode - сохраняем выбранный таймфрейм, но меняем символ только если это RANDOM
        nextTimeframe = _currentTimeframe;
        
        // Если текущий символ не RANDOM, сохраняем его
        if (_currentSymbol != 'RANDOM' && !_currentSymbol.startsWith('RANDOM')) {
          nextSymbol = _currentSymbol;
        } else {
          // Иначе выбираем случайный символ из доступных
          nextSymbol = _availableSymbols[_random.nextInt(_availableSymbols.length)];
        }
        
        // Используем более сложный механизм генерации seed для обеспечения уникальности
        // Комбинируем текущее время, случайное число и другие источники энтропии
        final baseTime = DateTime.now().millisecondsSinceEpoch;
        final randomComponent = _random.nextInt(1000000);
        final mixedSeed = baseTime ^ randomComponent; // XOR для большей диверсификации
        
        // Попытки найти уникальный сценарий с новым механизмом генерации seed
        int maxAttempts = 50;
        int attempts = 0;
        bool foundUnique = false;
        
        do {
          // Увеличиваем энтропию с каждой попыткой
          nextSeed = mixedSeed + attempts * 97 + _random.nextInt(10000);
          
          // Генерируем более уникальный ключ, используя микросекунды и другие компоненты
          final timestamp = DateTime.now().microsecondsSinceEpoch % 100000;
          scenarioKey = '${nextSymbol}_${nextTimeframe}_${nextSeed}_${timestamp}_v2';
          
          attempts++;
          
          // Проверяем, использовался ли этот сценарий ранее
          if (!_usedScenarios.contains(scenarioKey)) {
            foundUnique = true;
            // Добавляем сценарий в историю использованных
            _usedScenarios.add(scenarioKey);
          }
          
          // Защита от бесконечного цикла
          if (attempts >= maxAttempts) {
            // Принудительно создаем уникальный ключ
            nextSeed = DateTime.now().microsecondsSinceEpoch;
            scenarioKey = '${nextSymbol}_${nextTimeframe}_${nextSeed}_forced_${_random.nextInt(9999999)}';
            foundUnique = true;
            _usedScenarios.add(scenarioKey);
            
            // Очищаем историю использованных сценариев, если она стала слишком большой
            if (_usedScenarios.length > 500) {
              debugPrint('Clearing used scenarios cache (size: ${_usedScenarios.length})');
              _usedScenarios.clear();
            }
          }
        } while (!foundUnique);
        
        // Выводим информацию о новом сценарии для отладки
        debugPrint('Generated new scenario: $scenarioKey (attempt $attempts)');
      }
      
      _nextSymbol = nextSymbol;
      _nextTimeframe = nextTimeframe;
      _currentScenarioSeed = nextSeed;
      
      // Загружаем данные для следующего раунда заранее
      _nextChartCandles = await _simulatorService.fetchCandles(
        symbol: _nextSymbol.replaceAll('USDT', ''),
        timeFrame: models.TimeFrame.values.firstWhere((tf) => tf.apiValue == _nextTimeframe),
        limit: 400,
        difficulty: _difficulty,
        seed: _currentScenarioSeed,
      );
      
      // Проверяем, что данные были успешно загружены
      if (_nextChartCandles.isEmpty) {
        throw Exception('Не удалось предзагрузить данные: пустой список свечей');
      }
    } catch (e) {
      debugPrint('Error preloading next chart: $e');
      _nextChartCandles = [];
    }
  }

  // Метод для повторной попытки загрузки данных
  Future<void> retryLoading() async {
    await loadNewChart();
  }

  // Make a trade
  bool makeTrade(models.TradeAction action) {
    if (_isTradeActive || _currentCandles.isEmpty) return false;

    // Set entry point
    final entryCandle = _currentCandles.last;
    _entryPrice = entryCandle.close;
    _entryTime = entryCandle.time;
    _isTradeActive = true;

    // Получаем следующие 7 свечей
    final nextCandles = _allCandles.skip(_currentCandles.length).take(7).toList();
    if (nextCandles.isEmpty) return false;
    final resultCandle = nextCandles.last;

    // Calculate result по последней из 7 свечей
    final priceChange = (resultCandle.close - _entryPrice!) / _entryPrice! * 100;
    final success = (action == models.TradeAction.buy && resultCandle.close > _entryPrice!) ||
                   (action == models.TradeAction.sell && resultCandle.close < _entryPrice!);

    // Calculate profit/loss
    final tradeAmount = _balance * 0.1; // Use 10% of balance for each trade
    final profit = (tradeAmount * (priceChange / 100) * _leverage).abs(); // Use abs for simplicity
    _lastTradeProfit = success ? profit : -profit;

    // Update state
    _balance += _lastTradeProfit!;
    _roundsPlayed++;
    if (success) {
      _wins++;
      _winStreak++;
    } else {
      _winStreak = 0;
    }

    // Add next 7 candles to visible candles
    _currentCandles = [..._currentCandles, ...nextCandles];

    // Check for game over
    if (_balance <= 0 && _mode == models.SimulatorMode.infinitePatterns) {
      _deaths++;
    }

    notifyListeners();
    return success;
  }

  // Start new round
  Future<void> startNewRound() async {
    _isTradeActive = false;
    _lastTradeProfit = null;
    await loadNewChart();
  }

  // Set mode
  void setMode(models.SimulatorMode mode) {
    _mode = mode;
    _balance = 1000.0;
    _roundsPlayed = 0;
    _wins = 0;
    _deaths = 0;
    _winStreak = 0;
    
    // Очищаем историю использованных сценариев при смене режима
    _usedScenarios.clear();
    _currentScenarioSeed = math.Random().nextInt(1000000);
    
    loadNewChart();
    notifyListeners();
  }

  // Set leverage
  void setLeverage(double leverage) {
    _leverage = leverage;
    notifyListeners();
  }

  // Set initial balance
  void setInitialBalance(double balance) {
    _balance = balance;
    notifyListeners();
  }

  // Добавляю метод для обновления dragOffset
  void setDragOffset(double offset) {
    _dragOffset = offset;
    notifyListeners();
  }

  // Добавляю метод для изменения количества видимых свечей
  void setVisibleCandlesCount(int count) {
    _visibleCandlesCount = count;
    notifyListeners();
  }
  
  // Сброс игры с новым начальным балансом
  void resetGame({required double initialBalance}) {
    // Сбрасываем статистику, кроме количества deaths
    _balance = initialBalance;
    _roundsPlayed = 0;
    _wins = 0;
    _winStreak = 0;
    
    // Сбрасываем состояние торговли
    _entryPrice = null;
    _entryTime = null;
    _isTradeActive = false;
    _lastTradeProfit = null;
    _dragOffset = 0.0;
    
    // Загружаем новый чарт
    loadNewChart();
    notifyListeners();
  }
  
  // Пополнение баланса и инкремент смертей
  void topUpBalance(double amount) {
    // Пополняем баланс
    _balance = amount;
    
    // Увеличиваем счетчик смертей
    _deaths++;
    
    // Сбрасываем состояние торговли
    _entryPrice = null;
    _entryTime = null;
    _isTradeActive = false;
    _lastTradeProfit = null;
    _dragOffset = 0.0;
    
    // Загружаем новый чарт
    loadNewChart();
    notifyListeners();
  }

  void selectTimeframe(String timeframe) {
    _currentTimeframe = timeframe;
    // После выбора нового таймфрейма, загружаем новый график
    loadNewChart();
    notifyListeners();
  }

  void selectRandomTimeframe() {
    // ... existing code ...
  }
} 