import 'package:flutter/material.dart';
import '../models/trading_simulator_models.dart' as models;
import 'crypto_trading_simulator_screen.dart';

class InfinitePatternsSettingsScreen extends StatefulWidget {
  const InfinitePatternsSettingsScreen({Key? key}) : super(key: key);

  @override
  State<InfinitePatternsSettingsScreen> createState() => _InfinitePatternsSettingsScreenState();
}

class _InfinitePatternsSettingsScreenState extends State<InfinitePatternsSettingsScreen> {
  double _leverage = 1.0;
  double _initialBalance = 1000.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('Infinite Patterns Settings'),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Leverage Slider
            const Text(
              'Leverage',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Slider(
              value: _leverage,
              min: 1,
              max: 100,
              divisions: 99,
              label: '${_leverage.toInt()}x',
              onChanged: (value) {
                setState(() {
                  _leverage = value;
                });
              },
            ),
            Text(
              '${_leverage.toInt()}x',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Initial Balance Slider
            const Text(
              'Initial Balance',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Slider(
              value: _initialBalance,
              min: 100,
              max: 10000,
              divisions: 99,
              label: '\$${_initialBalance.toInt()}',
              onChanged: (value) {
                setState(() {
                  _initialBalance = value;
                });
              },
            ),
            Text(
              '\$${_initialBalance.toInt()}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Start Button
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CryptoTradingSimulatorScreen(
                      mode: models.SimulatorMode.infinitePatterns,
                      leverage: _leverage,
                      initialBalance: _initialBalance,
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'START',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 