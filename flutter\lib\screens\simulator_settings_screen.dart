import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/trading_simulator_provider.dart';
import '../models/trading_simulator_models.dart' as models;

class SimulatorSettingsScreen extends StatefulWidget {
  const SimulatorSettingsScreen({Key? key}) : super(key: key);

  @override
  State<SimulatorSettingsScreen> createState() => _SimulatorSettingsScreenState();
}

class _SimulatorSettingsScreenState extends State<SimulatorSettingsScreen> {
  double _leverage = 10.0;
  double _initialBalance = 1000.0;
  models.SimulatorMode _selectedMode = models.SimulatorMode.infinitePatterns;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CupertinoColors.systemBackground,
      appBar: AppBar(
        backgroundColor: CupertinoColors.systemBackground,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(CupertinoIcons.back, color: CupertinoColors.label),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Simulator Settings',
          style: TextStyle(
            color: CupertinoColors.label,
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Game Mode Selection
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: CupertinoColors.systemBackground,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: CupertinoColors.systemGrey.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Game Mode',
                  style: TextStyle(
                    color: CupertinoColors.label,
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                CupertinoSlidingSegmentedControl<models.SimulatorMode>(
                  groupValue: _selectedMode,
                  onValueChanged: (value) {
                    if (value != null) {
                      setState(() => _selectedMode = value);
                    }
                  },
                  children: const {
                    models.SimulatorMode.infinitePatterns: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Text('Infinite'),
                    ),
                    models.SimulatorMode.practice: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Text('Practice'),
                    ),
                    models.SimulatorMode.custom: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Text('Custom'),
                    ),
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Leverage Settings
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: CupertinoColors.systemBackground,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: CupertinoColors.systemGrey.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Leverage',
                  style: TextStyle(
                    color: CupertinoColors.label,
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: CupertinoSlider(
                        value: _leverage,
                        min: 1,
                        max: 100,
                        divisions: 99,
                        onChanged: (value) {
                          setState(() => _leverage = value);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${_leverage.toInt()}x',
                      style: const TextStyle(
                        color: CupertinoColors.label,
                        fontSize: 17,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Initial Balance Settings
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: CupertinoColors.systemBackground,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: CupertinoColors.systemGrey.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Initial Balance',
                  style: TextStyle(
                    color: CupertinoColors.label,
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: CupertinoSlider(
                        value: _initialBalance,
                        min: 100,
                        max: 10000,
                        divisions: 99,
                        onChanged: (value) {
                          setState(() => _initialBalance = value);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '\$${_initialBalance.toInt()}',
                      style: const TextStyle(
                        color: CupertinoColors.label,
                        fontSize: 17,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),

          // Start Button
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              final provider = context.read<TradingSimulatorProvider>();
              provider.setMode(_selectedMode);
              provider.setLeverage(_leverage);
              provider.setInitialBalance(_initialBalance);
              Navigator.pop(context);
            },
            child: Container(
              width: double.infinity,
              height: 50,
              decoration: BoxDecoration(
                color: CupertinoColors.activeBlue,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Text(
                  'Start Trading',
                  style: TextStyle(
                    color: CupertinoColors.white,
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 