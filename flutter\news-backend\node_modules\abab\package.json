{"name": "abab", "version": "1.0.4", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "files": ["index.js", "lib/"], "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "jscs . && eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"babel-core": "^6.1.4", "babel-loader": "^6.1.0", "babel-preset-es2015": "^6.1.4", "eslint": "^1.3.1", "jscs": "^2.1.1", "karma": "^0.13.10", "karma-cli": "^0.1.1", "karma-firefox-launcher": "^0.1.6", "karma-mocha": "^0.2.0", "karma-sauce-launcher": "^0.2.14", "karma-webpack": "^1.7.0", "mocha": "^2.2.5", "webpack": "^1.12.2"}}