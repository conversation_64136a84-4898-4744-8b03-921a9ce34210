import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/simple_trading_model.dart';
import 'basic_trading_simulator_screen.dart';
import 'fixed_trading_simulator_screen.dart';
import 'new_settings_screen.dart';

class NewModeSelectionScreen extends StatelessWidget {
  const NewModeSelectionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF000428), Color(0xFF004e92)],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'Crypto Trading Simulator',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 10),
                const Text(
                  'Practice trading without risking real money',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
                const SizedBox(height: 50),
                _buildModeButton(
                  context,
                  title: 'Custom Mode',
                  description: 'Configure your own trading experience',
                  color: Colors.amber,
                  onTap: () {
                    Provider.of<SimpleTradingModel>(context, listen: false)
                        .setGameMode('custom');
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const NewSettingsScreen(),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 20),
                _buildModeButton(
                  context,
                  title: 'Infinite Patterns',
                  description: 'Endless trading with random symbols',
                  color: Colors.green,
                  onTap: () {
                    final model = Provider.of<SimpleTradingModel>(context, listen: false);
                    model.setGameMode('infinite');
                    model.setLeverage(1); // По умолчанию кредитное плечо 1x
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const FixedTradingSimulatorScreen(),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 40),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text(
                    'Back to Games',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModeButton(
    BuildContext context, {
    required String title,
    required String description,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 280,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: color.withOpacity(0.2),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: color, width: 2),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 5),
            Text(
              description,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
