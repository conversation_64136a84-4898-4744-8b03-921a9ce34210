import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:http/http.dart' as http;
import '../models/anti_fomo_simulator_models.dart';
import '../models/trading_simulator_models.dart';
import 'binance_api_service.dart';

class AntiFomoSimulatorService {
  final BinanceApiService _binanceApiService = BinanceApiService();
  
  /// Fetch historical candle data from Binance API
  Future<List<CandleData>> fetchCandles({
    required String symbol,
    required TimeFrame timeFrame,
    int limit = 250,
  }) async {
    try {
      // Get a list of candles from different time periods for more diversity
      final List<List<CandleData>> candidateCharts = [];
      
      // Use BinanceApiService to fetch historical candles - first try standard approach
      final List<CandleData> standardCandles = await _binanceApiService.fetchHistoricalCandles(
        symbol: symbol,
        timeFrame: timeFrame,
        limit: limit,
      );
      candidateCharts.add(standardCandles);
      
      // Try fetching from a different date range for more diversity (if possible)
      try {
        final randomOffset = 30 + math.Random().nextInt(90); // Random offset between 30-120 days
        final List<CandleData> offsetCandles = await _binanceApiService.fetchHistoricalCandlesWithEndTime(
          symbol: symbol,
          timeFrame: timeFrame,
          limit: limit,
          endTime: DateTime.now().subtract(Duration(days: randomOffset)),
        );
        if (offsetCandles.isNotEmpty) {
          candidateCharts.add(offsetCandles);
        }
      } catch (e) {
        // Just continue if this fails
      }
      
      // Try another date range that's even older
      try {
        final randomOffset = 120 + math.Random().nextInt(180); // Random offset between 120-300 days
        final List<CandleData> olderCandles = await _binanceApiService.fetchHistoricalCandlesWithEndTime(
          symbol: symbol,
          timeFrame: timeFrame,
          limit: limit,
          endTime: DateTime.now().subtract(Duration(days: randomOffset)),
        );
        if (olderCandles.isNotEmpty) {
          candidateCharts.add(olderCandles);
        }
      } catch (e) {
        // Just continue if this fails
      }
      
      // If we have multiple candidate charts, randomly select one
      if (candidateCharts.length > 1) {
        return candidateCharts[math.Random().nextInt(candidateCharts.length)];
      } else {
        // If we only have one chart, use it
        return standardCandles;
      }
    } catch (e) {
      throw Exception('Failed to load candles: $e');
    }
  }
  
  /// Get a list of popular trading pairs
  List<String> getPopularTradingPairs() {
    return [
      'BTC', 'ETH', 'BNB', 'SOL', 'XRP', 
      'ADA', 'AVAX', 'DOT', 'MATIC', 'LINK',
      'UNI', 'ATOM', 'LTC', 'DOGE', 'SHIB',
    ];
  }
  
  /// Get a random trading pair
  String getRandomTradingPair() {
    final pairs = getPopularTradingPairs();
    final random = math.Random().nextInt(pairs.length);
    return pairs[random];
  }
  
  /// Generate a unique market scenario for more diverse trading experience
  Map<String, dynamic> generateMarketScenario() {
    // Сценарии теперь равновероятны
    final scenarios = MarketScenario.values;
    final scenario = scenarios[math.Random().nextInt(scenarios.length)];

    // Добавляем больше случайных параметров для уникальности
    final context = generateMarketContext();
    context['scenario'] = scenario;
    // Дополнительные случайные параметры
    context['randomSeed'] = math.Random().nextInt(1000000);
    context['eventIntensity'] = math.Random().nextDouble() * 100;
    context['newsType'] = [
      'None', 'Positive', 'Negative', 'Mixed', 'Rumor', 'Regulation', 'Earnings', 'Macro', 'Crypto', 'Unexpected'
    ][math.Random().nextInt(10)];
    return context;
  }
  
  /// Generate market context data for more diverse scenarios
  Map<String, dynamic> generateMarketContext() {
    // Base market properties
    final marketVolatility = math.Random().nextDouble() * 100; // 0-100 scale
    final marketLiquidity = math.Random().nextDouble() * 100; // 0-100 scale
    final newsImpact = math.Random().nextDouble() * 100; // 0-100 scale
    final technicalTrend = (math.Random().nextDouble() * 200) - 100; // -100 to +100 scale
    
    // Market cycle (0 = bottom, 50 = middle, 100 = top)
    final marketCycle = math.Random().nextDouble() * 100;
    
    // Special events (chance of occurrence)
    final hasEarningsRelease = math.Random().nextDouble() < 0.15;
    final hasRegulationNews = math.Random().nextDouble() < 0.10;
    final hasTechnicalBreakout = math.Random().nextDouble() < 0.20;
    final hasInstitutionalActivity = math.Random().nextDouble() < 0.25;
    
    // Market regime type
    final possibleRegimes = [
      'Bull Market', 'Bear Market', 'Sideways Market', 
      'Early Bull', 'Late Bull', 'Early Bear', 'Recovery',
      'Accumulation', 'Distribution', 'Capitulation'
    ];
    final marketRegime = possibleRegimes[math.Random().nextInt(possibleRegimes.length)];
    
    // Sector rotation
    final sectorPerformance = {
      'Tech': (math.Random().nextDouble() * 20) - 5,      // -5% to +15%
      'Finance': (math.Random().nextDouble() * 20) - 5,   // -5% to +15%
      'Energy': (math.Random().nextDouble() * 20) - 5,    // -5% to +15%
      'Healthcare': (math.Random().nextDouble() * 20) - 5,// -5% to +15%
      'Utilities': (math.Random().nextDouble() * 20) - 5, // -5% to +15%
    };
    
    // Return context data
    return {
      'volatility': marketVolatility,
      'liquidity': marketLiquidity,
      'newsImpact': newsImpact,
      'technicalTrend': technicalTrend,
      'marketCycle': marketCycle,
      'hasEarningsRelease': hasEarningsRelease,
      'hasRegulationNews': hasRegulationNews,
      'hasTechnicalBreakout': hasTechnicalBreakout,
      'hasInstitutionalActivity': hasInstitutionalActivity,
      'marketRegime': marketRegime,
      'sectorPerformance': sectorPerformance,
    };
  }
  
  /// Get a random timeframe
  TimeFrame getRandomTimeFrame() {
    final timeframes = TimeFrame.values;
    final random = math.Random().nextInt(timeframes.length);
    return timeframes[random];
  }
  
  /// Calculate the optimal action based on market conditions
  FomoTradeAction calculateOptimalAction(
    List<CandleData> visibleCandles,
    List<CandleData> futureCandles,
  ) {
    // Enhanced algorithm with more randomness and diverse scenarios
    
    // Extract the recent trend from visible candles (last 5-15 candles with random length)
    final trendLength = 5 + math.Random().nextInt(11); // Random length between 5-15
    final recentCandles = visibleCandles.length > trendLength 
        ? visibleCandles.sublist(visibleCandles.length - trendLength)
        : visibleCandles;
    
    // Look at future price movement with some randomized weighting
    final currentPrice = visibleCandles.last.close;
    final futurePrice = futureCandles.last.close;
    final priceChange = (futurePrice - currentPrice) / currentPrice;
    
    // Calculate trend strength based on recent candles
    double upCount = 0;
    double downCount = 0;
    
    for (int i = 0; i < recentCandles.length - 1; i++) {
      final priceMove = recentCandles[i+1].close - recentCandles[i].close;
      if (priceMove > 0) {
        upCount++;
      } else if (priceMove < 0) {
        downCount++;
      }
    }
    
    // Calculate the trend strength (-1 to 1 scale)
    final trendStrength = (upCount - downCount) / recentCandles.length;
    
    // Enhanced randomness to simulate more diverse market unpredictability
    final randomFactor = math.Random().nextDouble() * 0.08 - 0.04; // -4% to +4% random element (doubled)
    final adjustedPriceChange = priceChange + randomFactor;
    
    // More diverse volatility-based confidence factor with wider range
    double volatility = 0;
    for (final candle in recentCandles) {
      volatility += (candle.high - candle.low) / candle.low;
    }
    volatility = volatility / recentCandles.length;
    
    // Add random volatility spike in some cases
    if (math.Random().nextDouble() < 0.2) { // 20% chance of volatility spike
      volatility *= 1.5 + math.Random().nextDouble();
    }
    
    final confidenceFactor = math.max(0.4, math.min(1.6, 1.0 / (volatility * 10 + 0.5)));
    
    // Final price prediction with confidence adjustment
    final finalPrediction = adjustedPriceChange * confidenceFactor;
    
    // Add more diverse special case scenarios
    final scenarioRoll = math.Random().nextDouble();
    if (scenarioRoll < 0.15) { // Increased from 5% to 15% chance of special scenarios
      // Special market scenarios
      if (scenarioRoll < 0.05) {
        // Flash crash/pump scenario
        return math.Random().nextBool() ? FomoTradeAction.sell : FomoTradeAction.buy;
      } else if (scenarioRoll < 0.10) {
        // Sideways choppy market - hold is often best
        return FomoTradeAction.hold;
      } else {
        // Fake breakout/breakdown - counter-intuitive action
        return trendStrength > 0 ? FomoTradeAction.sell : FomoTradeAction.buy;
      }
    }
    
    // Enhanced pattern detection with more patterns
    final patternType = math.Random().nextInt(5); // 5 different pattern types
    bool patternDetected = false;
    
    if (recentCandles.length >= 5) {
      final last5Closes = recentCandles.sublist(recentCandles.length - 5).map((c) => c.close).toList();
      
      switch (patternType) {
        case 0:
          // Double bottom/top pattern
          if ((last5Closes[0] > last5Closes[1] && 
               last5Closes[1] < last5Closes[2] && 
               last5Closes[2] > last5Closes[3] && 
               last5Closes[3] < last5Closes[4]) ||
              (last5Closes[0] < last5Closes[1] && 
               last5Closes[1] > last5Closes[2] && 
               last5Closes[2] < last5Closes[3] && 
               last5Closes[3] > last5Closes[4])) {
            patternDetected = true;
          }
          break;
          
        case 1:
          // Head and shoulders pattern (simplified)
          if (last5Closes[0] < last5Closes[1] && 
              last5Closes[1] > last5Closes[2] &&
              last5Closes[2] < last5Closes[3] &&
              last5Closes[3] > last5Closes[4] &&
              last5Closes[1] < last5Closes[3]) {
            patternDetected = true;
          }
          break;
          
        case 2:
          // Breakout pattern
          final min = last5Closes.sublist(0, 4).reduce(math.min);
          final max = last5Closes.sublist(0, 4).reduce(math.max);
          if (last5Closes[4] > max * 1.02 || last5Closes[4] < min * 0.98) {
            patternDetected = true;
          }
          break;
          
        case 3:
          // Three pushes pattern
          if (last5Closes[0] < last5Closes[1] && 
              last5Closes[1] > last5Closes[2] &&
              last5Closes[2] < last5Closes[3] &&
              last5Closes[3] > last5Closes[2] &&
              last5Closes[3] < last5Closes[1]) {
            patternDetected = true;
          }
          break;
          
        case 4:
          // Volume pattern (simulated)
          if (math.Random().nextDouble() < 0.25) {
            patternDetected = true;
          }
          break;
      }
    }
    
    // Decision with more diverse thresholds and pattern influence
    if (patternDetected) {
      // Different actions based on pattern type
      switch (patternType) {
        case 0: // Double bottom/top
          return trendStrength > 0 ? FomoTradeAction.sell : FomoTradeAction.buy;
        case 1: // Head and shoulders
          return FomoTradeAction.sell;
        case 2: // Breakout
          {
            final last5Closes = recentCandles.sublist(recentCandles.length - 5).map((c) => c.close).toList();
            return last5Closes.last > last5Closes.first ? FomoTradeAction.buy : FomoTradeAction.sell;
          }
        case 3: // Three pushes
          return FomoTradeAction.sell;
        default:
          return math.Random().nextBool() ? FomoTradeAction.buy : FomoTradeAction.sell;
      }
    } else if (finalPrediction > 0.03 + (math.Random().nextDouble() * 0.02) || (trendStrength > 0.5 && finalPrediction > 0.01)) {
      // Strong upward prediction or moderate up with strong trend
      return FomoTradeAction.buy;
    } else if (finalPrediction < -0.03 - (math.Random().nextDouble() * 0.02) || (trendStrength < -0.5 && finalPrediction < -0.01)) {
      // Strong downward prediction or moderate down with strong trend
      return FomoTradeAction.sell;
    } else {
      // Price won't change much or unclear direction
      return FomoTradeAction.hold;
    }
  }
  
  /// Calculate trade outcome based on user action and optimal action
  double calculateTradeOutcome({
    required FomoTradeAction userAction,
    required FomoTradeAction optimalAction,
    required int socialHypeLevel,
    required TraderRole role,
    required DifficultyLevel difficulty,
  }) {
    // Base profit/loss percentage based on whether the action was correct
    double basePercentage;
    
    // Simplified logic: correct action = positive outcome, wrong action = negative outcome
    // with varied degrees based on how wrong the action was
    if (userAction == optimalAction) {
      // Correct action: good profit
      basePercentage = 4.0 + (math.Random().nextDouble() * 2.0); // 4-6%
    } 
    // Handle HOLD specially
    else if (userAction == FomoTradeAction.hold) {
      if (optimalAction == FomoTradeAction.buy) {
        // HOLD when should BUY - missed opportunity but not a loss
        basePercentage = 0.2 + (math.Random().nextDouble() * 0.8); // 0.2-1.0%
      } else if (optimalAction == FomoTradeAction.sell) {
        // HOLD when should SELL - price dropping, bad outcome
        basePercentage = -4.0 - (math.Random().nextDouble() * 2.0); // -4 to -6%
      } else {
        // Should never happen but included for completeness
        basePercentage = 0.0;
      }
    }
    // BUY vs SELL - completely opposite actions
    else if ((userAction == FomoTradeAction.buy && optimalAction == FomoTradeAction.sell) ||
             (userAction == FomoTradeAction.sell && optimalAction == FomoTradeAction.buy)) {
      // Completely wrong direction - largest loss
      basePercentage = -8.0 - (math.Random().nextDouble() * 4.0); // -8 to -12%
    }
    // BUY/SELL when should HOLD
    else {
      // Taking action when should hold - moderate loss
      basePercentage = -3.0 - (math.Random().nextDouble() * 1.5); // -3 to -4.5%
    }
    
    // Add some randomness for market variability (but reduced from before)
    final marketVariability = 0.7 + (math.Random().nextDouble() * 0.6); // 0.7-1.3 multiplier
    basePercentage *= marketVariability;
    
    // Adjust based on social hype - higher hype means more extreme outcomes
    double hypeImpact = (socialHypeLevel / 100) * 0.5; // 0-0.5 range based on hype
    double hypeMultiplier = 1.0 + hypeImpact; // 1.0-1.5 range
    basePercentage *= hypeMultiplier;
    
    // Apply difficulty multiplier
    basePercentage *= difficulty.riskMultiplier;
    
    // Apply role-specific multipliers
    double finalPercentage;
    switch (role) {
      case TraderRole.novice:
        // Novice: high leverage, high risk
        finalPercentage = basePercentage * 4.0; // 4x multiplier
        break;
      case TraderRole.whale:
        // Whale: lower percentage but applied to larger capital
        finalPercentage = basePercentage * 0.7; // 0.7x multiplier
        break;
      case TraderRole.darkpool:
        // Darkpool: higher returns but trap mechanism on high hype
        if (socialHypeLevel > 70 && userAction == FomoTradeAction.buy && math.Random().nextDouble() < 0.3) {
          // Trap: sudden reversal (30% chance at high hype)
          finalPercentage = -basePercentage.abs() * 1.5; // Reverse and amplify the loss
        } else {
          finalPercentage = basePercentage * 1.2; // 1.2x gain normally
        }
        break;
    }
    
    return finalPercentage;
  }
  
  /// Generate a list of chat messages based on social hype level
  List<ChatMessage> generateChatMessages(int hypeLevel, int count) {
    final List<ChatMessage> messages = [];
    final now = DateTime.now();
    
    // Generate market context for more diverse and coherent messages
    final marketContext = generateMarketContext();
    final marketRegime = marketContext['marketRegime'] as String;
    final isPositiveMarket = marketContext['technicalTrend'] > 20;
    final isNegativeMarket = marketContext['technicalTrend'] < -20;
    final isVolatile = marketContext['volatility'] > 70;
    
    // Special events to reference in chat
    final hasBreakout = marketContext['hasTechnicalBreakout'] as bool;
    final hasNews = marketContext['hasRegulationNews'] as bool;
    final hasEarnings = marketContext['hasEarningsRelease'] as bool;
    
    // Additional message themes based on context
    List<String> additionalThemes = [];
    
    if (isVolatile) {
      additionalThemes.add('volatility');
    }
    
    if (hasBreakout) {
      additionalThemes.add('breakout');
    }
    
    if (hasNews) {
      additionalThemes.add('regulation');
    }
    
    if (hasEarnings) {
      additionalThemes.add('earnings');
    }
    
    if (marketRegime == 'Accumulation') {
      additionalThemes.add('accumulation');
    } else if (marketRegime == 'Distribution') {
      additionalThemes.add('distribution');
    } else if (marketRegime == 'Capitulation') {
      additionalThemes.add('capitulation');
    }
    
    for (int i = 0; i < count; i++) {
      // Decreased positive probability to get more negative messages
      // With additional context influence
      double positiveChance = 0.35 + (hypeLevel / 300);
      
      // Market regime affects message sentiment
      if (isPositiveMarket) positiveChance += 0.1;
      if (isNegativeMarket) positiveChance -= 0.15;
      
      final isPositive = math.Random().nextDouble() < positiveChance;
      
      // Generate message with context
      String message;
      if (additionalThemes.isNotEmpty && math.Random().nextDouble() < 0.4) {
        // Use special theme for 40% of messages when available
        final theme = additionalThemes[math.Random().nextInt(additionalThemes.length)];
        message = isPositive 
            ? ChatMessage.generateThematicPositiveMessage(theme, hypeLevel)
            : ChatMessage.generateThematicNegativeMessage(theme, hypeLevel);
      } else {
        // Standard message generation
        message = isPositive
            ? ChatMessage.generatePositiveMessage(hypeLevel)
            : ChatMessage.generateNegativeMessage(hypeLevel);
      }
      
      messages.add(ChatMessage(
        username: ChatMessage.generateRandomUsername(),
        message: message,
        timestamp: now.subtract(Duration(seconds: i * 5)),
        isPositive: isPositive,
        likes: math.Random().nextInt(100),
      ));
    }
    
    return messages;
  }
  
  /// Generate a news ticker item
  TickerNewsItem generateNewsItem({bool forceSpecial = false}) {
    return TickerNewsItem.generateRandom(forceSpecial: forceSpecial);
  }
  
  /// Format currency value for display
  String formatCurrency(double value) {
    if (value.abs() >= 1000000) {
      return '\$${(value / 1000000).toStringAsFixed(2)}M';
    } else if (value.abs() >= 1000) {
      return '\$${(value / 1000).toStringAsFixed(2)}K';
    } else {
      return '\$${value.toStringAsFixed(2)}';
    }
  }
  
  /// Format percentage for display
  String formatPercentage(double percentage) {
    final sign = percentage >= 0 ? '+' : '';
    return '$sign${percentage.toStringAsFixed(2)}%';
  }
}
