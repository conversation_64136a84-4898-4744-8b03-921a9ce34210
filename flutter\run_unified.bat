@echo off
setlocal

if "%1"=="" goto :help
if "%1"=="-h" goto :help
if "%1"=="--help" goto :help

set MODE=%1
set DEVICE_ARG=

if "%2"=="-d" if not "%3"=="" set DEVICE_ARG=-d %3

if /i "%MODE%"=="full" goto :full
if /i "%MODE%"=="desktop" goto :full
if /i "%MODE%"=="lite" goto :lite
if /i "%MODE%"=="mobile" goto :lite

echo ❌ Неизвестный режим: %MODE%
echo Используйте: full, desktop, lite, или mobile
goto :help

:full
echo 🚀 Запуск полной версии TMM...
flutter run --dart-define=APP_MODE=full lib/main_unified.dart %DEVICE_ARG%
goto :end

:lite
echo 📱 Запуск лайт версии TMM...
flutter run --dart-define=APP_MODE=lite lib/main_unified.dart %DEVICE_ARG%
goto :end

:help
echo Использование: run_unified.bat [РЕЖИМ] [УСТРОЙСТВО]
echo.
echo РЕЖИМ:
echo   full, desktop    - Полная версия приложения
echo   lite, mobile     - Лайт версия приложения
echo.
echo УСТРОЙСТВО (опционально):
echo   -d [device_id]   - Запуск на конкретном устройстве
echo   -h, --help       - Показать эту справку
echo.
echo Примеры:
echo   run_unified.bat full
echo   run_unified.bat lite
echo   run_unified.bat full -d chrome
echo   run_unified.bat lite -d emulator-5554

:end
