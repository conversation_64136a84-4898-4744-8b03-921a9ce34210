import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/simple_trading_model.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math';

class NewTradingSimulatorScreen extends StatefulWidget {
  const NewTradingSimulatorScreen({Key? key}) : super(key: key);

  @override
  State<NewTradingSimulatorScreen> createState() => _NewTradingSimulatorScreenState();
}

class _NewTradingSimulatorScreenState extends State<NewTradingSimulatorScreen> {
  bool _showAllCandles = false;

  @override
  void initState() {
    super.initState();
    // Загружаем данные при инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final model = Provider.of<SimpleTradingModel>(context, listen: false);
      if (model.allCandles.isEmpty) {
        model.fetchCandles();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final model = Provider.of<SimpleTradingModel>(context);
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF000428), Color(0xFF004e92)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Верхняя панель с информацией
              _buildInfoPanel(model),

              // График
              Expanded(
                child: model.gameStatus == 'loading' || model.allCandles.isEmpty
                    ? const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      )
                    : _buildChart(model, screenSize),
              ),

              // Панель управления
              _buildControlPanel(model),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoPanel(SimpleTradingModel model) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Символ и таймфрейм
              Row(
                children: [
                  Text(
                    model.symbol,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      model.timeframe,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),

              // Баланс
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    'Balance',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '\$${model.balance.toStringAsFixed(2)}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),

          // Статистика для режима Infinite
          if (model.gameMode == 'infinite')
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildStatItem('Win Rate', '${model.totalPredictions > 0 ? (model.correctPredictions / model.totalPredictions * 100).toStringAsFixed(1) : 0}%'),
                  _buildStatItem('Streak', '${model.streak}'),
                  _buildStatItem('Max Streak', '${model.maxStreak}'),
                  _buildStatItem('Leverage', '${model.leverage}x'),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildChart(SimpleTradingModel model, Size screenSize) {
    // Определяем, какие свечи показывать
    final candles = _showAllCandles
        ? model.allCandles
        : model.allCandles.sublist(0, model.allCandles.length - SimpleTradingModel.futureCandlesCount);

    // Находим минимальные и максимальные значения для масштабирования
    double minY = double.infinity;
    double maxY = -double.infinity;

    for (final candle in candles) {
      if (candle.low < minY) minY = candle.low;
      if (candle.high > maxY) maxY = candle.high;
    }

    // Добавляем отступы
    final padding = (maxY - minY) * 0.05;
    minY -= padding;
    maxY += padding;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Результат сделки
          if (model.readyForNextLevel && model.trades.isNotEmpty)
            _buildTradeResult(model.trades.last),

          // График
          Expanded(
            child: LineChart(
              LineChartData(
                minX: 0,
                maxX: candles.length.toDouble() - 1,
                minY: minY,
                maxY: maxY,
                lineTouchData: LineTouchData(enabled: true),
                gridData: FlGridData(show: true),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          value.toStringAsFixed(1),
                          style: const TextStyle(
                            color: Colors.white60,
                            fontSize: 10,
                          ),
                        );
                      },
                    ),
                  ),
                  rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: true, border: Border.all(color: Colors.white24)),
                lineBarsData: [
                  // Линия цены закрытия
                  LineChartBarData(
                    spots: List.generate(candles.length, (i) {
                      return FlSpot(i.toDouble(), candles[i].close);
                    }),
                    isCurved: false,
                    color: Colors.blue,
                    barWidth: 2,
                    isStrokeCapRound: true,
                    dotData: FlDotData(show: false),
                    belowBarData: BarAreaData(show: false),
                  ),
                ],
                // Точка входа
                extraLinesData: ExtraLinesData(
                  horizontalLines: model.entryPrice != null && _showAllCandles
                      ? [
                          HorizontalLine(
                            y: model.entryPrice!,
                            color: Colors.white,
                            strokeWidth: 1,
                            dashArray: [5, 5],
                            label: HorizontalLineLabel(
                              show: true,
                              alignment: Alignment.topRight,
                              padding: const EdgeInsets.only(left: 10),
                              style: const TextStyle(color: Colors.white, fontSize: 10),
                              labelResolver: (line) => 'Entry: ${line.y.toStringAsFixed(2)}',
                            ),
                          ),
                        ]
                      : [],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTradeResult(SimpleTrade trade) {
    final isProfit = trade.profit > 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: (isProfit ? Colors.green : Colors.red).withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isProfit ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                trade.isWin ? 'Correct Prediction!' : 'Wrong Prediction!',
                style: TextStyle(
                  color: isProfit ? Colors.green : Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Entry: ${trade.entryPrice.toStringAsFixed(2)} → Exit: ${trade.exitPrice.toStringAsFixed(2)}',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${trade.percentChange >= 0 ? '+' : ''}${trade.percentChange.toStringAsFixed(2)}%',
                style: TextStyle(
                  color: trade.percentChange >= 0 ? Colors.green : Colors.red,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${trade.profit >= 0 ? '+' : ''}\$${trade.profit.toStringAsFixed(2)}',
                style: TextStyle(
                  color: isProfit ? Colors.green : Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlPanel(SimpleTradingModel model) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: model.readyForNextLevel
          ? _buildNextLevelButton(model)
          : _buildTradeButtons(model),
    );
  }

  Widget _buildNextLevelButton(SimpleTradingModel model) {
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _showAllCandles = false;
        });
        model.goToNextLevel();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: const Text(
        'Next Pattern',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTradeButtons(SimpleTradingModel model) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Кнопка UP
        ElevatedButton(
          onPressed: model.gameStatus == 'playing'
              ? () {
                  setState(() {
                    _showAllCandles = true;
                  });
                  model.makeTrade('buy');
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text(
            'UP',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),

        // Кнопка DOWN
        ElevatedButton(
          onPressed: model.gameStatus == 'playing'
              ? () {
                  setState(() {
                    _showAllCandles = true;
                  });
                  model.makeTrade('sell');
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text(
            'DOWN',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
