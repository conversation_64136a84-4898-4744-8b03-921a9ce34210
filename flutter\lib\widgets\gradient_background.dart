import 'package:flutter/material.dart';

/// A widget that provides a gradient background for screens
class G<PERSON>ientBackground extends StatelessWidget {
  final Widget child;
  final List<Color>? gradientColors;

  const GradientBackground({
    super.key,
    required this.child,
    this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: gradientColors ?? [
            Colors.black,
            const Color(0xFF121212),
            const Color(0xFF1E1E1E),
          ],
        ),
      ),
      child: child,
    );
  }
}
