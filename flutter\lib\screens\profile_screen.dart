import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import 'dart:math';
import 'dart:math' show pi, sin, log;
import '../widgets/app_bottom_navigation.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';

// Класс для элемента настроек
class SettingItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final bool hasSwitch;
  final bool? switchValue;
  final Function(bool)? onSwitchChanged;
  final VoidCallback? onTap;
  final Color? textColor;

  SettingItem({
    required this.icon,
    required this.title,
    this.subtitle,
    this.hasSwitch = false,
    this.switchValue,
    this.onSwitchChanged,
    this.onTap,
    this.textColor,
  });
}

// Отдельный StatefulWidget для аватара с собственным контроллером анимации
class _IndependentGlowAvatar extends StatefulWidget {
  final VoidCallback onTap;
  final Color accentColor;

  const _IndependentGlowAvatar({
    required this.onTap,
    required this.accentColor,
  });

  @override
  State<_IndependentGlowAvatar> createState() => _IndependentGlowAvatarState();
}

class _IndependentGlowAvatarState extends State<_IndependentGlowAvatar> with TickerProviderStateMixin {
  late AnimationController _avatarAnimationController;
  late Animation<double> _pulseAnimation;

  // Увеличиваем базовое свечение для аватара
  final double _baseGlowIntensity = 0.25;

  // Контроллер для постоянной мягкой пульсации
  late AnimationController _continuousPulseController;
  late Animation<double> _continuousPulseAnimation;

  @override
  void initState() {
    super.initState();
    // Основная анимация нажатия
    _avatarAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Создаем анимацию с кривой для более органичного эффекта
    _pulseAnimation = CurvedAnimation(
      parent: _avatarAnimationController,
      curve: Curves.easeOutCubic,
      reverseCurve: Curves.easeInCubic,
    );

    // Создаем контроллер для постоянной мягкой пульсации
    _continuousPulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000), // Медленная пульсация
    )..repeat(reverse: true); // Автоматически повторяем с реверсом

    // Анимация с синусоидальной кривой для более естественной пульсации
    _continuousPulseAnimation = CurvedAnimation(
      parent: _continuousPulseController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _avatarAnimationController.dispose();
    _continuousPulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: (_) {
        // Плавно анимируем к максимальному значению
        _avatarAnimationController.animateTo(1.0, duration: const Duration(milliseconds: 150));
      },
      onTapUp: (_) {
        // Плавно возвращаем к исходному состоянию с более длительной анимацией
        _avatarAnimationController.animateBack(0.0, duration: const Duration(milliseconds: 500));
      },
      onTapCancel: () {
        // Плавно возвращаем к исходному состоянию с более длительной анимацией
        _avatarAnimationController.animateBack(0.0, duration: const Duration(milliseconds: 500));
      },
      child: AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _continuousPulseAnimation]),
        builder: (context, _) {
          // Увеличиваем базовую пульсацию (от 0.0 до 0.15)
          final double basePulse = _continuousPulseAnimation.value * 0.15;

          // Увеличиваем дополнительную пульсацию при нажатии
          final double tapPulse = _pulseAnimation.value * 0.5;

          // Комбинированный эффект свечения с увеличенной яркостью
          final Color glowColor = Color.lerp(
            const Color(0xFF8AB06D).withOpacity((_baseGlowIntensity + basePulse).clamp(0.0, 1.0)),
            const Color(0xFF9BC97A).withOpacity((0.9 + basePulse).clamp(0.0, 1.0)), // Увеличиваем максимальную яркость
            _pulseAnimation.value,
          ) ?? const Color(0xFF8AB06D).withOpacity(_baseGlowIntensity.clamp(0.0, 1.0));

          return Container(
            width: 90,
            height: 90,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: glowColor,
                  // Увеличиваем параметры размытия для более яркого свечения
                  blurRadius: 15 + (basePulse * 8) + (tapPulse * 12),
                  spreadRadius: 2 + (basePulse * 2) + (tapPulse * 4),
                ),
              ],
            ),
            child: Center(
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color.lerp(const Color(0xFF8AB06D), Colors.white, (basePulse * 0.15) + (tapPulse * 0.3)) ?? const Color(0xFF8AB06D),
                      Color.lerp(const Color(0xFF9BC97A), Colors.white, (basePulse * 0.1) + (tapPulse * 0.2)) ?? const Color(0xFF9BC97A),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF8AB06D).withOpacity((0.4 + (basePulse * 0.15) + (tapPulse * 0.3)).clamp(0.0, 1.0)),
                      blurRadius: 10 + (basePulse * 3) + (tapPulse * 6),
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: const Center(
                  child: Text(
                    'JD',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Отдельный StatefulWidget для каждой кнопки с собственным контроллером анимации
class _IndependentGlowButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final bool isProfileCard; // Флаг для определения, является ли это профильной карточкой

  const _IndependentGlowButton({
    required this.child,
    this.onTap,
    this.isProfileCard = false, // По умолчанию false
  });

  @override
  State<_IndependentGlowButton> createState() => _IndependentGlowButtonState();
}

class _IndependentGlowButtonState extends State<_IndependentGlowButton> with TickerProviderStateMixin {
  late AnimationController _buttonAnimationController;
  late Animation<double> _pulseAnimation;

  // Контроллер для эффекта эхо-пульсации
  late AnimationController _echoPulseController;
  late Animation<double> _echoPulseAnimation;

  // Контроллер для вторичной эхо-пульсации (создает эффект затухающих волн)
  late AnimationController _secondaryEchoPulseController;
  late Animation<double> _secondaryEchoPulseAnimation;

  // Контроллер для эффекта переливания со стороны в сторону
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  // Флаг для отслеживания наведения мыши
  bool _isHovered = false;

  // Базовое свечение, которое всегда присутствует (уменьшено, так как будет активно только при наведении)
  final double _baseGlowIntensity = 0.05;

  // Единый цвет свечения
  final Color _glowColor = const Color(0xFF8AB06D); // Зеленый

  @override
  void initState() {
    super.initState();
    // Увеличиваем длительность анимации нажатия для более плавного эффекта
    _buttonAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800), // Увеличиваем для более плавного эффекта
    );

    // Создаем анимацию с кривой для более плавного, замедленного эффекта
    _pulseAnimation = CurvedAnimation(
      parent: _buttonAnimationController,
      // Используем более плавную кривую для эффекта слоу-мо
      curve: Curves.easeOutQuart, // Более плавная кривая для замедленного эффекта
      reverseCurve: Curves.easeInOutQuart, // Симметричная кривая для плавного затухания
    );

    // Инициализируем контроллер для основной эхо-пульсации с увеличенной длительностью
    _echoPulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3500), // Увеличиваем длительность для более плавного эффекта эхо
    );

    // Создаем анимацию эхо-пульсации с более плавной кривой
    _echoPulseAnimation = CurvedAnimation(
      parent: _echoPulseController,
      // Используем более плавную кривую для имитации затухания звуковой волны
      curve: Curves.easeInOutCirc, // Более плавная круговая кривая
    );

    // Инициализируем контроллер для вторичной эхо-пульсации с увеличенной длительностью
    _secondaryEchoPulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 4500), // Увеличиваем длительность для более плавного эффекта затухающего эхо
    );

    // Создаем анимацию вторичной эхо-пульсации с более плавной кривой
    _secondaryEchoPulseAnimation = CurvedAnimation(
      parent: _secondaryEchoPulseController,
      curve: Curves.easeInOutSine, // Синусоидальная кривая для плавных волн
    );

    // Не запускаем анимации автоматически, они будут запускаться только при наведении
    // Устанавливаем начальные значения для контроллеров
    _echoPulseController.value = 0.0;
    _secondaryEchoPulseController.value = 0.0;

    // Инициализируем контроллер для эффекта переливания со стороны в сторону с увеличенной длительностью
    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 6000), // Увеличиваем длительность для более плавного переливания
    );

    // Создаем анимацию переливания со стороны в сторону с более плавной кривой
    _waveAnimation = CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeInOutSine, // Синусоидальная кривая для более плавного волнообразного эффекта
    );

    // Не запускаем анимацию переливания автоматически
    _waveController.value = 0.0;
  }

  @override
  void dispose() {
    _buttonAnimationController.dispose();
    _echoPulseController.dispose();
    _secondaryEchoPulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  // Метод для запуска анимаций при наведении
  void _startAnimations() {
    if (!_isHovered) {
      setState(() {
        _isHovered = true;
      });

      // Запускаем эхо-пульсации
      _echoPulseController.repeat(reverse: true);

      // Запускаем вторичную эхо-пульсацию с небольшой задержкой
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted && _isHovered) {
          _secondaryEchoPulseController.repeat(reverse: true);
        }
      });

      // Запускаем переливание со стороны в сторону
      _waveController.repeat(reverse: true);
    }
  }

  // Метод для остановки анимаций при уходе мыши
  void _stopAnimations() {
    if (_isHovered) {
      setState(() {
        _isHovered = false;
      });

      // Плавно останавливаем анимации
      _echoPulseController.animateTo(0.0, duration: const Duration(milliseconds: 500));
      _secondaryEchoPulseController.animateTo(0.0, duration: const Duration(milliseconds: 500));
      _waveController.animateTo(0.0, duration: const Duration(milliseconds: 500));
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _startAnimations(),
      onExit: (_) => _stopAnimations(),
      child: GestureDetector(
        onTap: widget.onTap,
        onTapDown: (_) {
          if (widget.onTap != null) {
            // Более плавный эффект при нажатии с постепенным нарастанием
            _buttonAnimationController.animateTo(
              1.0,
              duration: const Duration(milliseconds: 400), // Увеличиваем для более плавного нарастания
              curve: Curves.easeOutCirc, // Более плавная кривая для органичного эффекта
            );

            // Плавный эффект волны при нажатии
            _waveController.animateTo(
              (_waveController.value + 0.1) % 1.0,
              duration: const Duration(milliseconds: 800), // Увеличиваем для более плавного эффекта
              curve: Curves.easeInOutSine, // Более плавная синусоидальная кривая
            );
          }
        },
        onTapUp: (_) {
          if (widget.onTap != null) {
            // Более плавное затухание эффекта нажатия
            _buttonAnimationController.animateBack(
              0.0,
              duration: const Duration(milliseconds: 1000), // Увеличиваем для более плавного затухания
              curve: Curves.easeOutQuart, // Более плавная кривая для органичного затухания
            );
          }
        },
        onTapCancel: () {
          if (widget.onTap != null) {
            // Более плавное затухание эффекта нажатия
            _buttonAnimationController.animateBack(
              0.0,
              duration: const Duration(milliseconds: 1000), // Увеличиваем для более плавного затухания
              curve: Curves.easeOutQuart, // Более плавная кривая для органичного затухания
            );
          }
        },
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _pulseAnimation,
            _echoPulseAnimation,
            _secondaryEchoPulseAnimation,
            _waveAnimation,
          ]),
          builder: (context, child) {
            // Основная эхо-пульсация с уменьшенной амплитудой для более тонкого эффекта
            final double echoPulse = _echoPulseAnimation.value * 0.18; // Уменьшаем амплитуду для более тонкого эффекта

            // Вторичная эхо-пульсация с уменьшенной амплитудой для более тонкого эффекта
            final double secondaryEchoPulse = _secondaryEchoPulseAnimation.value * 0.12; // Уменьшаем амплитуду для более тонкого эффекта

            // Эффект волны для переливания со стороны в сторону с уменьшенной амплитудой
            final double waveEffect = ((_waveAnimation.value * 1.6) - 0.8) * 0.8; // Уменьшаем амплитуду для более плавного переливания

            // Комбинированная пульсация с эффектом интерференции волн
            // Используем более сложную формулу для создания более плавного наложения волн
            final double combinedPulse = echoPulse * 0.6 +
                                        secondaryEchoPulse * 0.6 * (1 - echoPulse * 0.2) +
                                        sin(_waveAnimation.value * pi * 2) * 0.03; // Добавляем синусоидальную составляющую для более плавного эффекта

            // Эффект нажатия - очень плавное увеличение яркости для эффекта слоу-мо
            // Используем кубическую функцию для создания более плавного нарастания
            final double tapEffect = _pulseAnimation.value * _pulseAnimation.value * _pulseAnimation.value * 0.5; // Кубическая функция для более плавного нарастания

            // Добавляем эффект пульсации цвета при нажатии для усиления эффекта слоу-мо
            final double colorIntensity = (_baseGlowIntensity + combinedPulse + tapEffect).clamp(0.0, 1.0);

            // Создаем эффект пульсации цвета при нажатии
            final Color finalGlowColor = Color.lerp(
              _glowColor.withOpacity(colorIntensity),
              // Слегка меняем оттенок при нажатии для более заметного эффекта
              Color.fromARGB(
                _glowColor.alpha,
                (_glowColor.red + 15).clamp(0, 255),
                (_glowColor.green + 20).clamp(0, 255),
                (_glowColor.blue - 5).clamp(0, 255),
              ).withOpacity((colorIntensity + 0.1).clamp(0.0, 1.0)),
              _pulseAnimation.value,
            ) ?? _glowColor.withOpacity(colorIntensity);

            // Определяем радиус закругления в зависимости от типа виджета
            final double borderRadius = widget.isProfileCard ? 48.0 : 16.0;

            return DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(borderRadius),
                boxShadow: [
                  // Первый слой свечения - самый мягкий, создает основу для эффекта слоу-мо
                  BoxShadow(
                    color: finalGlowColor.withOpacity((finalGlowColor.opacity * 0.4).clamp(0.0, 1.0)),
                    // Более плавное изменение размытия с использованием синусоидальной функции
                    blurRadius: widget.isProfileCard
                        ? 12 + (sin(combinedPulse * pi) * 4)
                        : 8 + (sin(combinedPulse * pi) * 3),
                    // Более плавное изменение распространения
                    spreadRadius: widget.isProfileCard
                        ? 0.6 + (combinedPulse * 0.8)
                        : 0.3 + (combinedPulse * 0.5),
                    // Более плавное движение для эффекта слоу-мо
                    offset: Offset(sin(waveEffect * pi) * (widget.isProfileCard ? 2.0 : 1.5), 0),
                  ),

                  // Второй слой свечения - средний, создает основной эффект пульсации
                  BoxShadow(
                    color: finalGlowColor.withOpacity((finalGlowColor.opacity * 0.6).clamp(0.0, 1.0)),
                    // Более плавное изменение размытия с использованием синусоидальной функции
                    blurRadius: widget.isProfileCard
                        ? 18 + (sin(combinedPulse * pi * 1.5) * 8) + (tapEffect * 15)
                        : 12 + (sin(combinedPulse * pi * 1.5) * 6) + (tapEffect * 10),
                    // Более плавное изменение распространения
                    spreadRadius: widget.isProfileCard
                        ? 1.0 + (sin(combinedPulse * pi) * 0.8) + (tapEffect * tapEffect * 3.0)
                        : 0.6 + (sin(combinedPulse * pi) * 0.5) + (tapEffect * tapEffect * 2.0),
                    // Более плавное смещение для создания эффекта объемного переливания
                    offset: Offset(sin(waveEffect * pi * 0.8) * (widget.isProfileCard ? -3.0 : -2.0), sin(waveEffect * pi * 1.2) * 0.8),
                  ),

                  // Третий слой свечения - самый яркий, создает эффект вспышки при нажатии
                  BoxShadow(
                    color: finalGlowColor,
                    // Более плавное изменение размытия с использованием синусоидальной функции
                    blurRadius: widget.isProfileCard
                        ? 22 + (sin(combinedPulse * pi * 0.8) * 10) + (tapEffect * 20)
                        : 16 + (sin(combinedPulse * pi * 0.8) * 8) + (tapEffect * 15),
                    // Более плавное изменение распространения
                    spreadRadius: widget.isProfileCard
                        ? 1.2 + (sin(combinedPulse * pi * 1.2) * 1.0) + (tapEffect * tapEffect * 4.0)
                        : 0.8 + (sin(combinedPulse * pi * 1.2) * 0.7) + (tapEffect * tapEffect * 3.0),
                    // Более плавное смещение для создания эффекта движения света
                    offset: Offset(sin(waveEffect * pi * 1.2) * (widget.isProfileCard ? 5.0 : 4.0), sin(waveEffect * pi * 0.8) * (widget.isProfileCard ? 1.5 : 1.0)),
                  ),

                  // Четвертый слой свечения - дополнительный, создает эффект ореола при нажатии
                  BoxShadow(
                    // Этот слой появляется только при нажатии с более плавным нарастанием
                    color: finalGlowColor.withOpacity((finalGlowColor.opacity * 0.25 * tapEffect * (1 - sin(tapEffect * pi * 0.5))).clamp(0.0, 1.0)),
                    // Более плавное изменение размытия
                    blurRadius: widget.isProfileCard
                        ? 35 + (tapEffect * tapEffect * 25)
                        : 25 + (tapEffect * tapEffect * 20),
                    // Более плавное изменение распространения
                    spreadRadius: widget.isProfileCard
                        ? 1.5 + (tapEffect * tapEffect * tapEffect * 6.0)
                        : 1.0 + (tapEffect * tapEffect * tapEffect * 4.0),
                    // Нулевое смещение для создания равномерного ореола
                    offset: Offset.zero,
                  ),
                ],
              ),
              child: child,
            );
          },
          child: widget.child,
        ),
      ),
    );
  }
}

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with TickerProviderStateMixin {
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = true;

  // Мы больше не используем общий контроллер анимации
  // Вместо этого каждая кнопка будет иметь свой собственный контроллер

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    // Контроллеры анимации будут создаваться и уничтожаться в виджетах кнопок
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isDarkMode = themeProvider.isDarkMode;
    _darkModeEnabled = isDarkMode;

    // Цвета в стиле iOS с градиентами
    final backgroundColor = isDarkMode ? Colors.black : Colors.white;
    final cardColor = isDarkMode ? const Color(0xFF1C1C1E) : Colors.white;
    final textColor = isDarkMode ? Colors.white : Colors.black;
    final secondaryTextColor = isDarkMode ? Colors.white70 : Colors.black54;
    final accentColor = const Color(0xFF45553D); // Зеленый

    return Scaffold(
      backgroundColor: const Color(0xFF0A0B0D), // Dark background like sinusoid
      extendBodyBehindAppBar: true,
      extendBody: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Профиль',
          style: TextStyle(
            color: textColor,
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: CupertinoButton(
          padding: EdgeInsets.zero,
          child: Icon(
            CupertinoIcons.back,
            color: const Color(0xFF8AB06D),
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          CupertinoButton(
            padding: EdgeInsets.zero,
            child: Icon(
              CupertinoIcons.ellipsis_circle,
              color: const Color(0xFF8AB06D),
            ),
            onPressed: () {
              _showOptionsSheet(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Основное содержимое
          Expanded(
            child: Stack(
              children: [
                // Фоновый градиент
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: isDarkMode
                          ? const LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [Color(0xFF000000), Color(0xFF121212)],
                            )
                          : const LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [Color(0xFFF2F2F7), Color(0xFFFFFFFF)],
                            ),
                    ),
                  ),
                ),

                // Основной контент
                SafeArea(
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(), // iOS-style physics
                    child: Column(
                      children: [
                        const SizedBox(height: 20),

                        // Профиль пользователя
                        _buildProfileHeader(context, textColor, secondaryTextColor, accentColor, isDarkMode),

                        const SizedBox(height: 30),

                        // Настройки
                        _buildSectionHeader('Настройки', textColor),
                        const SizedBox(height: 25),

                        _buildSettingsGroup(
                          context: context,
                          cardColor: cardColor,
                          textColor: textColor,
                          secondaryTextColor: secondaryTextColor,
                          accentColor: accentColor,
                          items: [
                            SettingItem(
                              icon: CupertinoIcons.bell,
                              title: 'Уведомления',
                              hasSwitch: true,
                              switchValue: _notificationsEnabled,
                              onSwitchChanged: (value) {
                                setState(() {
                                  _notificationsEnabled = value;
                                });
                              },
                            ),
                            SettingItem(
                              icon: CupertinoIcons.moon_stars,
                              title: 'Темная тема',
                              hasSwitch: true,
                              switchValue: _darkModeEnabled,
                              onSwitchChanged: (value) {
                                setState(() {
                                  _darkModeEnabled = value;
                                  themeProvider.toggleTheme();
                                });
                              },
                            ),
                            SettingItem(
                              icon: CupertinoIcons.globe,
                              title: 'Язык',
                              subtitle: 'Русский',
                              onTap: () => _showLanguageSelector(context),
                            ),
                          ],
                        ),

                        const SizedBox(height: 40),

                        // Аккаунт
                        _buildSectionHeader('Аккаунт', textColor),
                        const SizedBox(height: 25),

                        _buildSettingsGroup(
                          context: context,
                          cardColor: cardColor,
                          textColor: textColor,
                          secondaryTextColor: secondaryTextColor,
                          accentColor: accentColor,
                          items: [
                            SettingItem(
                              icon: CupertinoIcons.lock_shield,
                              title: 'Безопасность',
                              onTap: () {},
                            ),
                            SettingItem(
                              icon: CupertinoIcons.person_2,
                              title: 'Пригласить друзей',
                              onTap: () {},
                            ),
                            SettingItem(
                              icon: CupertinoIcons.question_circle,
                              title: 'Помощь',
                              onTap: () {},
                            ),
                            SettingItem(
                              icon: CupertinoIcons.info_circle,
                              title: 'О приложении',
                              onTap: () {},
                            ),
                          ],
                        ),

                        const SizedBox(height: 40),

                        // Выход
                        _buildSettingsGroup(
                          context: context,
                          cardColor: cardColor,
                          textColor: textColor,
                          secondaryTextColor: secondaryTextColor,
                          accentColor: accentColor,
                          items: [
                            SettingItem(
                              icon: CupertinoIcons.square_arrow_left,
                              title: 'Выйти',
                              textColor: Colors.red,
                              onTap: () => _showLogoutConfirmation(context),
                            ),
                          ],
                        ),

                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 4,
        onTap: (index) {
          if (index != 4) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 1:
                Navigator.pushReplacementNamed(context, '/charts');
                break;
              case 2:
                Navigator.pushReplacementNamed(context, '/sinusoid');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
            }
          }
        },
      ),
    );
  }

  // Объединенный виджет для аватара и имени пользователя
  Widget _buildCombinedProfileCard({
    required BuildContext context,
    required String name,
    required String email,
    required Color textColor,
    required Color secondaryTextColor,
    required Color accentColor,
    required bool isDarkMode,
  }) {
    return _buildAnimatedButton(
      onTap: () => _showEditProfileDialog(context),
      isProfileCard: true, // Указываем, что это профильная карточка
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 22),
        decoration: BoxDecoration(
          color: isDarkMode ? const Color(0xFF1C1C1E) : Colors.white,
          borderRadius: BorderRadius.circular(48), // Увеличиваем закругление для более мягкого эффекта
          border: Border.all(
            color: isDarkMode ? Colors.white.withOpacity(0.15) : Colors.black.withOpacity(0.05),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 15,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Stack(
          children: [
            // Выделяющаяся иконка кисточки
            Positioned(
              top: 10,
              right: 10,
              child: GestureDetector(
                onTap: () => _showEditProfileDialog(context),
                child: _buildGlowingEditIcon(),
              ),
            ),

            // Основной контент
            Row(
              children: [
                // Аватар с градиентом
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xFF8AB06D), // Яркий зеленый
                        const Color(0xFF9BC97A), // Очень яркий зеленый
                      ],
                    ),

                  ),
                  child: Stack(
                    children: [
                      // Инициалы пользователя
                      const Center(
                        child: Text(
                          'JD',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 28,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),

                      // Иконка камеры
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: const Color(0xFF8AB06D),
                            shape: BoxShape.circle,

                          ),
                          child: const Center(
                            child: Icon(
                              CupertinoIcons.camera_fill,
                              color: Colors.white,
                              size: 14,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 25),

                // Информация о пользователе
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            name,
                            style: TextStyle(
                              color: textColor,
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                              letterSpacing: -0.5,
                            ),
                          ),
                          const Spacer(),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        email,
                        style: TextStyle(
                          color: secondaryTextColor,
                          fontSize: 18,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          _buildStatusBadge(
                            icon: CupertinoIcons.checkmark_seal_fill,
                            label: 'Верифицирован',
                            color: Colors.blue, // Синяя галочка
                          ),
                          const SizedBox(width: 8),
                          _buildStatusBadge(
                            icon: CupertinoIcons.star_fill,
                            label: 'Premium',
                            color: Colors.amber, // Золотая звездочка
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms).moveY(begin: 20, end: 0);
  }

  // Выделяющаяся иконка кисточки с пульсирующим свечением
  Widget _buildGlowingEditIcon() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return Container(
      width: 42,
      height: 42,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isDarkMode ? Colors.black.withOpacity(0.3) : Colors.white.withOpacity(0.8),
        border: Border.all(
          color: const Color(0xFF8AB06D).withOpacity(0.5),
          width: 1.5,
        ),

      ),
      child: const Icon(
        CupertinoIcons.pencil,
        color: Color(0xFF9BC97A),
        size: 22,
      ),
    );
  }

  // Маленький бейдж статуса
  Widget _buildStatusBadge({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.4),
          width: 1.5,
        ),

      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 12,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Заголовок секции
  Widget _buildSectionHeader(String title, Color textColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 100),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
              color: textColor,
              fontSize: 22,
              fontWeight: FontWeight.w600,
              letterSpacing: -0.5,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms, delay: 200.ms).moveY(begin: 10, end: 0);
  }

  // Профиль пользователя
  Widget _buildProfileHeader(BuildContext context, Color textColor, Color secondaryTextColor, Color accentColor, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 100),
      child: Column(
        children: [
          // Объединенный виджет для аватара и имени пользователя
          _buildCombinedProfileCard(
            context: context,
            name: 'Джон Доу',
            email: '<EMAIL>',
            textColor: textColor,
            secondaryTextColor: secondaryTextColor,
            accentColor: accentColor,
            isDarkMode: isDarkMode,
          ),

          const SizedBox(height: 30),


        ],
      ),
    );
  }

  // Группа настроек
  Widget _buildSettingsGroup({
    required BuildContext context,
    required Color cardColor,
    required Color textColor,
    required Color secondaryTextColor,
    required Color accentColor,
    required List<SettingItem> items,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 100),
      child: Column(
        children: List.generate(items.length, (index) {
          final item = items[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 30),
            child: _buildSettingItemCard(
              context: context,
              item: item,
              cardColor: cardColor,
              textColor: textColor,
              secondaryTextColor: secondaryTextColor,
              accentColor: accentColor,
              delay: Duration(milliseconds: 100 * index),
            ),
          );
        }),
      ),
    ).animate().fadeIn(duration: 500.ms, delay: 400.ms).moveY(begin: 20, end: 0);
  }

  // Карточка настройки
  Widget _buildSettingItemCard({
    required BuildContext context,
    required SettingItem item,
    required Color cardColor,
    required Color textColor,
    required Color secondaryTextColor,
    required Color accentColor,
    required Duration delay,
  }) {
    // Проверяем, является ли это кнопкой "Выйти" (по цвету текста и названию)
    final bool isLogoutButton = item.textColor == Colors.red && item.title == 'Выйти';

    return _buildAnimatedButton(
      onTap: item.hasSwitch ? null : item.onTap,
      disableGlow: isLogoutButton, // Отключаем свечение для кнопки "Выйти"
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 18),
        decoration: BoxDecoration(
          color: cardColor,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          children: [
            // Иконка
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: (item.textColor ?? const Color(0xFF8AB06D)).withOpacity(0.15),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Icon(
                  item.icon,
                  color: item.textColor ?? const Color(0xFF8AB06D),
                  size: 26,
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Текст
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title,
                    style: TextStyle(
                      color: item.textColor ?? textColor,
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (item.subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      item.subtitle!,
                      style: TextStyle(
                        color: secondaryTextColor,
                        fontSize: 18,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Переключатель или стрелка
            if (item.hasSwitch)
              CupertinoSwitch(
                value: item.switchValue ?? false,
                activeColor: const Color(0xFF8AB06D),
                onChanged: item.onSwitchChanged ?? (_) {},
              )
            else
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: (item.textColor ?? const Color(0xFF8AB06D)).withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Icon(
                    CupertinoIcons.chevron_right,
                    color: item.textColor ?? const Color(0xFF8AB06D),
                    size: 18,
                  ),
                ),
              ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 400.ms, delay: delay);
  }

  // Анимированная кнопка с независимым свечением при нажатии
  Widget _buildAnimatedButton({required Widget child, VoidCallback? onTap, bool isProfileCard = false, bool disableGlow = false}) {
    // Если disableGlow = true, возвращаем обычную кнопку без свечения
    if (disableGlow) {
      return GestureDetector(
        onTap: onTap,
        child: child,
      );
    }

    // Иначе возвращаем кнопку со свечением
    return _IndependentGlowButton(
      child: child,
      onTap: onTap,
      isProfileCard: isProfileCard, // Передаем флаг
    );
  }
  // Диалог редактирования профиля
  void _showEditProfileDialog(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: const Text('Редактировать профиль'),
        message: const Text('Выберите действие'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Действие по изменению фото
            },
            child: const Text('Изменить фото'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Действие по изменению имени
            },
            child: const Text('Изменить имя'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Действие по изменению email
            },
            child: const Text('Изменить email'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.pop(context),
          isDestructiveAction: true,
          child: const Text('Отмена'),
        ),
      ),
    );
  }

  // Диалог выбора языка
  void _showLanguageSelector(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: const Text('Выберите язык'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () => Navigator.pop(context),
            isDefaultAction: true,
            child: const Text('Русский'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.pop(context),
            child: const Text('English'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.pop(context),
            child: const Text('Español'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.pop(context),
            child: const Text('Français'),
          ),
          CupertinoActionSheetAction(
            onPressed: () => Navigator.pop(context),
            child: const Text('Deutsch'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.pop(context),
          isDestructiveAction: true,
          child: const Text('Отмена'),
        ),
      ),
    );
  }

  // Диалог подтверждения выхода
  void _showLogoutConfirmation(BuildContext context) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Выйти из аккаунта'),
        content: const Text('Вы уверены, что хотите выйти из своего аккаунта?'),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.pop(context),
            isDefaultAction: true,
            child: const Text('Отмена'),
          ),
          CupertinoDialogAction(
            onPressed: () {
              Navigator.pop(context);
              // Действие по выходу из аккаунта
            },
            isDestructiveAction: true,
            child: const Text('Выйти'),
          ),
        ],
      ),
    );
  }

  // Показать меню опций
  void _showOptionsSheet(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Действие по настройке уведомлений
            },
            child: const Text('Настройки уведомлений'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Действие по настройке приватности
            },
            child: const Text('Настройки приватности'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.pop(context),
          child: const Text('Отмена'),
        ),
      ),
    );
  }
}
