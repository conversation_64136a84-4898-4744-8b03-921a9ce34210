import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'providers/trading_simulator_provider.dart';
import 'widgets/game_mode_selection_screen.dart';
import 'widgets/custom_mode_settings_screen.dart';
import 'widgets/infinite_patterns_leverage_screen.dart';
import 'widgets/trading_simulator_game_screen.dart';

class CryptoTradingSimulatorScreen extends StatefulWidget {
  const CryptoTradingSimulatorScreen({Key? key}) : super(key: key);

  @override
  State<CryptoTradingSimulatorScreen> createState() => _CryptoTradingSimulatorScreenState();
}

class _CryptoTradingSimulatorScreenState extends State<CryptoTradingSimulatorScreen> {
  @override
  void initState() {
    super.initState();
    // Мы не будем использовать этот метод, так как он вызывает ошибку
    // Provider не доступен в initState
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => TradingSimulatorProvider(),
      child: Consumer<TradingSimulatorProvider>(
        builder: (context, provider, _) {
          return Scaffold(
            backgroundColor: Colors.black,
            appBar: AppBar(
              backgroundColor: Colors.black,
              title: Text(
                _getAppBarTitle(provider.currentScreen),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              leading: _buildBackButton(context, provider),
              elevation: 0,
            ),
            body: _buildCurrentScreen(provider),
          );
        },
      ),
    );
  }

  Widget _buildBackButton(BuildContext context, TradingSimulatorProvider provider) {
    if (provider.currentScreen == SimulatorScreen.modeSelection) {
      return IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      );
    } else {
      return IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => provider.navigateBack(),
      );
    }
  }

  String _getAppBarTitle(SimulatorScreen screen) {
    switch (screen) {
      case SimulatorScreen.modeSelection:
        return 'Crypto Trading Simulator';
      case SimulatorScreen.customModeSettings:
        return 'Custom Mode Settings';
      case SimulatorScreen.infinitePatternsLeverage:
        return 'Select Leverage';
      case SimulatorScreen.gameScreen:
        return 'Trading Simulator';
      default:
        return 'Crypto Trading Simulator';
    }
  }

  Widget _buildCurrentScreen(TradingSimulatorProvider provider) {
    switch (provider.currentScreen) {
      case SimulatorScreen.modeSelection:
        return const GameModeSelectionScreen();
      case SimulatorScreen.customModeSettings:
        return const CustomModeSettingsScreen();
      case SimulatorScreen.infinitePatternsLeverage:
        return const InfinitePatternsLeverageScreen();
      case SimulatorScreen.gameScreen:
        return const TradingSimulatorGameScreen();
      default:
        return const GameModeSelectionScreen();
    }
  }
}
