{"dart.flutterSdkPath": null, "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.flutterHotReloadOnSave": "always", "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreatePlatforms": ["android", "ios", "web"], "files.associations": {"*.dart": "dart"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": true, "source.organizeImports": true}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/build": true, "**/.dart_tool": true, "**/.packages": true, "**/pubspec.lock": false}, "search.exclude": {"**/build": true, "**/.dart_tool": true, "**/.packages": true}, "dart.lineLength": 120, "dart.insertArgumentPlaceholders": false, "dart.enableSnippets": true, "dart.completeFunctionCalls": true, "flutter.experiments": {"custom-embedder": true}, "emmet.includeLanguages": {"dart": "html"}, "workbench.colorCustomizations": {"activityBar.background": "#0F172A", "activityBar.foreground": "#6366F1", "statusBar.background": "#1E293B", "statusBar.foreground": "#CBD5E1"}}