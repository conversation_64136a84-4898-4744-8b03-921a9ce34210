import 'package:flutter/material.dart';
import 'screens/welcome_screen.dart';
import 'screens/login_screen.dart';
import 'screens/news_screen.dart';
import 'screens/minimal_news_detail_screen.dart';
import 'screens/crypto_markets_screen.dart';
import 'screens/ai_assistant_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/saved_analyses_screen.dart';
import 'screens/courses_screen.dart';
import 'screens/course_detail_screen.dart';
import 'screens/games_screen.dart';
import 'screens/crypto_trading_simulator_screen.dart';
import 'screens/anti_fomo_simulator_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Finance AI Flutter',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      initialRoute: '/',
      routes: {
        '/': (context) => const WelcomeScreen(),
        '/login': (context) => const LoginScreen(),
        '/news': (context) => const NewsScreen(),
        '/news_detail': (context) => const MinimalNewsDetailScreen(),
        '/crypto_markets': (context) => const CryptoMarketsScreen(),
        '/ai_assistant': (context) => const AIAssistantScreen(),
        '/profile': (context) => const ProfileScreen(),
        '/saved_analyses': (context) => const SavedAnalysesScreen(),
        '/courses': (context) => const CoursesScreen(),
        '/course_detail': (context) => const CourseDetailScreen(),
        '/games': (context) => const GamesScreen(),
        '/crypto_simulator': (context) => const CryptoTradingSimulatorScreen(),
        '/anti_fomo_simulator': (context) => const AntiFOMOSimulatorScreen(),
      },
    );
  }
}
