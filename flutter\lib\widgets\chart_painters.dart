import 'package:flutter/material.dart';

class CandleData {
  final DateTime time;
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;

  CandleData({
    required this.time,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });

  bool get isGreen => close >= open;
}

class LineChartPainter extends CustomPainter {
  final List<double> prices;
  final double maxPrice;
  final double minPrice;
  final Color color;
  final bool fillArea;

  LineChartPainter({
    required this.prices,
    required this.maxPrice,
    required this.minPrice,
    required this.color,
    this.fillArea = true,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (prices.isEmpty) return;

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final fillPaint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final priceRange = maxPrice - minPrice;
    final xStep = size.width / (prices.length - 1);

    final path = Path();
    final fillPath = Path();

    // Начинаем с первой точки
    final firstPoint = Offset(
      0,
      size.height - ((prices[0] - minPrice) / priceRange * size.height),
    );
    path.moveTo(firstPoint.dx, firstPoint.dy);
    fillPath.moveTo(firstPoint.dx, size.height);
    fillPath.lineTo(firstPoint.dx, firstPoint.dy);

    // Добавляем остальные точки
    for (int i = 1; i < prices.length; i++) {
      final x = xStep * i;
      final y = size.height - ((prices[i] - minPrice) / priceRange * size.height);
      path.lineTo(x, y);
      fillPath.lineTo(x, y);
    }

    // Закрываем путь для заполнения
    if (fillArea) {
      fillPath.lineTo(size.width, size.height);
      fillPath.close();
      canvas.drawPath(fillPath, fillPaint);
    }

    // Рисуем линию
    canvas.drawPath(path, paint);

    // Рисуем горизонтальные линии сетки
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    final textPaint = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем 5 горизонтальных линий
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);

      // Добавляем метки цен
      final price = maxPrice - (i / 4) * priceRange;
      textPaint.text = TextSpan(
        text: price.toStringAsFixed(price < 10 ? 2 : 0),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );
      textPaint.layout();
      textPaint.paint(canvas, Offset(size.width - textPaint.width - 4, y - textPaint.height / 2));
    }

    // Рисуем вертикальные линии сетки (например, 6 линий)
    for (int i = 0; i <= 6; i++) {
      final x = size.width * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }
  }

  @override
  bool shouldRepaint(LineChartPainter oldDelegate) {
    return oldDelegate.prices != prices ||
        oldDelegate.maxPrice != maxPrice ||
        oldDelegate.minPrice != minPrice ||
        oldDelegate.color != color;
  }
}

class CandlestickChartPainter extends CustomPainter {
  final List<CandleData> candles;

  CandlestickChartPainter({required this.candles});

  @override
  void paint(Canvas canvas, Size size) {
    if (candles.isEmpty) return;

    // Находим максимальную и минимальную цены
    double maxPrice = candles.first.high;
    double minPrice = candles.first.low;

    for (final candle in candles) {
      if (candle.high > maxPrice) maxPrice = candle.high;
      if (candle.low < minPrice) minPrice = candle.low;
    }

    final priceRange = maxPrice - minPrice;
    final candleWidth = size.width / candles.length * 0.8; // 80% ширины для свечи
    final spacing = size.width / candles.length * 0.2; // 20% ширины для промежутка

    // Рисуем горизонтальные линии сетки
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    final textPaint = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем 5 горизонтальных линий
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);

      // Добавляем метки цен
      final price = maxPrice - (i / 4) * priceRange;
      textPaint.text = TextSpan(
        text: price.toStringAsFixed(price < 10 ? 2 : 0),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );
      textPaint.layout();
      textPaint.paint(canvas, Offset(size.width - textPaint.width - 4, y - textPaint.height / 2));
    }

    // Рисуем вертикальные линии сетки
    for (int i = 0; i <= 6; i++) {
      final x = size.width * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }

    // Рисуем свечи
    for (int i = 0; i < candles.length; i++) {
      final candle = candles[i];
      final x = i * (candleWidth + spacing) + spacing / 2;

      // Преобразуем цены в координаты Y
      final highY = size.height - ((candle.high - minPrice) / priceRange * size.height);
      final lowY = size.height - ((candle.low - minPrice) / priceRange * size.height);
      final openY = size.height - ((candle.open - minPrice) / priceRange * size.height);
      final closeY = size.height - ((candle.close - minPrice) / priceRange * size.height);

      // Определяем цвет свечи
      final color = candle.isGreen ? Colors.green : Colors.red;

      // Рисуем тень (вертикальная линия от минимума до максимума)
      final wickPaint = Paint()
        ..color = color
        ..strokeWidth = 1.0;
      canvas.drawLine(
        Offset(x + candleWidth / 2, highY),
        Offset(x + candleWidth / 2, lowY),
        wickPaint,
      );

      // Рисуем тело свечи (прямоугольник от open до close)
      final bodyPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;
      final bodyTop = candle.isGreen ? closeY : openY;
      final bodyBottom = candle.isGreen ? openY : closeY;
      final bodyHeight = (bodyBottom - bodyTop).abs();

      canvas.drawRect(
        Rect.fromLTWH(x, bodyTop, candleWidth, bodyHeight),
        bodyPaint,
      );
    }
  }

  @override
  bool shouldRepaint(CandlestickChartPainter oldDelegate) {
    return oldDelegate.candles != candles;
  }
}
