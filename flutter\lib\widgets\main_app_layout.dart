import 'package:flutter/material.dart';
import 'bottom_nav_bar.dart';
import '../screens/news_screen.dart';
import '../screens/charts_screen.dart';
import '../screens/reactor_sinusoid_screen.dart';
import '../screens/courses_screen.dart';
import '../screens/profile_screen.dart';

class MainAppLayout extends StatefulWidget {
  const MainAppLayout({Key? key}) : super(key: key);

  @override
  State<MainAppLayout> createState() => _MainAppLayoutState();
}

class _MainAppLayoutState extends State<MainAppLayout> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const NewsScreen(),
    const ChartsScreen(),
    const ReactorSinusoidScreen(),
    const CoursesScreen(),
    const ProfileScreen(),
  ];

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
      ),
    );
  }
} 