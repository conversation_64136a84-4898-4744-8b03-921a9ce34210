import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Класс для создания минималистичного футуристического фона
/// в стиле загрузочного экрана PS5
class MinimalistPS5Background extends CustomPainter {
  final double animationValue;
  final Offset pointerPosition;
  final double scrollOffset;
  final Size screenSize;
  
  // Частицы для анимации
  final List<Particle> _particles = [];
  
  // Цвета
  final Color particleColor;
  final Color backgroundColor;
  final Color accentColor;
  
  // Количество частиц
  final int particleCount;
  
  MinimalistPS5Background({
    required this.animationValue,
    required this.pointerPosition,
    required this.scrollOffset,
    required this.screenSize,
    this.particleColor = Colors.white,
    this.backgroundColor = Colors.black,
    this.accentColor = Colors.blueGrey,
    this.particleCount = 100,
  }) {
    _initializeParticles();
  }
  
  /// Инициализация частиц
  void _initializeParticles() {
    if (_particles.isNotEmpty) return;
    
    final random = math.Random(42); // Фиксированный seed для консистентности
    
    for (int i = 0; i < particleCount; i++) {
      final size = 1.0 + random.nextDouble() * 3.0; // Размер от 1 до 4
      
      // Вычисляем стартовое положение
      final xPos = random.nextDouble();
      final yPos = random.nextDouble();
      
      // Параметры движения частицы
      final speed = 0.02 + random.nextDouble() * 0.04; // Скорость движения
      final directionAngle = random.nextDouble() * math.pi * 2; // Начальное направление
      
      // Параметры параллакса
      final parallaxFactor = 0.3 + random.nextDouble() * 0.7; // Разные факторы для 3D-эффекта
      
      // Параметры отображения
      final opacity = 0.2 + random.nextDouble() * 0.7; // Разная прозрачность
      
      _particles.add(Particle(
        x: xPos,
        y: yPos,
        size: size,
        speed: speed,
        directionAngle: directionAngle,
        parallaxFactor: parallaxFactor,
        opacity: opacity,
      ));
    }
  }
  
  @override
  void paint(Canvas canvas, Size size) {
    // Заливка фона
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = backgroundColor,
    );
    
    // Рисуем частицы
    for (var particle in _particles) {
      // Вычисляем движение частицы по времени
      final timeOffset = animationValue * math.pi * 2;
      
      // Движение по кругу с постепенным смещением
      final angleOffset = timeOffset * particle.speed;
      final xOffset = math.cos(particle.directionAngle + angleOffset) * 0.1;
      final yOffset = math.sin(particle.directionAngle + angleOffset) * 0.1;
      
      // Добавляем эффект параллакса от курсора
      final cursorParallaxX = (pointerPosition.dx / screenSize.width - 0.5) * 30 * particle.parallaxFactor;
      final cursorParallaxY = (pointerPosition.dy / screenSize.height - 0.5) * 30 * particle.parallaxFactor;
      
      // Учитываем скролл
      final scrollParallax = scrollOffset * 0.05 * particle.parallaxFactor;
      
      // Вычисляем итоговую позицию
      final x = ((particle.x + xOffset) % 1.0) * size.width + cursorParallaxX;
      final y = ((particle.y + yOffset) % 1.0) * size.height + cursorParallaxY - scrollParallax;
      
      // "Дыхание" частиц - пульсация
      final pulseEffect = math.sin(timeOffset + particle.x * 10 + particle.y * 10) * 0.2 + 0.8;
      
      // Расстояние до курсора для интерактивности
      final distanceToCursor = pointerPosition == Offset.zero ? 
          double.infinity : (Offset(x, y) - Offset(pointerPosition.dx, pointerPosition.dy)).distance;
      
      // Эффект притяжения/отталкивания курсора
      final cursorInfluenceRadius = 100.0;
      final cursorInfluence = distanceToCursor < cursorInfluenceRadius
          ? 1.0 - (distanceToCursor / cursorInfluenceRadius)
          : 0.0;
      
      // Выбираем цвет в зависимости от близости к курсору
      final color = cursorInfluence > 0.5
          ? Color.lerp(particleColor, accentColor, (cursorInfluence - 0.5) * 2)!
          : particleColor;
      
      // Итоговый размер с учетом всех эффектов
      final finalSize = particle.size * pulseEffect * (1.0 + cursorInfluence * 1.5);
      
      // Рисуем частицу
      final paint = Paint()
        ..color = color.withOpacity(particle.opacity * pulseEffect)
        ..style = PaintingStyle.fill;
      
      // Для более крупных частиц добавляем свечение
      if (finalSize > 2.5 || cursorInfluence > 0.3) {
        // Свечение
        final glowPaint = Paint()
          ..color = color.withOpacity(particle.opacity * 0.3)
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, (finalSize * 2).clamp(1.0, 10.0));
        
        canvas.drawCircle(Offset(x, y), finalSize * 2, glowPaint);
      }
      
      // Рисуем основную частицу
      canvas.drawCircle(Offset(x, y), finalSize, paint);
    }
    
    // Добавляем тонкую линию акцента (в стиле минимализма)
    if (screenSize.width > 0) {
      final linePaint = Paint()
        ..color = accentColor.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      
      // Горизонтальная линия на 1/3 от верха
      final lineY = size.height / 3;
      final lineProgress = (math.sin(animationValue * math.pi * 2) + 1) / 2; // 0-1
      
      // Линия, которая появляется и исчезает
      canvas.drawLine(
        Offset(size.width * 0.1, lineY),
        Offset(size.width * (0.1 + lineProgress * 0.8), lineY),
        linePaint,
      );
    }
  }
  
  @override
  bool shouldRepaint(MinimalistPS5Background oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.pointerPosition != pointerPosition ||
           oldDelegate.scrollOffset != scrollOffset;
  }
}

/// Класс для представления частицы в анимации
class Particle {
  final double x;        // Позиция по X (0-1)
  final double y;        // Позиция по Y (0-1)
  final double size;     // Размер частицы
  final double speed;    // Скорость движения
  final double directionAngle; // Направление движения
  final double parallaxFactor; // Фактор параллакса
  final double opacity;  // Прозрачность
  
  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.speed,
    required this.directionAngle,
    required this.parallaxFactor,
    required this.opacity,
  });
}
