import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

// Модель для хранения рыночных данных с расширенной аналитикой
class MarketSentimentModel {
  final DateTime timestamp;
  final double overallScore;
  final Map<String, SentimentMetric> metrics;
  final MarketTrend currentTrend;

  MarketSentimentModel({
    required this.timestamp,
    required this.overallScore,
    required this.metrics,
    required this.currentTrend,
  });

  // Преобразование в JSON для сохранения
  Map<String, dynamic> toJson() => {
    'timestamp': timestamp.toIso8601String(),
    'overallScore': overallScore,
    'metrics': metrics.map((key, value) => MapEntry(key, value.toJson())),
    'currentTrend': currentTrend.toString().split('.').last,
  };

  // Создание из JSON
  factory MarketSentimentModel.fromJson(Map<String, dynamic> json) {
    return MarketSentimentModel(
      timestamp: DateTime.parse(json['timestamp']),
      overallScore: json['overallScore'],
      metrics: (json['metrics'] as Map).map(
        (key, value) => MapEntry(key, SentimentMetric.fromJson(value))
      ),
      currentTrend: MarketTrend.values.firstWhere(
        (trend) => trend.toString().split('.').last == json['currentTrend']
      ),
    );
  }
}

// Детализированная метрика с историческим контекстом
class SentimentMetric {
  final double currentValue;
  final double historicalAverage;
  final double volatility;
  final MetricTrend trend;

  SentimentMetric({
    required this.currentValue,
    required this.historicalAverage,
    required this.volatility,
    required this.trend,
  });

  // Преобразование в JSON
  Map<String, dynamic> toJson() => {
    'currentValue': currentValue,
    'historicalAverage': historicalAverage,
    'volatility': volatility,
    'trend': trend.toString().split('.').last,
  };

  // Создание из JSON
  factory SentimentMetric.fromJson(Map<String, dynamic> json) {
    return SentimentMetric(
      currentValue: json['currentValue'],
      historicalAverage: json['historicalAverage'],
      volatility: json['volatility'],
      trend: MetricTrend.values.firstWhere(
        (trend) => trend.toString().split('.').last == json['trend']
      ),
    );
  }
}

// Перечисление трендов
enum MarketTrend { bullish, bearish, neutral }
enum MetricTrend { increasing, decreasing, stable }

// Сервис для более точного анализа рыночных данных
class AdvancedMarketDataService {
  // Константы для более стабильной оценки
  static const Map<String, double> _metricWeights = {
    'marketCap': 0.25,
    'tradingVolume': 0.2,
    'priceMovement': 0.15,
    'volatilityIndex': 0.1,
    'networkActivity': 0.1,
    'liquidityRatio': 0.1,
    'macroEconomicIndicators': 0.1,
  };

  // Кэширование исторических данных для более точного анализа
  final Map<String, List<double>> _historicalData = {};

  // Получение комплексного анализа рыночных данных
  Future<MarketSentimentModel> fetchAdvancedMarketSentiment() async {
    try {
      // Сбор и анализ детальных метрик
      final metrics = await _collectAndAnalyzeMetrics();

      // Расчет общего индекса с учетом исторического контекста
      final overallScore = _calculateComprehensiveScore(metrics);

      // Определение общего тренда рынка
      final currentTrend = _determinMarketTrend(metrics);

      // Создание модели с детальным анализом
      final sentimentModel = MarketSentimentModel(
        timestamp: DateTime.now(),
        overallScore: overallScore,
        metrics: metrics,
        currentTrend: currentTrend,
      );

      // Кэширование результатов
      _cacheMarketData(sentimentModel);

      return sentimentModel;
    } catch (e) {
      print('Ошибка при получении расширенных данных: $e');
      return _retrieveCachedOrFallbackData();
    }
  }

  // Сбор и глубокий анализ метрик
  Future<Map<String, SentimentMetric>> _collectAndAnalyzeMetrics() async {
    final metrics = <String, SentimentMetric>{};

    // Параллельный сбор метрик для повышения эффективности
    final metricFutures = {
      'marketCap': _fetchMarketCapMetric(),
      'tradingVolume': _fetchTradingVolumeMetric(),
      'priceMovement': _fetchPriceMovementMetric(),
      'volatilityIndex': _fetchVolatilityMetric(),
      'networkActivity': _fetchNetworkActivityMetric(),
      'liquidityRatio': _fetchLiquidityRatioMetric(),
      'macroEconomicIndicators': _fetchMacroEconomicIndicators(),
    };

    // Ожидание всех результатов
    final results = await Future.wait(metricFutures.values);

    // Присваивание результатов с анализом тренда и волатильности
    metricFutures.keys.toList().asMap().forEach((index, key) {
      metrics[key] = _analyzeMetricTrend(key, results[index]);
    });

    return metrics;
  }

  // Анализ тренда и волатильности для каждой метрики
  SentimentMetric _analyzeMetricTrend(String metricName, double currentValue) {
    // Обновление исторических данных
    _historicalData[metricName] ??= [];
    _historicalData[metricName]!.add(currentValue);

    // Ограничение размера исторических данных
    if (_historicalData[metricName]!.length > 30) {
      _historicalData[metricName]!.removeAt(0);
    }

    // Расчет исторического среднего
    final historicalAverage = _historicalData[metricName]!.isNotEmpty
      ? _historicalData[metricName]!.reduce((a, b) => a + b) / _historicalData[metricName]!.length
      : currentValue;

    // Расчет волатильности
    final volatility = _calculateVolatility(_historicalData[metricName]!);

    // Определение тренда метрики
    MetricTrend trend;
    if (currentValue > historicalAverage * 1.05) {
      trend = MetricTrend.increasing;
    } else if (currentValue < historicalAverage * 0.95) {
      trend = MetricTrend.decreasing;
    } else {
      trend = MetricTrend.stable;
    }

    return SentimentMetric(
      currentValue: currentValue,
      historicalAverage: historicalAverage,
      volatility: volatility,
      trend: trend,
    );
  }

  // Расчет волатильности
  double _calculateVolatility(List<double> data) {
    if (data.length < 2) return 0.0;

    final mean = data.reduce((a, b) => a + b) / data.length;
    final variance = data.map((x) => (x - mean) * (x - mean)).reduce((a, b) => a + b) / data.length;
    return (variance > 0) ? sqrt(variance) : 0.0;
  }

  // Расчет комплексного индекса с учетом весов и тренда
  double _calculateComprehensiveScore(Map<String, SentimentMetric> metrics) {
    double score = 0.0;

    metrics.forEach((key, metric) {
      double metricScore = metric.currentValue;

      // Корректировка в зависимости от тренда
      switch (metric.trend) {
        case MetricTrend.increasing:
          metricScore *= 1.1;
          break;
        case MetricTrend.decreasing:
          metricScore *= 0.9;
          break;
        case MetricTrend.stable:
          metricScore *= 1.0;
          break;
      }

      // Применение весов
      score += metricScore * (_metricWeights[key] ?? 0.1);
    });

    return score.clamp(0.0, 100.0);
  }

  // Определение общего тренда рынка
  MarketTrend _determinMarketTrend(Map<String, SentimentMetric> metrics) {
    int bulishIndicators = 0;
    int bearishIndicators = 0;

    metrics.forEach((key, metric) {
      switch (metric.trend) {
        case MetricTrend.increasing:
          bulishIndicators++;
          break;
        case MetricTrend.decreasing:
          bearishIndicators++;
          break;
        case MetricTrend.stable:
          // Нейтральные показатели не влияют
          break;
      }
    });

    if (bulishIndicators > bearishIndicators * 1.5) {
      return MarketTrend.bullish;
    } else if (bearishIndicators > bulishIndicators * 1.5) {
      return MarketTrend.bearish;
    }
    return MarketTrend.neutral;
  }

  // Методы получения данных (заглушки - требуют реальной имплементации)
  Future<double> _fetchMarketCapMetric() async {
    // Реальная имплементация с получением данных через API
    return 50.0;
  }

  Future<double> _fetchTradingVolumeMetric() async {
    // Реальная имплементация с получением данных через API
    return 50.0;
  }

  Future<double> _fetchPriceMovementMetric() async {
    // Реальная имплементация с получением данных через API
    return 50.0;
  }

  Future<double> _fetchVolatilityMetric() async {
    // Реальная имплементация с получением данных через API
    return 50.0;
  }

  Future<double> _fetchNetworkActivityMetric() async {
    // Реальная имплементация с получением данных через API
    return 50.0;
  }

  Future<double> _fetchLiquidityRatioMetric() async {
    // Реальная имплементация с получением данных через API
    return 50.0;
  }

  Future<double> _fetchMacroEconomicIndicators() async {
    // Реальная имплементация с получением данных через API
    return 50.0;
  }

  // Кэширование данных
  void _cacheMarketData(MarketSentimentModel data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('advancedMarketSentimentData', jsonEncode(data.toJson()));
  }

  // Извлечение кэшированных данных
  MarketSentimentModel _retrieveCachedOrFallbackData() {
    // Логика извлечения последних кэшированных данных
    // В реальном приложении - более сложная логика
    return MarketSentimentModel(
      timestamp: DateTime.now(),
      overallScore: 50.0,
      metrics: {},
      currentTrend: MarketTrend.neutral,
    );
  }
}

// Точка входа в приложение
void main() {
  runApp(const MarketSentimentApp());
}

// Основное приложение
class MarketSentimentApp extends StatelessWidget {
  const MarketSentimentApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Расширенный рыночный анализ',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const MarketSentimentScreen(),
    );
  }
}

// Экран с рыночными данными
class MarketSentimentScreen extends StatefulWidget {
  const MarketSentimentScreen({super.key});

  @override
  State<MarketSentimentScreen> createState() => _MarketSentimentScreenState();
}

class _MarketSentimentScreenState extends State<MarketSentimentScreen> {
  final AdvancedMarketDataService _marketDataService = AdvancedMarketDataService();
  MarketSentimentModel? _sentimentData;
  Timer? _dataRefreshTimer;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _startPeriodicRefresh();
  }

  void _loadInitialData() async {
    try {
      final data = await _marketDataService.fetchAdvancedMarketSentiment();
      setState(() {
        _sentimentData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar();
    }
  }

  void _startPeriodicRefresh() {
    // Обновление данных каждые 30 минут
    _dataRefreshTimer = Timer.periodic(const Duration(minutes: 30), (_) {
      _loadInitialData();
    });
  }

  void _showErrorSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Не удалось обновить данные. Показаны последние доступные.'),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void dispose() {
    _dataRefreshTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Расширенный рыночный анализ'),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
                        : _sentimentData == null
              ? const Center(child: Text('Не удалось загрузить данные'))
              : _buildSentimentView(),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadInitialData,
        child: const Icon(Icons.refresh),
      ),
    );
  }

  Widget _buildSentimentView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Общий индикатор настроения
          _buildOverallSentimentCard(),

          const SizedBox(height: 16),

          // Детальные метрики
          _buildDetailedMetricsSection(),

          const SizedBox(height: 16),

          // Тренд рынка
          _buildMarketTrendCard(),

          const SizedBox(height: 16),

          // Временная метка последнего обновления
          _buildUpdateTimestampCard(),
        ],
      ),
    );
  }

  Widget _buildOverallSentimentCard() {
    final score = _sentimentData!.overallScore;
    Color scoreColor = _getScoreColor(score);

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'Комплексный индекс рынка',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 10),
            Text(
              score.toStringAsFixed(2),
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: scoreColor,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              _getSentimentDescription(score),
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedMetricsSection() {
    final metrics = _sentimentData!.metrics;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Детальные индикаторы',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 10),
            ...metrics.entries.map((entry) => _buildMetricRow(
              entry.key,
              entry.value
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String metricName, SentimentMetric metric) {
    final Color metricColor = _getScoreColor(metric.currentValue);
    final Color trendColor = _getTrendColor(metric.trend);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              _getMetricLabel(metricName),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              metric.currentValue.toStringAsFixed(2),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: metricColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(
                  _getTrendIcon(metric.trend),
                  color: trendColor,
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  _getTrendLabel(metric.trend),
                  style: TextStyle(
                    color: trendColor,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMarketTrendCard() {
    final trend = _sentimentData!.currentTrend;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'Текущий тренд рынка',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 10),
            Icon(
              _getMarketTrendIcon(trend),
              color: _getMarketTrendColor(trend),
              size: 48,
            ),
            const SizedBox(height: 10),
            Text(
              _getMarketTrendLabel(trend),
              style: TextStyle(
                fontSize: 20,
                color: _getMarketTrendColor(trend),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateTimestampCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.update, size: 20),
            const SizedBox(width: 8),
            Text(
              'Последнее обновление: ${_formatDateTime(_sentimentData!.timestamp)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  // Вспомогательные методы визуализации
  Color _getScoreColor(double score) {
    if (score < 20) return Colors.red.shade900;
    if (score < 40) return Colors.red;
    if (score < 50) return Colors.orange;
    if (score < 60) return Colors.yellow.shade700;
    if (score < 70) return Colors.lightGreen;
    if (score < 85) return Colors.green;
    return Colors.green.shade700;
  }

  Color _getTrendColor(MetricTrend trend) {
    switch (trend) {
      case MetricTrend.increasing:
        return Colors.green;
      case MetricTrend.decreasing:
        return Colors.red;
      case MetricTrend.stable:
        return Colors.grey;
    }
  }

  IconData _getTrendIcon(MetricTrend trend) {
    switch (trend) {
      case MetricTrend.increasing:
        return Icons.trending_up;
      case MetricTrend.decreasing:
        return Icons.trending_down;
      case MetricTrend.stable:
        return Icons.trending_flat;
    }
  }

  Color _getMarketTrendColor(MarketTrend trend) {
    switch (trend) {
      case MarketTrend.bullish:
        return Colors.green;
      case MarketTrend.bearish:
        return Colors.red;
      case MarketTrend.neutral:
        return Colors.grey;
    }
  }

  IconData _getMarketTrendIcon(MarketTrend trend) {
    switch (trend) {
      case MarketTrend.bullish:
        return Icons.arrow_upward;
      case MarketTrend.bearish:
        return Icons.arrow_downward;
      case MarketTrend.neutral:
        return Icons.swap_horiz;
    }
  }

  String _getSentimentDescription(double score) {
    if (score < 20) return 'Крайне негативный';
    if (score < 40) return 'Негативный';
    if (score < 50) return 'Умеренно негативный';
    if (score < 60) return 'Нейтральный';
    if (score < 70) return 'Умеренно позитивный';
    if (score < 85) return 'Позитивный';
    return 'Крайне позитивный';
  }

  String _getTrendLabel(MetricTrend trend) {
    switch (trend) {
      case MetricTrend.increasing:
        return 'Рост';
      case MetricTrend.decreasing:
        return 'Спад';
      case MetricTrend.stable:
        return 'Стабильно';
    }
  }

  String _getMarketTrendLabel(MarketTrend trend) {
    switch (trend) {
      case MarketTrend.bullish:
        return 'Растущий';
      case MarketTrend.bearish:
        return 'Падающий';
      case MarketTrend.neutral:
        return 'Нейтральный';
    }
  }

  String _getMetricLabel(String metricName) {
    final Map<String, String> labels = {
      'marketCap': 'Рыночная капитализация',
      'tradingVolume': 'Объем торгов',
      'priceMovement': 'Движение цены',
      'volatilityIndex': 'Индекс волатильности',
      'networkActivity': 'Сетевая активность',
      'liquidityRatio': 'Коэффициент ликвидности',
      'macroEconomicIndicators': 'Макроэкономические показатели',
    };
    return labels[metricName] ?? metricName;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}.${dateTime.month.toString().padLeft(2, '0')}.${dateTime.year} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
